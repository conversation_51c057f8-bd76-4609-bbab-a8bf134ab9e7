-- BPO Talent Training Platform Database Schema
-- This SQL file contains the complete database schema for the application

-- Enable UUID extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User role enum type
CREATE TYPE user_role AS ENUM ('admin', 'bpo_admin', 'bpo_team', 'prospect');

-- User status enum type
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'pending_activation', 'suspended');

-- Job type enum type
CREATE TYPE job_type AS ENUM ('full_time', 'part_time', 'contract', 'remote');

-- Application status enum type
CREATE TYPE application_status AS ENUM ('submitted', 'reviewing', 'interview_scheduled', 'rejected', 'accepted');

-- Interview status enum type
CREATE TYPE interview_status AS ENUM ('scheduled', 'completed', 'cancelled', 'no_show');

-- Activity type enum type
CREATE TYPE activity_type AS ENUM ('video', 'reading', 'quiz', 'scenario', 'roleplay');

-- Progress status enum type
CREATE TYPE progress_status AS ENUM ('not_started', 'in_progress', 'completed', 'failed');

-- Content status enum type
CREATE TYPE content_status AS ENUM ('draft', 'published', 'archived');

-- Training status enum type
CREATE TYPE training_status AS ENUM ('not_started', 'in_progress', 'completed');

-- BPO team role enum type
CREATE TYPE bpo_team_role AS ENUM ('admin', 'recruiter', 'interviewer');

-- Users Table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    encrypted_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role user_role NOT NULL,
    status user_status DEFAULT 'pending_activation',
    is_password_change_required BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_users_timestamp
BEFORE UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();
