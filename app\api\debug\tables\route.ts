import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    // Get a sample from job_postings
    const { data: jobPostings, error: jobPostingsError } = await supabase
      .from('job_postings')
      .select('*')
      .limit(1);

    if (jobPostingsError) {
      return NextResponse.json(
        { error: "Error fetching job_postings", details: jobPostingsError }, 
        { status: 500 }
      );
    }

    // Try to create a simple job posting
    const testVacancy = {
      title: "Test Job",
      description: "Test description",
      requirements: "Test requirements",
      job_type: "full_time",
      bpo_id: "00000000-0000-0000-0000-000000000000", // This should be a valid UUID in your database
      status: "draft",
      created_at: new Date().toISOString()
    };

    const { data: insertTest, error: insertError } = await supabase
      .from('job_postings')
      .insert(testVacancy)
      .select();

    return NextResponse.json({
      jobPostings: {
        sample: jobPostings,
        schema: jobPostings && jobPostings.length > 0 
          ? Object.keys(jobPostings[0]).map(key => ({
              name: key,
              type: typeof jobPostings[0][key],
              value: jobPostings[0][key]
            }))
          : [],
      },
      insertTest: {
        data: insertTest,
        error: insertError
      }
    });
  } catch (error) {
    return NextResponse.json({ error: String(error) }, { status: 500 });
  }
} 