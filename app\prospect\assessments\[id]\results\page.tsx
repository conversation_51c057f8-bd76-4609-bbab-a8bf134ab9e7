import React from 'react';
import { ErrorBoundary } from '@/components/error-boundary';
import { AssessmentResults } from './assessment-results';

// This is a server component that can properly handle params
export default function AssessmentResultsPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params promise with React.use()
  const resolvedParams = React.use(params);
  const id = resolvedParams.id;
  
  return (
    <ErrorBoundary>
      <AssessmentResults assessmentId={id} />
    </ErrorBoundary>
  );
} 