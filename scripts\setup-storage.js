const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupStorage() {
  console.log('🚀 Setting up Supabase Storage...\n');

  try {
    // Check if files bucket exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();

    if (listError) {
      console.error('❌ Error listing buckets:', listError.message);
      return;
    }

    const filesBucket = buckets.find(bucket => bucket.name === 'files');

    if (filesBucket) {
      console.log('✅ files bucket already exists');
    } else {
      console.log('📝 Creating files bucket...');

      const { data: createData, error: createError } = await supabase.storage.createBucket('files', {
        public: true,
        allowedMimeTypes: ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'],
        fileSizeLimit: 5242880 // 5MB
      });

      if (createError) {
        console.error('❌ Error creating bucket:', createError.message);
        return;
      }

      console.log('✅ files bucket created successfully');
    }

    // Test upload permissions
    console.log('\n🔍 Testing upload permissions...');
    
    const testFile = new Blob(['test content'], { type: 'text/plain' });
    const testFileName = `test-${Date.now()}.txt`;
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('files')
      .upload(testFileName, testFile);

    if (uploadError) {
      console.error('❌ Upload test failed:', uploadError.message);
      console.log('💡 You may need to set up RLS policies for the storage bucket');
    } else {
      console.log('✅ Upload test successful');
      
      // Clean up test file
      await supabase.storage.from('files').remove([testFileName]);
      console.log('🧹 Test file cleaned up');
    }

    console.log('\n📋 All available buckets:');
    buckets.forEach(bucket => {
      console.log(`  - ${bucket.name} (${bucket.public ? 'public' : 'private'})`);
    });

    console.log('\n📋 Storage setup complete!');
    console.log('📁 Bucket: files');
    console.log('🔒 Public access: enabled');
    console.log('📏 File size limit: 5MB');
    console.log('📄 Allowed types: PDF, JPG, PNG');

  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

// Run the setup
setupStorage();
