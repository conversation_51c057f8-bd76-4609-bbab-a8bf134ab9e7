import { SupabaseClient, User } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';
import { createApiClient, createServerClient } from '@/lib/supabase-server';
import { isPlatformAdmin, isBPOAdmin, getUserBPOMemberships } from '@/lib/auth-utils';
import type { Database } from '@/types/database.types';

// =============================================================================
// AUTHENTICATION TYPES
// =============================================================================

export interface AuthUser {
  id: string;
  email: string;
  role: 'admin' | 'bpo_admin' | 'bpo_team' | 'prospect';
  full_name?: string;
  avatar_url?: string;
  isAdmin: boolean;
  isBPOAdmin: boolean;
  bpoMemberships: Array<{ bpo_id: string; role: string }>;
}

export interface AuthResult {
  user: AuthUser | null;
  error: string | null;
  status: number;
}

// =============================================================================
// SECURE AUTHENTICATION FUNCTIONS
// =============================================================================

/**
 * Get authenticated user with secure validation for API routes
 * Uses getUser() instead of getSession() for better security
 */
export async function getAuthenticatedUser(): Promise<AuthResult> {
  try {
    const supabase = await createApiClient();
    
    // Use getUser() for secure authentication (not getSession())
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return {
        user: null,
        error: 'Unauthorized - Authentication required',
        status: 401
      };
    }

    // Get user details from database
    const { data: userData, error: dbError } = await supabase
      .from('users')
      .select('id, email, role, full_name, avatar_url')
      .eq('id', user.id)
      .single();

    if (dbError || !userData) {
      return {
        user: null,
        error: 'User not found in database',
        status: 404
      };
    }

    // Check admin status using secure functions
    const isAdmin = await isPlatformAdmin(supabase, user.id);

    // Get BPO memberships only for users who might have them
    let memberships: Array<{ bpo_id: string; role: string }> = [];
    let isBPOAdminUser = false;

    // Only check BPO memberships for non-prospect users
    if (userData.role !== 'prospect') {
      const membershipResult = await getUserBPOMemberships(supabase, user.id);
      memberships = membershipResult.memberships;
      isBPOAdminUser = memberships.some(m => m.role === 'admin');
    }

    const authUser: AuthUser = {
      id: userData.id,
      email: userData.email,
      role: userData.role,
      full_name: userData.full_name,
      avatar_url: userData.avatar_url,
      isAdmin,
      isBPOAdmin: isBPOAdminUser,
      bpoMemberships: memberships
    };

    return {
      user: authUser,
      error: null,
      status: 200
    };

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      user: null,
      error: 'Authentication failed',
      status: 500
    };
  }
}

/**
 * Get authenticated user for server components
 */
export async function getServerAuthUser(): Promise<AuthResult> {
  try {
    const supabase = await createServerClient();
    
    // Use getUser() for secure authentication
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return {
        user: null,
        error: 'Unauthorized - Authentication required',
        status: 401
      };
    }

    // Get user details from database
    const { data: userData, error: dbError } = await supabase
      .from('users')
      .select('id, email, role, full_name, avatar_url')
      .eq('id', user.id)
      .single();

    if (dbError || !userData) {
      return {
        user: null,
        error: 'User not found in database',
        status: 404
      };
    }

    // Check admin status using secure functions
    const isAdmin = await isPlatformAdmin(supabase, user.id);

    // Get BPO memberships only for users who might have them
    let memberships: Array<{ bpo_id: string; role: string }> = [];
    let isBPOAdminUser = false;

    // Only check BPO memberships for non-prospect users
    if (userData.role !== 'prospect') {
      const membershipResult = await getUserBPOMemberships(supabase, user.id);
      memberships = membershipResult.memberships;
      isBPOAdminUser = memberships.some(m => m.role === 'admin');
    }

    const authUser: AuthUser = {
      id: userData.id,
      email: userData.email,
      role: userData.role,
      full_name: userData.full_name,
      avatar_url: userData.avatar_url,
      isAdmin,
      isBPOAdmin: isBPOAdminUser,
      bpoMemberships: memberships
    };

    return {
      user: authUser,
      error: null,
      status: 200
    };

  } catch (error) {
    console.error('Server authentication error:', error);
    return {
      user: null,
      error: 'Authentication failed',
      status: 500
    };
  }
}

// =============================================================================
// ROLE-BASED ACCESS CONTROL
// =============================================================================

/**
 * Require platform admin access
 */
export async function requirePlatformAdmin(): Promise<AuthResult> {
  const authResult = await getAuthenticatedUser();
  
  if (!authResult.user) {
    return authResult;
  }

  if (!authResult.user.isAdmin) {
    return {
      user: null,
      error: 'Forbidden - Platform admin access required',
      status: 403
    };
  }

  return authResult;
}

/**
 * Require BPO admin access for a specific BPO
 */
export async function requireBPOAdmin(bpoId: string): Promise<AuthResult> {
  const authResult = await getAuthenticatedUser();
  
  if (!authResult.user) {
    return authResult;
  }

  // Platform admins can access any BPO
  if (authResult.user.isAdmin) {
    return authResult;
  }

  // Check if user is admin of the specific BPO
  const isBPOAdminForThisBPO = authResult.user.bpoMemberships.some(
    m => m.bpo_id === bpoId && m.role === 'admin'
  );

  if (!isBPOAdminForThisBPO) {
    return {
      user: null,
      error: 'Forbidden - BPO admin access required',
      status: 403
    };
  }

  return authResult;
}

/**
 * Require BPO team member access for a specific BPO
 */
export async function requireBPOTeamMember(bpoId: string): Promise<AuthResult> {
  const authResult = await getAuthenticatedUser();
  
  if (!authResult.user) {
    return authResult;
  }

  // Platform admins can access any BPO
  if (authResult.user.isAdmin) {
    return authResult;
  }

  // Check if user is member of the specific BPO
  const isBPOTeamMember = authResult.user.bpoMemberships.some(
    m => m.bpo_id === bpoId
  );

  if (!isBPOTeamMember) {
    return {
      user: null,
      error: 'Forbidden - BPO team access required',
      status: 403
    };
  }

  return authResult;
}

// =============================================================================
// RESPONSE HELPERS
// =============================================================================

/**
 * Create standardized error response
 */
export function createAuthErrorResponse(authResult: AuthResult): NextResponse {
  return NextResponse.json(
    { error: authResult.error },
    { status: authResult.status }
  );
}

/**
 * Handle authentication in API routes with consistent error responses
 */
export async function withAuth<T>(
  handler: (user: AuthUser) => Promise<T>,
  requireAdmin = false
): Promise<NextResponse> {
  try {
    const authResult = requireAdmin 
      ? await requirePlatformAdmin()
      : await getAuthenticatedUser();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const result = await handler(authResult.user);
    return NextResponse.json(result);

  } catch (error) {
    console.error('API handler error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
