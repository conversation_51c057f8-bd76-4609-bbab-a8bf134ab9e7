#!/usr/bin/env node

/**
 * Error Logging Script for BPO Training Platform
 * 
 * Usage: node scripts/log-error.js
 * 
 * This script helps quickly document errors in the error database
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

function generateErrorId() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  
  // Get next error number for today
  const errorDbPath = path.join(__dirname, '..', 'docs', 'error-log', 'error-database.md');
  const content = fs.readFileSync(errorDbPath, 'utf8');
  const todayPattern = new RegExp(`ERR-${year}-${month}-${day}-(\\d{3})`, 'g');
  const matches = [...content.matchAll(todayPattern)];
  const nextNumber = matches.length + 1;
  
  return `ERR-${year}-${month}-${day}-${String(nextNumber).padStart(3, '0')}`;
}

function formatErrorEntry(data) {
  return `
## ${data.id}: ${data.summary}

### 📋 Summary
${data.summary}

### 🚨 Symptoms
${data.symptoms.split('\n').map(line => line.trim() ? `- ${line.trim()}` : '').filter(Boolean).join('\n')}

### 🔍 Root Cause
${data.rootCause}

### ✅ Solution
${data.solution}

### 🛡️ Prevention
${data.prevention}

### 📊 Impact
- **Severity**: ${data.severity}
- **Affected Features**: ${data.affectedFeatures}
- **Time to Resolution**: ${data.timeToResolution}
- **User Impact**: ${data.userImpact}

### 🏷️ Tags
${data.tags}

---
`;
}

async function main() {
  console.log('🚨 BPO Training Platform - Error Logger\n');
  
  try {
    const errorId = generateErrorId();
    console.log(`📝 Logging new error: ${errorId}\n`);
    
    const summary = await question('📋 Brief summary of the error: ');
    const symptoms = await question('🚨 Symptoms (what user experienced, separate with newlines): ');
    const rootCause = await question('🔍 Root cause (technical explanation): ');
    const solution = await question('✅ Solution (step-by-step fix): ');
    const prevention = await question('🛡️ Prevention (how to avoid in future): ');
    
    console.log('\n📊 Impact Assessment:');
    const severity = await question('Severity (Critical/High/Medium/Low): ');
    const affectedFeatures = await question('Affected features: ');
    const timeToResolution = await question('Time to resolution: ');
    const userImpact = await question('User impact: ');
    
    const tags = await question('🏷️ Tags (e.g., #api #supabase #import): ');
    
    const errorData = {
      id: errorId,
      summary,
      symptoms,
      rootCause,
      solution,
      prevention,
      severity,
      affectedFeatures,
      timeToResolution,
      userImpact,
      tags
    };
    
    // Add to error database
    const errorDbPath = path.join(__dirname, '..', 'docs', 'error-log', 'error-database.md');
    const errorEntry = formatErrorEntry(errorData);
    
    // Find the template section and insert before it
    let content = fs.readFileSync(errorDbPath, 'utf8');
    const templateIndex = content.indexOf('## Template for New Errors');
    
    if (templateIndex !== -1) {
      content = content.slice(0, templateIndex) + errorEntry + '\n' + content.slice(templateIndex);
    } else {
      content += errorEntry;
    }
    
    fs.writeFileSync(errorDbPath, content);
    
    console.log(`\n✅ Error ${errorId} logged successfully!`);
    console.log(`📁 Added to: docs/error-log/error-database.md`);
    
    // Ask if they want to add to a category file
    const addToCategory = await question('\n📂 Add to a category file? (y/n): ');
    if (addToCategory.toLowerCase() === 'y') {
      console.log('\nAvailable categories:');
      console.log('1. api-errors.md');
      console.log('2. database-errors.md');
      console.log('3. import-errors.md');
      console.log('4. ui-errors.md');
      console.log('5. deployment-errors.md');
      
      const categoryChoice = await question('Choose category (1-5): ');
      const categories = ['api-errors.md', 'database-errors.md', 'import-errors.md', 'ui-errors.md', 'deployment-errors.md'];
      const categoryFile = categories[parseInt(categoryChoice) - 1];
      
      if (categoryFile) {
        const categoryPath = path.join(__dirname, '..', 'docs', 'error-log', 'categories', categoryFile);
        if (fs.existsSync(categoryPath)) {
          fs.appendFileSync(categoryPath, errorEntry);
          console.log(`✅ Added to category: ${categoryFile}`);
        }
      }
    }
    
    console.log('\n🎯 Don\'t forget to:');
    console.log('- Update prevention-checklist.md if needed');
    console.log('- Share the solution with the team');
    console.log('- Commit the documentation changes');
    
  } catch (error) {
    console.error('❌ Error logging failed:', error.message);
  } finally {
    rl.close();
  }
}

main();
