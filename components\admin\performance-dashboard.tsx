/**
 * Performance Monitoring Dashboard
 * Real-time performance metrics and optimization insights
 */

"use client"

import * as React from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Activity, 
  Zap, 
  Database, 
  Image, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  RefreshCw,
  AlertTriangle,
  CheckCircle
} from "lucide-react"
import { performanceMonitor } from "@/lib/performance-monitor"
import { getAllCacheStats } from "@/lib/cache-manager"
import { usePerformanceSummary, useMemoryMonitor } from "@/hooks/use-performance"

// =============================================================================
// TYPES
// =============================================================================

interface PerformanceMetric {
  name: string
  value: number
  unit: string
  status: 'good' | 'warning' | 'critical'
  trend?: 'up' | 'down' | 'stable'
}

interface CacheMetrics {
  hitRate: number
  totalSize: number
  totalEntries: number
  byType: Record<string, {
    hits: number
    misses: number
    size: number
    hitRate: number
  }>
}

// =============================================================================
// PERFORMANCE DASHBOARD COMPONENT
// =============================================================================

export function PerformanceDashboard() {
  const [metrics, setMetrics] = React.useState<PerformanceMetric[]>([])
  const [cacheMetrics, setCacheMetrics] = React.useState<CacheMetrics | null>(null)
  const [isRefreshing, setIsRefreshing] = React.useState(false)
  
  const performanceSummary = usePerformanceSummary()
  const memoryInfo = useMemoryMonitor()

  // Refresh metrics
  const refreshMetrics = React.useCallback(async () => {
    setIsRefreshing(true)
    
    try {
      // Get performance summary
      const summary = performanceMonitor.getSummary()
      
      // Get cache statistics
      const cacheStats = getAllCacheStats()
      
      // Calculate overall cache metrics
      const totalHits = Object.values(cacheStats).reduce((sum, cache) => sum + cache.hits, 0)
      const totalMisses = Object.values(cacheStats).reduce((sum, cache) => sum + cache.misses, 0)
      const totalRequests = totalHits + totalMisses
      const overallHitRate = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0
      
      setCacheMetrics({
        hitRate: overallHitRate,
        totalSize: Object.values(cacheStats).reduce((sum, cache) => sum + cache.size, 0),
        totalEntries: Object.values(cacheStats).reduce((sum, cache) => sum + cache.size, 0),
        byType: cacheStats
      })
      
      // Create performance metrics array
      const newMetrics: PerformanceMetric[] = [
        {
          name: 'Web Vitals Score',
          value: summary.webVitalsScore,
          unit: '%',
          status: summary.webVitalsScore >= 80 ? 'good' : summary.webVitalsScore >= 60 ? 'warning' : 'critical'
        },
        {
          name: 'Cache Hit Rate',
          value: overallHitRate,
          unit: '%',
          status: overallHitRate >= 80 ? 'good' : overallHitRate >= 60 ? 'warning' : 'critical'
        },
        {
          name: 'Session Duration',
          value: Math.round(summary.sessionDuration / 1000 / 60),
          unit: 'min',
          status: 'good'
        },
        {
          name: 'Total Metrics',
          value: summary.totalMetrics,
          unit: 'count',
          status: 'good'
        }
      ]
      
      if (memoryInfo) {
        newMetrics.push({
          name: 'Memory Usage',
          value: Math.round((memoryInfo.used / memoryInfo.limit) * 100),
          unit: '%',
          status: (memoryInfo.used / memoryInfo.limit) < 0.8 ? 'good' : 
                  (memoryInfo.used / memoryInfo.limit) < 0.9 ? 'warning' : 'critical'
        })
      }
      
      setMetrics(newMetrics)
    } catch (error) {
      console.error('Failed to refresh metrics:', error)
    } finally {
      setIsRefreshing(false)
    }
  }, [memoryInfo])

  // Auto-refresh metrics
  React.useEffect(() => {
    refreshMetrics()
    const interval = setInterval(refreshMetrics, 30000) // Every 30 seconds
    return () => clearInterval(interval)
  }, [refreshMetrics])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600 bg-green-50'
      case 'warning': return 'text-yellow-600 bg-yellow-50'
      case 'critical': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return CheckCircle
      case 'warning': return AlertTriangle
      case 'critical': return AlertTriangle
      default: return Activity
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Performance Dashboard</h2>
          <p className="text-muted-foreground">Real-time application performance metrics</p>
        </div>
        <Button 
          onClick={refreshMetrics} 
          disabled={isRefreshing}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric) => {
          const StatusIcon = getStatusIcon(metric.status)
          return (
            <Card key={metric.name} className="border-none shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <StatusIcon className={`h-4 w-4 ${getStatusColor(metric.status).split(' ')[0]}`} />
                    <span className="text-sm font-medium text-muted-foreground">
                      {metric.name}
                    </span>
                  </div>
                  <Badge className={getStatusColor(metric.status)}>
                    {metric.status}
                  </Badge>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold">
                    {metric.value}
                    <span className="text-sm font-normal text-muted-foreground ml-1">
                      {metric.unit}
                    </span>
                  </div>
                  {metric.name.includes('Rate') || metric.name.includes('Usage') ? (
                    <Progress 
                      value={metric.value} 
                      className="mt-2 h-1.5"
                    />
                  ) : null}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Detailed Metrics */}
      <Tabs defaultValue="cache" className="space-y-4">
        <TabsList>
          <TabsTrigger value="cache">Cache Performance</TabsTrigger>
          <TabsTrigger value="memory">Memory Usage</TabsTrigger>
          <TabsTrigger value="vitals">Web Vitals</TabsTrigger>
          <TabsTrigger value="optimization">Optimization Tips</TabsTrigger>
        </TabsList>

        {/* Cache Performance Tab */}
        <TabsContent value="cache" className="space-y-4">
          <Card className="border-none shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Cache Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              {cacheMetrics ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {Math.round(cacheMetrics.hitRate)}%
                      </div>
                      <div className="text-sm text-muted-foreground">Hit Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {cacheMetrics.totalEntries}
                      </div>
                      <div className="text-sm text-muted-foreground">Total Entries</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {cacheMetrics.totalSize}
                      </div>
                      <div className="text-sm text-muted-foreground">Cache Size</div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    {Object.entries(cacheMetrics.byType).map(([type, stats]) => (
                      <div key={type} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                        <div>
                          <div className="font-medium capitalize">{type} Cache</div>
                          <div className="text-sm text-muted-foreground">
                            {stats.hits} hits, {stats.misses} misses
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold">{Math.round(stats.hitRate)}%</div>
                          <div className="text-sm text-muted-foreground">hit rate</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Loading cache metrics...
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Memory Usage Tab */}
        <TabsContent value="memory" className="space-y-4">
          <Card className="border-none shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Memory Usage
              </CardTitle>
            </CardHeader>
            <CardContent>
              {memoryInfo ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {Math.round(memoryInfo.used / 1024 / 1024)}MB
                      </div>
                      <div className="text-sm text-muted-foreground">Used</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {Math.round(memoryInfo.total / 1024 / 1024)}MB
                      </div>
                      <div className="text-sm text-muted-foreground">Total</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {Math.round(memoryInfo.limit / 1024 / 1024)}MB
                      </div>
                      <div className="text-sm text-muted-foreground">Limit</div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Memory Usage</span>
                      <span>{Math.round((memoryInfo.used / memoryInfo.limit) * 100)}%</span>
                    </div>
                    <Progress 
                      value={(memoryInfo.used / memoryInfo.limit) * 100} 
                      className="h-2"
                    />
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Memory monitoring not available in this browser
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Web Vitals Tab */}
        <TabsContent value="vitals" className="space-y-4">
          <Card className="border-none shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Web Vitals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Web Vitals data will appear here as users interact with the application
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Optimization Tips Tab */}
        <TabsContent value="optimization" className="space-y-4">
          <Card className="border-none shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Optimization Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-900 mb-2">✅ Implemented Optimizations</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Multi-layer caching with TTL and invalidation</li>
                    <li>• Lazy loading for components and images</li>
                    <li>• Debounced search and operations</li>
                    <li>• Memoized computations and callbacks</li>
                    <li>• Bundle optimization and code splitting</li>
                    <li>• Performance monitoring and analytics</li>
                  </ul>
                </div>
                
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <h4 className="font-medium text-green-900 mb-2">🚀 Additional Recommendations</h4>
                  <ul className="text-sm text-green-800 space-y-1">
                    <li>• Enable service worker for offline caching</li>
                    <li>• Implement virtual scrolling for large lists</li>
                    <li>• Use WebP images with fallbacks</li>
                    <li>• Preload critical resources</li>
                    <li>• Optimize database queries with indexes</li>
                    <li>• Implement CDN for static assets</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
