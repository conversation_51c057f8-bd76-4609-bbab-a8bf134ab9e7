"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { Loader2, Check<PERSON>ircle, Trophy, Star } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import confetti from 'canvas-confetti'

interface LessonCompleteButtonProps {
  lessonId: string
  moduleId: string
}

export function LessonCompleteButton({ lessonId, moduleId }: LessonCompleteButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isCompleted, setIsCompleted] = useState(false)
  const router = useRouter()
  const { toast } = useToast()
  const buttonRef = useRef<HTMLButtonElement>(null)
  
  const triggerConfetti = () => {
    if (typeof window !== "undefined" && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect()
      const x = (rect.left + rect.width / 2) / window.innerWidth
      const y = (rect.top + rect.height / 2) / window.innerHeight
      
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { x, y },
        gravity: 0.8,
        colors: ['#4F46E5', '#3B82F6', '#10B981', '#34D399', '#F59E0B']
      })
    }
  }

  const handleCompleteLesson = async () => {
    setIsLoading(true)
    
    try {
      const response = await fetch('/api/training/complete-lesson', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ lessonId }),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        console.error('Error response:', data)
        throw new Error(data.error || 'Failed to complete lesson')
      }
      
      // Show success animation
      setIsCompleted(true)
      triggerConfetti()
      
      // Display toast notification
      toast({
        title: "Lesson completed!",
        description: "You've successfully finished this lesson. Great work!",
      })
      
      // Refresh the page data
      router.refresh()
      
      // Redirect back to module page after a delay
      setTimeout(() => {
        router.push(`/prospect/training/${moduleId}`)
      }, 2500)
      
    } catch (error: any) {
      console.error('Error completing lesson:', error)
      toast({
        title: "Error",
        description: error.message || "Something went wrong",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }
  
  return (
    <Button 
      ref={buttonRef}
      className={`relative overflow-hidden transition-all duration-300 ${
        isCompleted
          ? "bg-green-600 hover:bg-green-700"
          : "bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
      }`}
      onClick={handleCompleteLesson}
      disabled={isLoading || isCompleted}
      size="lg"
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Processing...
        </>
      ) : isCompleted ? (
        <>
          <CheckCircle className="mr-2 h-4 w-4" />
          Lesson Completed!
          <div className="absolute inset-0 flex justify-center items-center pointer-events-none">
            <div className="animate-ping absolute h-10 w-10 rounded-full bg-green-400 opacity-20"></div>
            <div className="relative h-5 w-5">
              <Star className="h-5 w-5 text-yellow-300 animate-pulse" fill="currentColor" />
            </div>
          </div>
        </>
      ) : (
        <>
          <CheckCircle className="mr-2 h-4 w-4" />
          Mark Lesson as Complete
        </>
      )}
    </Button>
  )
} 