'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';

export default function Home() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // This is just a fallback in case middleware redirection doesn't work
    // It will redirect based on user role
    const checkUserAndRedirect = async () => {
      try {
        console.log('Checking user session...');
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Session error:', sessionError);
          setError(`Session error: ${sessionError.message}`);
          setLoading(false);
          return;
        }

        if (session) {
          console.log('User is logged in:', session.user.email);
          // Get user role from the database
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('role')
            .eq('id', session.user.id)
            .single();

          if (userError) {
            console.error('User data error:', userError);
            setError(`User data error: ${userError.message}`);
            setLoading(false);
            return;
          }

          if (userData) {
            console.log('User role:', userData.role);
            if (userData.role === 'admin') {
              router.push('/admin');
            } else if (userData.role === 'bpo_admin' || userData.role === 'bpo_team') {
              router.push('/bpo');
            } else if (userData.role === 'prospect') {
              router.push('/prospect');
            }
          }
        } else {
          console.log('No user session, redirecting to login');
          router.push('/login');
        }
      } catch (err) {
        console.error('Unexpected error:', err);
        setError(`Unexpected error: ${err}`);
        setLoading(false);
      }
    };

    checkUserAndRedirect();
  }, [router]);

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center max-w-md">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-medium text-red-600 mb-2">Connection Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <h2 className="mt-4 text-xl font-medium">Loading your dashboard...</h2>
          <p className="mt-2 text-gray-500">Please wait while we redirect you to the appropriate dashboard.</p>
        </div>
      </div>
    );
  }

  return null;
}
