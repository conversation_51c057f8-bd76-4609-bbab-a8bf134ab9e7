# 🤝 Development Workflow

Comprehensive guide for contributing to the BPO Training Platform, including development setup, coding standards, and collaboration processes.

## 🎯 Getting Started

### Prerequisites

Before contributing, ensure you have:

- **Node.js 18+** installed
- **Git** configured with your GitHub account
- **VS Code** (recommended) with suggested extensions
- **Basic knowledge** of React, TypeScript, and Next.js

### Initial Setup

```bash
# 1. Fork the repository on GitHub
# 2. Clone your fork
git clone https://github.com/your-username/bpo-training-platform.git
cd bpo-training-platform

# 3. Add upstream remote
git remote add upstream https://github.com/original-repo/bpo-training-platform.git

# 4. Install dependencies
npm install

# 5. Copy environment file
cp .env.example .env.local

# 6. Set up your development environment
# Follow the Environment Setup guide

# 7. Start development server
npm run dev
```

## 🌿 Branching Strategy

### Branch Types

```
main
├── develop                    # Integration branch
├── feature/feature-name       # New features
├── bugfix/bug-description     # Bug fixes
├── hotfix/critical-fix        # Critical production fixes
├── release/v1.2.0            # Release preparation
└── docs/documentation-update  # Documentation updates
```

### Branch Naming Convention

```bash
# Feature branches
feature/training-module-editor
feature/job-application-workflow
feature/admin-analytics-dashboard

# Bug fix branches
bugfix/login-validation-error
bugfix/progress-tracking-issue
bugfix/file-upload-timeout

# Hotfix branches
hotfix/security-vulnerability
hotfix/database-connection-issue

# Documentation branches
docs/api-documentation-update
docs/deployment-guide-revision
```

### Creating a New Branch

```bash
# 1. Ensure you're on the latest develop branch
git checkout develop
git pull upstream develop

# 2. Create and switch to new branch
git checkout -b feature/your-feature-name

# 3. Push branch to your fork
git push -u origin feature/your-feature-name
```

## 💻 Development Process

### 1. Planning Phase

Before starting development:

1. **Check existing issues** on GitHub
2. **Create or comment on an issue** describing your planned changes
3. **Get approval** from maintainers for significant changes
4. **Break down work** into smaller, manageable tasks

### 2. Development Phase

```bash
# 1. Write code following our coding standards
# 2. Write tests for new functionality
npm run test:unit

# 3. Run linting and formatting
npm run lint
npm run format

# 4. Type check
npm run type-check

# 5. Test your changes thoroughly
npm run test
npm run test:e2e

# 6. Update documentation if needed
```

### 3. Code Quality Checks

```bash
# Run all quality checks before committing
npm run pre-commit

# This runs:
# - ESLint for code quality
# - Prettier for formatting
# - TypeScript compiler
# - Unit tests
# - Security audit
```

### 4. Commit Guidelines

#### Commit Message Format

```
<type>(<scope>): <subject>

<body>

<footer>
```

#### Commit Types

- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **test**: Adding or updating tests
- **chore**: Maintenance tasks

#### Examples

```bash
# Good commit messages
git commit -m "feat(auth): add multi-factor authentication support"
git commit -m "fix(training): resolve progress tracking calculation error"
git commit -m "docs(api): update authentication endpoint documentation"
git commit -m "test(components): add unit tests for Button component"

# Bad commit messages
git commit -m "fix stuff"
git commit -m "update code"
git commit -m "changes"
```

#### Detailed Commit Example

```
feat(training): add video lesson support

- Add video upload functionality to lesson editor
- Implement video player with progress tracking
- Add video transcoding service integration
- Update database schema for video metadata

Closes #123
```

## 🔄 Pull Request Process

### 1. Preparing Your Pull Request

```bash
# 1. Ensure your branch is up to date
git checkout develop
git pull upstream develop
git checkout feature/your-feature-name
git rebase develop

# 2. Run final checks
npm run pre-commit
npm run build

# 3. Push your changes
git push origin feature/your-feature-name
```

### 2. Creating the Pull Request

Use our PR template:

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] E2E tests pass
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots to help explain your changes.

## Checklist
- [ ] My code follows the style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
```

### 3. PR Review Process

1. **Automated Checks**: CI/CD pipeline runs automatically
2. **Code Review**: At least one maintainer reviews your code
3. **Testing**: Reviewers test functionality manually if needed
4. **Feedback**: Address any requested changes
5. **Approval**: PR gets approved by maintainers
6. **Merge**: Maintainer merges the PR

### 4. Addressing Review Feedback

```bash
# 1. Make requested changes
# 2. Commit changes
git add .
git commit -m "fix: address PR review feedback"

# 3. Push updates
git push origin feature/your-feature-name

# The PR will automatically update
```

## 🧪 Testing Requirements

### Test Coverage Requirements

- **Unit Tests**: Minimum 80% coverage for new code
- **Integration Tests**: Required for API endpoints
- **E2E Tests**: Required for critical user workflows

### Writing Tests

```typescript
// Example unit test
// components/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '../Button'

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument()
  })

  it('calls onClick handler when clicked', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

```typescript
// Example integration test
// app/api/__tests__/auth.test.ts
import { createMocks } from 'node-mocks-http'
import handler from '../auth/login/route'

describe('/api/auth/login', () => {
  it('authenticates user with valid credentials', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        email: '<EMAIL>',
        password: 'password123'
      }
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(200)
    const data = JSON.parse(res._getData())
    expect(data.success).toBe(true)
  })
})
```

### Running Tests

```bash
# Run all tests
npm run test

# Run specific test types
npm run test:unit
npm run test:integration
npm run test:e2e

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## 📝 Documentation Requirements

### Code Documentation

```typescript
/**
 * Calculates the completion percentage for a training module
 * @param completedActivities - Number of completed activities
 * @param totalActivities - Total number of activities in the module
 * @returns Completion percentage (0-100)
 * @throws Error if totalActivities is 0 or negative
 */
export function calculateCompletionPercentage(
  completedActivities: number,
  totalActivities: number
): number {
  if (totalActivities <= 0) {
    throw new Error('Total activities must be greater than 0')
  }
  
  return Math.round((completedActivities / totalActivities) * 100)
}
```

### Component Documentation

```typescript
interface ButtonProps {
  /** Button text content */
  children: React.ReactNode
  /** Button visual style variant */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  /** Button size */
  size?: 'default' | 'sm' | 'lg' | 'icon'
  /** Whether the button is in loading state */
  loading?: boolean
  /** Click event handler */
  onClick?: () => void
}

/**
 * Reusable button component with multiple variants and states
 * 
 * @example
 * ```tsx
 * <Button variant="destructive" onClick={handleDelete}>
 *   Delete Item
 * </Button>
 * ```
 */
export function Button({ children, variant = 'default', ...props }: ButtonProps) {
  // Component implementation
}
```

### API Documentation

```typescript
/**
 * GET /api/training/modules
 * 
 * Retrieves a paginated list of training modules
 * 
 * @param page - Page number (default: 1)
 * @param limit - Items per page (default: 20, max: 100)
 * @param status - Filter by module status
 * @param search - Search by title or description
 * 
 * @returns {Object} Response object
 * @returns {TrainingModule[]} response.data.modules - Array of training modules
 * @returns {PaginationInfo} response.data.pagination - Pagination information
 * 
 * @throws {401} Unauthorized - Invalid or missing authentication
 * @throws {403} Forbidden - Insufficient permissions
 * @throws {500} Internal Server Error - Server error
 */
```

## 🔧 Development Tools

### Required VS Code Extensions

```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json",
    "ms-playwright.playwright"
  ]
}
```

### Git Hooks Setup

```bash
# Install husky for git hooks
npm run prepare

# Pre-commit hook runs:
# - ESLint
# - Prettier
# - TypeScript check
# - Unit tests

# Pre-push hook runs:
# - Full test suite
# - Build check
```

### Development Scripts

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "format": "prettier --write .",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    "pre-commit": "lint-staged",
    "analyze": "ANALYZE=true npm run build"
  }
}
```

## 🚀 Release Process

### Version Numbering

We follow [Semantic Versioning](https://semver.org/):

- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Workflow

```bash
# 1. Create release branch
git checkout develop
git pull upstream develop
git checkout -b release/v1.2.0

# 2. Update version
npm version minor # or major/patch

# 3. Update CHANGELOG.md
# 4. Create PR to main
# 5. After merge, tag the release
git tag v1.2.0
git push upstream v1.2.0

# 6. Merge back to develop
git checkout develop
git merge main
git push upstream develop
```

## 🎯 Best Practices

### Code Organization

```
src/
├── components/
│   ├── ui/           # Reusable UI components
│   ├── forms/        # Form components
│   ├── layout/       # Layout components
│   └── features/     # Feature-specific components
├── hooks/            # Custom React hooks
├── lib/              # Utility functions and configurations
├── types/            # TypeScript type definitions
├── styles/           # Global styles and themes
└── utils/            # Helper functions
```

### Performance Considerations

- Use `React.memo()` for expensive components
- Implement proper loading states
- Optimize images and assets
- Use lazy loading for non-critical components
- Implement proper error boundaries

### Security Guidelines

- Never commit sensitive data
- Validate all user inputs
- Use proper authentication checks
- Implement rate limiting
- Follow OWASP security guidelines

### Accessibility Standards

- Use semantic HTML elements
- Provide proper ARIA labels
- Ensure keyboard navigation
- Maintain color contrast ratios
- Test with screen readers

## 🆘 Getting Help

### Communication Channels

1. **GitHub Issues**: For bug reports and feature requests
2. **GitHub Discussions**: For questions and general discussion
3. **Discord**: Real-time chat with the community
4. **Email**: For security issues or private matters

### Mentorship Program

New contributors can request mentorship:

1. Comment on the issue you want to work on
2. Tag `@mentors` in your comment
3. A mentor will be assigned to help you
4. Regular check-ins and code review support

---

**Next**: Learn about [Code Style Guidelines](code-style.md) for consistent code formatting.
