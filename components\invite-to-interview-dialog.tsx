'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { useToast } from '@/components/ui/use-toast'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Calendar,
  MessageSquare,
  Send,
  User,
  Phone,
  Video,
  MapPin,
  Clock,
  CheckCircle2,
  AlertCircle,
  CalendarDays,
  Users,
  Plus,
  X
} from 'lucide-react'

interface InviteToInterviewDialogProps {
  prospectId: string
  prospectName: string
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function InviteToInterviewDialog({
  prospectId,
  prospectName,
  open,
  onOpenChange,
  onSuccess
}: InviteToInterviewDialogProps) {
  const [message, setMessage] = useState('')
  const [interviewType, setInterviewType] = useState('video_call')
  const [duration, setDuration] = useState('60')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [availableSlots, setAvailableSlots] = useState<any[]>([])
  const [loadingSlots, setLoadingSlots] = useState(false)
  const [useAutoSlots, setUseAutoSlots] = useState(true)
  const [dateRange, setDateRange] = useState('week') // 'week', 'month', 'custom'
  const [selectedDates, setSelectedDates] = useState<string[]>([])
  const [additionalInterviewers, setAdditionalInterviewers] = useState<string[]>([])
  const [bpoTeamMembers, setBpoTeamMembers] = useState<any[]>([])
  const [loadingTeamMembers, setLoadingTeamMembers] = useState(false)
  const { toast } = useToast()

  // Fetch available time slots when dialog opens
  useEffect(() => {
    if (open && useAutoSlots) {
      fetchAvailableSlots()
    }
    if (open) {
      fetchBpoTeamMembers()
    }
  }, [open, duration, useAutoSlots, dateRange, selectedDates, additionalInterviewers])

  const fetchBpoTeamMembers = async () => {
    try {
      setLoadingTeamMembers(true)
      const response = await fetch('/api/bpo/team-members')

      if (response.ok) {
        const data = await response.json()
        setBpoTeamMembers(data.teamMembers || [])
      } else {
        console.error('Failed to fetch team members')
        setBpoTeamMembers([])
      }
    } catch (error) {
      console.error('Error fetching team members:', error)
      setBpoTeamMembers([])
    } finally {
      setLoadingTeamMembers(false)
    }
  }

  const fetchAvailableSlots = async () => {
    try {
      setLoadingSlots(true)

      // Build query parameters
      const params = new URLSearchParams({
        durationMinutes: duration,
        dateRange: dateRange
      })

      // Add specific dates if custom range
      if (dateRange === 'custom' && selectedDates.length > 0) {
        selectedDates.forEach(date => params.append('dates', date))
      }

      // Add additional interviewers
      if (additionalInterviewers.length > 0) {
        additionalInterviewers.forEach(id => params.append('interviewers', id))
      }

      const response = await fetch(`/api/bpo/schedule/available-slots?${params}`)

      if (response.ok) {
        const data = await response.json()
        setAvailableSlots(data.availableSlots || [])
      } else {
        console.error('Failed to fetch available slots')
        setAvailableSlots([])
      }
    } catch (error) {
      console.error('Error fetching available slots:', error)
      setAvailableSlots([])
    } finally {
      setLoadingSlots(false)
    }
  }

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true)

      // First, we need to find the application for this prospect
      // Since we're on the public profile page, we need to get the application ID
      // This would typically be passed as a prop, but for now we'll need to fetch it
      
      const response = await fetch('/api/interviews/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prospectId: prospectId,
          message: message || `Hello ${prospectName}, we would like to invite you for an interview. Please select a convenient time for you.`,
          interviewType: interviewType,
          durationMinutes: parseInt(duration),
          autoGenerateSlots: useAutoSlots,
          dateRange: dateRange,
          selectedDates: selectedDates,
          additionalInterviewers: additionalInterviewers
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send interview invitation')
      }

      toast({
        title: "✅ Interview Invitation Sent Successfully!",
        description: `${prospectName} has been sent an interview invitation and will receive an email notification.`,
        duration: 8000,
      })

      onOpenChange(false)

      // Reset form completely
      setMessage('')
      setInterviewType('video_call')
      setDuration('60')
      onSuccess?.()
      setDateRange('week')
      setSelectedDates([])
      setAdditionalInterviewers([])

    } catch (error: any) {
      console.error('Error sending interview invitation:', error)
      toast({
        title: "Failed to send invitation",
        description: error.message || "There was an error sending the interview invitation. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-blue-600" />
            Invite to Interview
          </DialogTitle>
          <DialogDescription>
            Send an interview invitation to {prospectName}. They will be able to select a convenient time.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-4">
            {/* Candidate Info */}
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                <User className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{prospectName}</p>
                <p className="text-sm text-gray-600">Candidate</p>
              </div>
            </div>

            {/* Interview Type */}
            <div className="space-y-2">
              <Label htmlFor="interview-type" className="flex items-center gap-2">
                <Video className="h-4 w-4" />
                Interview Type
              </Label>
              <Select value={interviewType} onValueChange={setInterviewType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select interview type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="video_call">
                    <div className="flex items-center gap-2">
                      <Video className="h-4 w-4" />
                      Video Call
                    </div>
                  </SelectItem>
                  <SelectItem value="phone_call">
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Phone Call
                    </div>
                  </SelectItem>
                  <SelectItem value="in_person">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      In Person
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Duration */}
            <div className="space-y-2">
              <Label htmlFor="duration" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Duration
              </Label>
              <Select value={duration} onValueChange={setDuration}>
                <SelectTrigger>
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">30 minutes</SelectItem>
                  <SelectItem value="45">45 minutes</SelectItem>
                  <SelectItem value="60">1 hour</SelectItem>
                  <SelectItem value="90">1.5 hours</SelectItem>
                  <SelectItem value="120">2 hours</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Additional Interviewers */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Additional Interviewers
              </Label>
              <div className="space-y-2">
                {loadingTeamMembers ? (
                  <div className="text-sm text-gray-500">Loading team members...</div>
                ) : (
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {bpoTeamMembers.map((member) => (
                      <div key={member.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`interviewer-${member.id}`}
                          checked={additionalInterviewers.includes(member.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setAdditionalInterviewers([...additionalInterviewers, member.id])
                            } else {
                              setAdditionalInterviewers(additionalInterviewers.filter(id => id !== member.id))
                            }
                          }}
                        />
                        <Label htmlFor={`interviewer-${member.id}`} className="text-sm">
                          {member.name || member.email}
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-4">

            {/* Date Range Selection */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                <CalendarDays className="h-4 w-4" />
                Available Period
              </Label>
              <div className="space-y-2">
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select time period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="week">This Week</SelectItem>
                    <SelectItem value="month">This Month</SelectItem>
                    <SelectItem value="custom">Custom Dates</SelectItem>
                  </SelectContent>
                </Select>

                {dateRange === 'custom' && (
                  <div className="space-y-2">
                    <Label className="text-xs text-gray-600">Select specific dates:</Label>
                    <div className="grid grid-cols-7 gap-1 text-xs">
                      {/* This would be a proper date picker in production */}
                      <div className="text-center text-gray-500">Coming soon...</div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Schedule Integration */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Use My Schedule
                </Label>
                <Switch
                  checked={useAutoSlots}
                  onCheckedChange={(checked) => {
                    setUseAutoSlots(checked)
                    if (checked) {
                      fetchAvailableSlots()
                    }
                  }}
                />
              </div>

            {useAutoSlots && (
              <div className="space-y-2">
                {loadingSlots ? (
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    Loading available time slots...
                  </div>
                ) : availableSlots.length > 0 ? (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">
                        {availableSlots.length} time slots available
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {availableSlots.slice(0, 3).map((slot, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {new Date(slot.startTime).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric'
                          })} at {new Date(slot.startTime).toLocaleTimeString('en-US', {
                            hour: 'numeric',
                            minute: '2-digit'
                          })}
                        </Badge>
                      ))}
                      {availableSlots.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{availableSlots.length - 3} more
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-green-700 mt-1">
                      These slots will be offered to the candidate
                    </p>
                  </div>
                ) : (
                  <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <AlertCircle className="h-4 w-4 text-amber-600" />
                      <span className="text-sm font-medium text-amber-800">
                        No available time slots
                      </span>
                    </div>
                    <p className="text-xs text-amber-700">
                      Please set up your schedule first or turn off auto-scheduling
                    </p>
                  </div>
                )}
              </div>
            )}

              {!useAutoSlots && (
                <p className="text-xs text-gray-500">
                  The candidate will be asked to suggest their preferred times
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Message - Full Width */}
        <div className="space-y-4 mt-6">
          <div className="space-y-2">
            <Label htmlFor="message" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Personal Message (Optional)
            </Label>
            <Textarea
              id="message"
              placeholder={`Hello ${prospectName}, we would like to invite you for an interview. Please select a convenient time for you.`}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={3}
              className="resize-none"
            />
            <p className="text-xs text-gray-500">
              This message will be included with the interview invitation.
            </p>
          </div>

          {/* What happens next */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <h4 className="font-medium text-blue-900 text-sm mb-2">What happens next:</h4>
            <ul className="text-xs text-blue-800 space-y-1">
              <li>• The candidate will receive an interview invitation</li>
              <li>• They can select from available time slots</li>
              <li>• You'll be notified once they confirm a time</li>
              <li>• Interview details will be shared with both parties</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="flex gap-3">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isSubmitting ? (
              "Sending..."
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Send Invitation
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
