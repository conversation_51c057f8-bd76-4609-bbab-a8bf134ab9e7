-- Add interview type column to interviews table
-- This allows BPO users to specify the type of interview when sending invitations

-- Create enum for interview types
CREATE TYPE interview_type AS ENUM (
  'phone_call',
  'video_call', 
  'in_person',
  'other'
);

-- Add interview_type column to interviews table
ALTER TABLE interviews 
ADD COLUMN interview_type interview_type DEFAULT 'video_call';

-- Add comment for documentation
COMMENT ON COLUMN interviews.interview_type IS 'Type of interview: phone_call, video_call, in_person, or other';

-- Update existing records to have a default interview type
UPDATE interviews 
SET interview_type = 'video_call' 
WHERE interview_type IS NULL;
