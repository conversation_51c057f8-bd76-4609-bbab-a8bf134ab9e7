"use client"

import {
  <PERSON>riefcase,
  Building,
  Clock,
  DollarSign,
  Filter,
  MapPin,
  Search,
  Star,
  Calendar,
  ArrowUpDown,
} from "lucide-react"
import { useState, useEffect } from "react"
import { useTheme } from "next-themes"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"

interface JobProps {
  id: string
  title: string
  company: string
  companyLogo: string | null
  location: string
  jobType: string
  salary: string
  postedDate: string
  description: string
  skills: string[]
  isNew: boolean
  deadline: string | null
}

export function JobBoardPage({ jobs = [] }: { jobs?: JobProps[] }) {
  const [searchTerm, setSearchTerm] = useState("")
  const [jobTypeFilter, setJobTypeFilter] = useState<string[]>([])
  const [sortBy, setSortBy] = useState("relevance")
  const [mounted, setMounted] = useState(false)
  const { theme } = useTheme()
  const [isLoading, setIsLoading] = useState(true)

  // Simulate loading state
  useEffect(() => {
    setMounted(true)
    const timer = setTimeout(() => setIsLoading(false), 800)
    return () => clearTimeout(timer)
  }, [])

  // Sort jobs based on selected option
  const sortJobs = (jobs: JobProps[]) => {
    switch (sortBy) {
      case "recent":
        return [...jobs].sort((a, b) => {
          const dateA = a.postedDate.replace("Posted ", "").replace(" days ago", "")
          const dateB = b.postedDate.replace("Posted ", "").replace(" days ago", "")
          return parseInt(dateA) - parseInt(dateB)
        })
      case "salary-high":
        return [...jobs].sort((a, b) => {
          const salaryA = a.salary.includes("-")
            ? parseInt(a.salary.split("-")[1].replace(/\D/g, ""))
            : parseInt(a.salary.replace(/\D/g, "")) || 0
          const salaryB = b.salary.includes("-")
            ? parseInt(b.salary.split("-")[1].replace(/\D/g, ""))
            : parseInt(b.salary.replace(/\D/g, "")) || 0
          return salaryB - salaryA
        })
      case "salary-low":
        return [...jobs].sort((a, b) => {
          const salaryA = a.salary.includes("-")
            ? parseInt(a.salary.split("-")[0].replace(/\D/g, ""))
            : parseInt(a.salary.replace(/\D/g, "")) || 0
          const salaryB = b.salary.includes("-")
            ? parseInt(b.salary.split("-")[0].replace(/\D/g, ""))
            : parseInt(b.salary.replace(/\D/g, "")) || 0
          return salaryA - salaryB
        })
      default:
        return jobs
    }
  }

  // Filter jobs based on search term and job type
  const filteredJobs = sortJobs(jobs.filter(job => {
    // Search filter
    const searchLower = searchTerm.toLowerCase()
    const matchesSearch = searchTerm === "" ||
      job.title.toLowerCase().includes(searchLower) ||
      job.company.toLowerCase().includes(searchLower) ||
      job.description.toLowerCase().includes(searchLower) ||
      job.skills.some(skill => skill.toLowerCase().includes(searchLower))

    // Job type filter
    const matchesJobType = jobTypeFilter.length === 0 ||
      jobTypeFilter.includes(job.jobType.toLowerCase())

    return matchesSearch && matchesJobType
  }))

  // Handle job type filter change
  const handleJobTypeChange = (type: string) => {
    setJobTypeFilter(prev => {
      if (prev.includes(type)) {
        return prev.filter(t => t !== type)
      } else {
        return [...prev, type]
      }
    })
  }

  if (!mounted) return null
