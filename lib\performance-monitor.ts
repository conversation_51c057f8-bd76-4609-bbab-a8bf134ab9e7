/**
 * Performance Monitoring and Analytics
 * Real-time performance tracking with Web Vitals and custom metrics
 */

"use client"

import React from 'react'
import { onCLS, onINP, onFCP, onLCP, onTTFB } from 'web-vitals'

// =============================================================================
// TYPES
// =============================================================================

interface PerformanceMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  timestamp: number
  url: string
  userAgent: string
}

interface CustomMetric {
  name: string
  value: number
  unit: string
  timestamp: number
  metadata?: Record<string, any>
}

interface PerformanceReport {
  webVitals: PerformanceMetric[]
  customMetrics: CustomMetric[]
  resourceTiming: PerformanceResourceTiming[]
  navigationTiming: PerformanceNavigationTiming | null
  memoryUsage?: PerformanceMemory
  timestamp: number
  sessionId: string
}

// =============================================================================
// PERFORMANCE MONITOR CLASS
// =============================================================================

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private customMetrics: CustomMetric[] = []
  private sessionId: string
  private isInitialized = false
  private observers: PerformanceObserver[] = []

  constructor() {
    this.sessionId = this.generateSessionId()
    this.initialize()
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  private initialize() {
    if (typeof window === 'undefined' || this.isInitialized) return

    this.isInitialized = true

    // Initialize Web Vitals monitoring
    this.initializeWebVitals()

    // Initialize custom performance observers
    this.initializePerformanceObservers()

    // Monitor page visibility changes
    this.initializeVisibilityMonitoring()

    // Monitor memory usage (if available)
    this.initializeMemoryMonitoring()

    // Send report on page unload
    this.initializeReporting()
  }

  private initializeWebVitals() {
    const onVital = (metric: any) => {
      const performanceMetric: PerformanceMetric = {
        name: metric.name,
        value: metric.value,
        rating: metric.rating,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent
      }

      this.metrics.push(performanceMetric)
      this.sendMetric(performanceMetric)
    }

    // Collect all Web Vitals
    onCLS(onVital)
    onINP(onVital) // INP replaced FID in web-vitals v3+
    onFCP(onVital)
    onLCP(onVital)
    onTTFB(onVital)
  }

  private initializePerformanceObservers() {
    // Long Task Observer
    if ('PerformanceObserver' in window) {
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) { // Tasks longer than 50ms
              this.recordCustomMetric('long-task', entry.duration, 'ms', {
                startTime: entry.startTime,
                name: entry.name
              })
            }
          }
        })
        longTaskObserver.observe({ entryTypes: ['longtask'] })
        this.observers.push(longTaskObserver)
      } catch (e) {
        console.warn('Long Task Observer not supported')
      }

      // Layout Shift Observer
      try {
        const layoutShiftObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if ((entry as any).hadRecentInput) continue
            
            this.recordCustomMetric('layout-shift', (entry as any).value, 'score', {
              startTime: entry.startTime,
              sources: (entry as any).sources
            })
          }
        })
        layoutShiftObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.push(layoutShiftObserver)
      } catch (e) {
        console.warn('Layout Shift Observer not supported')
      }

      // Resource Timing Observer
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const resource = entry as PerformanceResourceTiming
            
            // Track slow resources
            if (resource.duration > 1000) {
              this.recordCustomMetric('slow-resource', resource.duration, 'ms', {
                name: resource.name,
                type: resource.initiatorType,
                size: resource.transferSize
              })
            }
          }
        })
        resourceObserver.observe({ entryTypes: ['resource'] })
        this.observers.push(resourceObserver)
      } catch (e) {
        console.warn('Resource Observer not supported')
      }
    }
  }

  private initializeVisibilityMonitoring() {
    let visibilityStart = Date.now()

    const handleVisibilityChange = () => {
      if (document.hidden) {
        const visibleTime = Date.now() - visibilityStart
        this.recordCustomMetric('page-visible-time', visibleTime, 'ms')
      } else {
        visibilityStart = Date.now()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
  }

  private initializeMemoryMonitoring() {
    if ('memory' in performance) {
      const checkMemory = () => {
        const memory = (performance as any).memory
        this.recordCustomMetric('memory-used', memory.usedJSHeapSize, 'bytes')
        this.recordCustomMetric('memory-total', memory.totalJSHeapSize, 'bytes')
        this.recordCustomMetric('memory-limit', memory.jsHeapSizeLimit, 'bytes')
      }

      // Check memory usage every 30 seconds
      setInterval(checkMemory, 30000)
      checkMemory() // Initial check
    }
  }

  private initializeReporting() {
    // Send report on page unload
    window.addEventListener('beforeunload', () => {
      this.sendReport()
    })

    // Send report periodically
    setInterval(() => {
      this.sendReport()
    }, 60000) // Every minute
  }

  // =============================================================================
  // PUBLIC METHODS
  // =============================================================================

  /**
   * Record a custom performance metric
   */
  recordCustomMetric(
    name: string,
    value: number,
    unit: string = 'ms',
    metadata?: Record<string, any>
  ) {
    const metric: CustomMetric = {
      name,
      value,
      unit,
      timestamp: Date.now(),
      metadata
    }

    this.customMetrics.push(metric)

    // Log significant metrics
    if (value > 1000 && unit === 'ms') {
      console.warn(`Performance Warning: ${name} took ${value}ms`, metadata)
    }
  }

  /**
   * Start timing a custom operation
   */
  startTiming(name: string): () => void {
    const startTime = performance.now()
    
    return () => {
      const duration = performance.now() - startTime
      this.recordCustomMetric(name, duration, 'ms')
    }
  }

  /**
   * Measure function execution time
   */
  measureFunction<T extends (...args: any[]) => any>(
    fn: T,
    name?: string
  ): T {
    return ((...args: any[]) => {
      const functionName = name || fn.name || 'anonymous'
      const endTiming = this.startTiming(`function-${functionName}`)
      
      try {
        const result = fn(...args)
        
        // Handle async functions
        if (result instanceof Promise) {
          return result.finally(() => endTiming())
        }
        
        endTiming()
        return result
      } catch (error) {
        endTiming()
        throw error
      }
    }) as T
  }

  /**
   * Get current performance report
   */
  getReport(): PerformanceReport {
    const report: PerformanceReport = {
      webVitals: [...this.metrics],
      customMetrics: [...this.customMetrics],
      resourceTiming: this.getResourceTiming(),
      navigationTiming: this.getNavigationTiming(),
      timestamp: Date.now(),
      sessionId: this.sessionId
    }

    if ('memory' in performance) {
      report.memoryUsage = (performance as any).memory
    }

    return report
  }

  /**
   * Get performance summary
   */
  getSummary() {
    const webVitalsGood = this.metrics.filter(m => m.rating === 'good').length
    const webVitalsTotal = this.metrics.length
    const avgCustomMetric = this.customMetrics.reduce((sum, m) => sum + m.value, 0) / this.customMetrics.length

    return {
      webVitalsScore: webVitalsTotal > 0 ? (webVitalsGood / webVitalsTotal) * 100 : 0,
      totalMetrics: this.metrics.length + this.customMetrics.length,
      averageCustomMetric: avgCustomMetric || 0,
      sessionDuration: Date.now() - parseInt(this.sessionId.split('-')[0]),
      memoryUsage: 'memory' in performance ? (performance as any).memory : null
    }
  }

  /**
   * Clear all metrics
   */
  clear() {
    this.metrics = []
    this.customMetrics = []
  }

  // =============================================================================
  // PRIVATE METHODS
  // =============================================================================

  private getResourceTiming(): PerformanceResourceTiming[] {
    return performance.getEntriesByType('resource') as PerformanceResourceTiming[]
  }

  private getNavigationTiming(): PerformanceNavigationTiming | null {
    const entries = performance.getEntriesByType('navigation')
    return entries.length > 0 ? entries[0] as PerformanceNavigationTiming : null
  }

  private sendMetric(metric: PerformanceMetric) {
    // In a real application, send to analytics service
    if (process.env.NODE_ENV === 'development') {
      console.log('Performance Metric:', metric)
    }

    // Example: Send to analytics service
    // analytics.track('performance_metric', metric)
  }

  private sendReport() {
    const report = this.getReport()
    
    // In a real application, send to analytics service
    if (process.env.NODE_ENV === 'development') {
      console.log('Performance Report:', report)
    }

    // Example: Send to analytics service
    // analytics.track('performance_report', report)
  }

  /**
   * Cleanup observers
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// =============================================================================
// SINGLETON INSTANCE
// =============================================================================

export const performanceMonitor = new PerformanceMonitor()

// =============================================================================
// REACT HOOKS
// =============================================================================

/**
 * Hook to measure component render time
 */
export function usePerformanceMonitor(componentName: string) {
  React.useEffect(() => {
    const endTiming = performanceMonitor.startTiming(`component-${componentName}`)
    return endTiming
  }, [componentName])
}

/**
 * Hook to track page load performance
 */
export function usePagePerformance(pageName: string) {
  React.useEffect(() => {
    const startTime = Date.now()
    
    const handleLoad = () => {
      const loadTime = Date.now() - startTime
      performanceMonitor.recordCustomMetric(`page-load-${pageName}`, loadTime, 'ms')
    }

    if (document.readyState === 'complete') {
      handleLoad()
    } else {
      window.addEventListener('load', handleLoad)
      return () => window.removeEventListener('load', handleLoad)
    }
  }, [pageName])
}

/**
 * Hook to measure API call performance
 */
export function useApiPerformance() {
  return React.useCallback((apiName: string, promise: Promise<any>) => {
    const endTiming = performanceMonitor.startTiming(`api-${apiName}`)
    
    return promise.finally(() => {
      endTiming()
    })
  }, [])
}

// =============================================================================
// PERFORMANCE UTILITIES
// =============================================================================

/**
 * Decorator for measuring function performance
 */
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const methodName = name || `${target.constructor.name}.${propertyKey}`
    
    descriptor.value = performanceMonitor.measureFunction(originalMethod, methodName)
    
    return descriptor
  }
}

/**
 * Measure async operation performance
 */
export async function measureAsync<T>(
  name: string,
  operation: () => Promise<T>
): Promise<T> {
  const endTiming = performanceMonitor.startTiming(name)
  
  try {
    const result = await operation()
    return result
  } finally {
    endTiming()
  }
}

// =============================================================================
// EXPORTS
// =============================================================================

export { PerformanceMonitor }
export type { PerformanceMetric, CustomMetric, PerformanceReport }
