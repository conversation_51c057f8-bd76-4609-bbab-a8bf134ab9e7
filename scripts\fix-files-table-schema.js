const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixFilesTableSchema() {
  console.log('🔧 Fixing Files Table Schema...\n');

  try {
    // 1. Check current schema
    console.log('1️⃣ Checking current files table schema...');
    const { data: columns, error: schemaError } = await supabase
      .rpc('get_table_schema', { table_name: 'files' })
      .catch(async () => {
        // Fallback to information_schema query
        const { data, error } = await supabase
          .from('information_schema.columns')
          .select('column_name, data_type, is_nullable, column_default')
          .eq('table_schema', 'public')
          .eq('table_name', 'files')
          .order('ordinal_position');
        return { data, error };
      });

    if (schemaError) {
      console.error('❌ Error checking schema:', schemaError);
      return;
    }

    console.log('📋 Current schema:');
    columns?.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });

    // 2. Fix the module_id column to be nullable
    console.log('\n2️⃣ Making module_id column nullable...');
    
    const { error: alterError } = await supabase.rpc('execute_sql', {
      sql: 'ALTER TABLE public.files ALTER COLUMN module_id DROP NOT NULL;'
    }).catch(async () => {
      // Direct SQL execution if RPC doesn't work
      return await supabase.from('_sql').insert({
        query: 'ALTER TABLE public.files ALTER COLUMN module_id DROP NOT NULL;'
      });
    });

    if (alterError) {
      console.error('❌ Error altering table:', alterError);
      console.log('💡 You may need to run this SQL manually in Supabase:');
      console.log('   ALTER TABLE public.files ALTER COLUMN module_id DROP NOT NULL;');
    } else {
      console.log('✅ Successfully made module_id nullable');
    }

    // 3. Test insert again
    console.log('\n3️⃣ Testing insert after schema fix...');
    
    const { data: prospects } = await supabase
      .from('prospects')
      .select('id')
      .limit(1);

    if (prospects && prospects.length > 0) {
      const testData = {
        prospect_id: prospects[0].id,
        file_type: 'certificate',
        file_category: 'uploaded',
        title: 'Test After Fix',
        file_url: 'https://example.com/test.pdf',
        original_filename: 'test.pdf',
        file_size: 12345,
        mime_type: 'application/pdf',
        issued_at: new Date().toISOString()
      };

      const { data: testResult, error: testError } = await supabase
        .from('files')
        .insert(testData)
        .select()
        .single();

      if (testError) {
        console.error('❌ Test insert still failed:', testError);
      } else {
        console.log('✅ Test insert successful after fix!');
        // Clean up
        await supabase.from('files').delete().eq('id', testResult.id);
        console.log('🧹 Test record cleaned up');
      }
    }

  } catch (error) {
    console.error('❌ Fix failed:', error);
    console.log('\n💡 Manual SQL to run in Supabase:');
    console.log('ALTER TABLE public.files ALTER COLUMN module_id DROP NOT NULL;');
  }
}

// Run the fix
fixFilesTableSchema();
