/**
 * Module Card Component
 * Reusable card component for displaying training modules with consistent styling
 */

"use client"

import * as React from "react"
import Link from "next/link"
import { Clock, Play, CheckCircle, Lock, ArrowRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// TypeScript interfaces for better type safety
export interface ModuleData {
  id: string
  title: string
  description?: string
  cover_image_url?: string
  duration_minutes?: number
  progressPercentage: number
  status?: 'not_started' | 'in_progress' | 'completed' | 'locked'
  required_order?: number
}

export interface ModuleCardProps {
  module: ModuleData
  variant?: 'default' | 'compact' | 'detailed'
  className?: string
  href?: string
  onClick?: () => void
  showProgress?: boolean
  showDuration?: boolean
  showDescription?: boolean
  disabled?: boolean
}

// Color scheme generator for consistent module styling
const getModuleColorScheme = (moduleId: string) => {
  const colorSchemes = [
    {
      gradient: "from-blue-500/10 to-indigo-500/10 dark:from-blue-500/5 dark:to-indigo-500/5",
      button: "bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700",
      avatar: "bg-blue-600",
      border: "border-blue-200 dark:border-blue-800",
    },
    {
      gradient: "from-green-500/10 to-emerald-500/10 dark:from-green-500/5 dark:to-emerald-500/5",
      button: "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700",
      avatar: "bg-green-600",
      border: "border-green-200 dark:border-green-800",
    },
    {
      gradient: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/5 dark:to-pink-500/5",
      button: "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700",
      avatar: "bg-purple-600",
      border: "border-purple-200 dark:border-purple-800",
    },
    {
      gradient: "from-amber-500/10 to-orange-500/10 dark:from-amber-500/5 dark:to-orange-500/5",
      button: "bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700",
      avatar: "bg-amber-600",
      border: "border-amber-200 dark:border-amber-800",
    },
    {
      gradient: "from-sky-500/10 to-cyan-500/10 dark:from-sky-500/5 dark:to-cyan-500/5",
      button: "bg-gradient-to-r from-sky-600 to-cyan-600 hover:from-sky-700 hover:to-cyan-700",
      avatar: "bg-sky-600",
      border: "border-sky-200 dark:border-sky-800",
    },
  ]
  
  const hash = moduleId.charCodeAt(moduleId.length - 1) % colorSchemes.length
  return colorSchemes[hash]
}

// Generate avatar fallback from module title
const generateAvatarFallback = (title: string): string => {
  const words = title.split(' ')
  if (words.length >= 2) {
    return `${words[0][0]}${words[1][0]}`.toUpperCase()
  }
  return title.substring(0, 2).toUpperCase()
}

// Get status information
const getModuleStatus = (module: ModuleData) => {
  const isCompleted = module.progressPercentage === 100
  const isInProgress = module.progressPercentage > 0 && module.progressPercentage < 100
  const isLocked = module.status === 'locked'
  const isNotStarted = module.progressPercentage === 0 && !isLocked
  
  return {
    isCompleted,
    isInProgress,
    isLocked,
    isNotStarted,
    statusText: isCompleted ? 'Completed' : isInProgress ? 'In Progress' : isLocked ? 'Locked' : 'Not Started',
    actionText: isCompleted ? 'Review' : isInProgress ? 'Continue' : 'Start',
    icon: isCompleted ? CheckCircle : isInProgress ? Play : isLocked ? Lock : ArrowRight
  }
}

// Format duration
const formatDuration = (minutes?: number): string => {
  if (!minutes) return "Duration not specified"
  
  if (minutes < 60) {
    return `${minutes} min`
  }
  
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  
  if (remainingMinutes === 0) {
    return `${hours}h`
  }
  
  return `${hours}h ${remainingMinutes}m`
}

export const ModuleCard = React.forwardRef<
  HTMLDivElement,
  ModuleCardProps
>(({
  module,
  variant = 'default',
  className,
  href,
  onClick,
  showProgress = true,
  showDuration = true,
  showDescription = false,
  disabled = false,
  ...props
}, ref) => {
  const colorScheme = getModuleColorScheme(module.id)
  const status = getModuleStatus(module)
  const StatusIcon = status.icon
  
  const cardContent = (
    <Card 
      ref={ref}
      className={cn(
        "border-none shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md",
        variant === 'compact' && "min-w-[280px] max-w-sm",
        variant === 'default' && "min-w-[340px] max-w-xs",
        variant === 'detailed' && "w-full",
        disabled && "opacity-50 cursor-not-allowed",
        !disabled && "hover:scale-[1.02] cursor-pointer",
        className
      )}
      onClick={!disabled ? onClick : undefined}
      {...props}
    >
      {/* Header with gradient background */}
      <div className={cn(
        "relative bg-gradient-to-r",
        colorScheme.gradient,
        variant === 'compact' ? "h-24" : variant === 'detailed' ? "h-32" : "h-36"
      )}>
        {/* Status badges */}
        <div className="absolute top-3 left-3 flex items-center gap-2">
          <Badge 
            className={cn(
              "text-xs font-medium",
              status.isCompleted && "bg-green-100 text-green-700 hover:bg-green-100",
              status.isInProgress && "bg-blue-100 text-blue-700 hover:bg-blue-100",
              status.isLocked && "bg-gray-100 text-gray-700 hover:bg-gray-100",
              status.isNotStarted && "bg-gray-100 text-gray-700 hover:bg-gray-100"
            )}
          >
            {status.statusText}
          </Badge>
          {showProgress && (
            <Badge variant="outline" className="bg-white/90 text-gray-700 text-xs">
              {module.progressPercentage}%
            </Badge>
          )}
        </div>
        
        {/* Module info */}
        <div className="absolute bottom-3 left-3 flex items-center gap-3">
          <Avatar className={cn(
            "border-2 border-white shadow-sm",
            variant === 'compact' ? "h-8 w-8" : "h-10 w-10"
          )}>
            {module.cover_image_url && (
              <AvatarImage src={module.cover_image_url} alt={module.title} />
            )}
            <AvatarFallback className={cn("text-white font-semibold", colorScheme.avatar)}>
              {generateAvatarFallback(module.title)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <h3 className={cn(
              "font-semibold text-gray-900 dark:text-gray-100 truncate",
              variant === 'compact' ? "text-sm" : "text-base"
            )}>
              {module.title}
            </h3>
            {showDuration && (
              <div className="flex items-center text-xs text-gray-600 dark:text-gray-400 mt-0.5">
                <Clock className="mr-1 h-3 w-3 flex-shrink-0" />
                <span className="truncate">{formatDuration(module.duration_minutes)}</span>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Card content */}
      <CardContent className={cn(
        "p-4 space-y-3",
        variant === 'compact' && "p-3 space-y-2"
      )}>
        {/* Description */}
        {showDescription && module.description && (
          <p className="text-sm text-muted-foreground line-clamp-2">
            {module.description}
          </p>
        )}
        
        {/* Progress bar */}
        {showProgress && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">Progress</span>
              <span className="font-medium">{module.progressPercentage}%</span>
            </div>
            <Progress 
              value={module.progressPercentage} 
              className="h-1.5"
              aria-label={`Module progress: ${module.progressPercentage}%`}
            />
          </div>
        )}
        
        {/* Action button */}
        <Button 
          className={cn(
            "w-full text-white font-medium",
            colorScheme.button,
            variant === 'compact' && "h-8 text-sm"
          )}
          disabled={disabled || status.isLocked}
          asChild={!!href}
          aria-label={`${status.actionText} ${module.title}`}
        >
          {href ? (
            <Link href={href}>
              <StatusIcon className="mr-2 h-4 w-4" />
              {status.actionText}
            </Link>
          ) : (
            <>
              <StatusIcon className="mr-2 h-4 w-4" />
              {status.actionText}
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  )
  
  return cardContent
})

ModuleCard.displayName = "ModuleCard"

// Export utility functions for external use
export { getModuleColorScheme, generateAvatarFallback, getModuleStatus, formatDuration }
