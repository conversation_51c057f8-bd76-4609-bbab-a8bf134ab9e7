# ⚡ Performance Optimization Guide

Comprehensive guide to optimizing the BPO Training Platform for maximum performance, covering caching strategies, database optimization, and frontend performance.

## 🎯 Performance Overview

The platform implements **multi-layer performance optimizations**:

- **Advanced Caching System**: 80%+ hit rates with intelligent invalidation
- **Database Optimization**: Query optimization and connection pooling
- **Frontend Performance**: Lazy loading, code splitting, and memoization
- **Real-time Monitoring**: Performance analytics and optimization insights

## 🧠 Caching Architecture

### Multi-Layer Caching System

```typescript
// Cache configuration
const cacheConfig = {
  training: {
    ttl: 5 * 60 * 1000,        // 5 minutes
    strategy: 'stale-while-revalidate',
    maxSize: 100
  },
  user: {
    ttl: 10 * 60 * 1000,       // 10 minutes
    strategy: 'cache-first',
    maxSize: 500,
    persistent: true
  },
  api: {
    ttl: 2 * 60 * 1000,        // 2 minutes
    strategy: 'network-first',
    maxSize: 200
  },
  static: {
    ttl: 60 * 60 * 1000,       // 1 hour
    strategy: 'cache-first',
    maxSize: 1000
  }
}
```

### Cache Implementation

```typescript
// Advanced LRU Cache with TTL
class AdvancedCache<T> {
  private cache = new Map<string, CacheEntry<T>>()
  private accessOrder = new Map<string, number>()
  private maxSize: number
  private ttl: number
  private strategy: CacheStrategy

  constructor(options: CacheOptions) {
    this.maxSize = options.maxSize
    this.ttl = options.ttl
    this.strategy = options.strategy
  }

  async get(key: string, fetcher?: () => Promise<T>): Promise<T | null> {
    const entry = this.cache.get(key)
    const now = Date.now()

    // Cache hit - return if not expired
    if (entry && now < entry.expiresAt) {
      this.updateAccessOrder(key)
      return entry.value
    }

    // Stale-while-revalidate strategy
    if (entry && this.strategy === 'stale-while-revalidate') {
      this.updateAccessOrder(key)
      // Return stale data immediately
      if (fetcher) {
        // Revalidate in background
        this.revalidateInBackground(key, fetcher)
      }
      return entry.value
    }

    // Cache miss or expired - fetch new data
    if (fetcher) {
      const value = await fetcher()
      this.set(key, value)
      return value
    }

    return null
  }

  set(key: string, value: T): void {
    const now = Date.now()
    
    // Remove oldest entries if at capacity
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLeastRecentlyUsed()
    }

    this.cache.set(key, {
      value,
      createdAt: now,
      expiresAt: now + this.ttl
    })
    
    this.updateAccessOrder(key)
  }

  private async revalidateInBackground(key: string, fetcher: () => Promise<T>) {
    try {
      const value = await fetcher()
      this.set(key, value)
    } catch (error) {
      console.warn('Background revalidation failed:', error)
    }
  }

  private evictLeastRecentlyUsed(): void {
    let oldestKey = ''
    let oldestAccess = Infinity

    for (const [key, accessTime] of this.accessOrder) {
      if (accessTime < oldestAccess) {
        oldestAccess = accessTime
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
      this.accessOrder.delete(oldestKey)
    }
  }

  private updateAccessOrder(key: string): void {
    this.accessOrder.set(key, Date.now())
  }
}
```

### Cache Usage Examples

```typescript
// Training modules cache
const trainingCache = new AdvancedCache<TrainingModule[]>({
  maxSize: 100,
  ttl: 5 * 60 * 1000,
  strategy: 'stale-while-revalidate'
})

// Cached training modules fetch
const getTrainingModules = async (userId: string) => {
  return trainingCache.get(`training-modules-${userId}`, async () => {
    const { data } = await supabase
      .from('training_modules')
      .select('*')
      .eq('status', 'published')
    
    return data || []
  })
}

// User profile cache with persistence
const userCache = new AdvancedCache<UserProfile>({
  maxSize: 500,
  ttl: 10 * 60 * 1000,
  strategy: 'cache-first',
  persistent: true
})

// API response cache
const apiCache = new AdvancedCache<any>({
  maxSize: 200,
  ttl: 2 * 60 * 1000,
  strategy: 'network-first'
})
```

## 🗄️ Database Optimization

### Query Optimization

```typescript
// Optimized query with batching
const getOptimizedTrainingProgress = async (prospectId: string) => {
  // Single query with joins instead of multiple queries
  const { data } = await supabase
    .from('progress')
    .select(`
      *,
      activity:activities(
        id,
        title,
        type,
        lesson:lessons(
          id,
          title,
          module:training_modules(
            id,
            title,
            cover_image_url
          )
        )
      )
    `)
    .eq('prospect_id', prospectId)
    .order('updated_at', { ascending: false })

  return data
}

// Batch operations for better performance
const updateMultipleProgress = async (updates: ProgressUpdate[]) => {
  // Use upsert for batch updates
  const { data, error } = await supabase
    .from('progress')
    .upsert(updates, { 
      onConflict: 'prospect_id,activity_id',
      ignoreDuplicates: false 
    })

  return { data, error }
}

// Optimized pagination with cursor-based approach
const getPaginatedJobs = async (cursor?: string, limit = 20) => {
  let query = supabase
    .from('job_postings')
    .select(`
      *,
      bpo:bpos(name, logo_url),
      applications_count:applications(count)
    `)
    .eq('status', 'published')
    .order('created_at', { ascending: false })
    .limit(limit)

  if (cursor) {
    query = query.lt('created_at', cursor)
  }

  const { data } = await query
  return data
}
```

### Database Indexes

```sql
-- Performance indexes for common queries
CREATE INDEX CONCURRENTLY idx_training_modules_status_published 
ON training_modules(status) WHERE status = 'published';

CREATE INDEX CONCURRENTLY idx_job_postings_active 
ON job_postings(status, application_deadline) 
WHERE status = 'published' AND application_deadline > NOW();

CREATE INDEX CONCURRENTLY idx_progress_prospect_activity 
ON progress(prospect_id, activity_id, status);

CREATE INDEX CONCURRENTLY idx_applications_job_status 
ON applications(job_id, status, submitted_at);

-- Composite indexes for complex queries
CREATE INDEX CONCURRENTLY idx_users_role_status 
ON users(role, status) WHERE status = 'active';

CREATE INDEX CONCURRENTLY idx_bpo_team_members_lookup 
ON bpo_team_members(user_id, bpo_id, role) 
WHERE is_placeholder = false;

-- Full-text search indexes
CREATE INDEX CONCURRENTLY idx_job_postings_search 
ON job_postings USING gin(to_tsvector('english', title || ' ' || description));

CREATE INDEX CONCURRENTLY idx_training_modules_search 
ON training_modules USING gin(to_tsvector('english', title || ' ' || description));
```

### Connection Pooling

```typescript
// Optimized Supabase client configuration
const supabaseConfig = {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  },
  db: {
    schema: 'public'
  },
  global: {
    headers: {
      'x-application-name': 'bpo-training-platform'
    }
  },
  // Connection pooling settings
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
}
```

## ⚛️ React Performance Optimization

### Component Memoization

```typescript
// Optimized computation hook
const useOptimizedComputation = <T, R>(
  data: T,
  computeFn: (data: T) => R,
  deps: React.DependencyList = []
): R => {
  return useMemo(() => {
    if (!data) return null as R
    return computeFn(data)
  }, [data, ...deps])
}

// Deep memoization for complex objects
const useDeepMemo = <T>(value: T): T => {
  const ref = useRef<T>()
  
  if (!isEqual(ref.current, value)) {
    ref.current = value
  }
  
  return ref.current as T
}

// Optimized component with memoization
const TrainingModuleCard = memo(({ module, onStart }: TrainingModuleCardProps) => {
  const progressPercentage = useOptimizedComputation(
    module,
    (m) => calculateProgress(m.progress),
    [module.progress]
  )

  const handleStart = useCallback(() => {
    onStart(module.id)
  }, [module.id, onStart])

  return (
    <Card>
      <CardContent>
        <h3>{module.title}</h3>
        <ProgressIndicator value={progressPercentage} />
        <Button onClick={handleStart}>Start Module</Button>
      </CardContent>
    </Card>
  )
})
```

### Debounced Operations

```typescript
// Advanced debounce hook with cancellation
const useDebouncedOperation = <T extends any[]>(
  operation: (...args: T) => void | Promise<void>,
  delay: number,
  options: {
    leading?: boolean
    trailing?: boolean
    maxWait?: number
  } = {}
) => {
  const timeoutRef = useRef<NodeJS.Timeout>()
  const maxTimeoutRef = useRef<NodeJS.Timeout>()
  const lastCallTimeRef = useRef<number>()

  const debouncedOperation = useCallback((...args: T) => {
    const now = Date.now()
    const timeSinceLastCall = lastCallTimeRef.current ? now - lastCallTimeRef.current : 0

    const callNow = options.leading && !timeoutRef.current

    // Clear existing timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    if (maxTimeoutRef.current) {
      clearTimeout(maxTimeoutRef.current)
    }

    lastCallTimeRef.current = now

    // Execute immediately if leading edge
    if (callNow) {
      operation(...args)
    }

    // Set up trailing execution
    if (options.trailing !== false) {
      timeoutRef.current = setTimeout(() => {
        if (!callNow) {
          operation(...args)
        }
        timeoutRef.current = undefined
      }, delay)
    }

    // Set up max wait execution
    if (options.maxWait && timeSinceLastCall >= options.maxWait) {
      operation(...args)
    }
  }, [operation, delay, options])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      if (maxTimeoutRef.current) {
        clearTimeout(maxTimeoutRef.current)
      }
    }
  }, [])

  return debouncedOperation
}

// Usage in search component
const SearchComponent = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [results, setResults] = useState([])

  const performSearch = useDebouncedOperation(
    async (term: string) => {
      if (term.length < 2) {
        setResults([])
        return
      }

      const searchResults = await searchAPI(term)
      setResults(searchResults)
    },
    300,
    { trailing: true, maxWait: 1000 }
  )

  useEffect(() => {
    performSearch(searchTerm)
  }, [searchTerm, performSearch])

  return (
    <div>
      <Input
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder="Search..."
      />
      <SearchResults results={results} />
    </div>
  )
}
```

### Lazy Loading Implementation

```typescript
// Advanced lazy loading with intersection observer
const LazyComponent = ({ 
  children, 
  fallback = <div>Loading...</div>,
  threshold = 0.1,
  rootMargin = '50px'
}: LazyComponentProps) => {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold, rootMargin }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [threshold, rootMargin])

  return (
    <div ref={ref}>
      {isVisible ? children : fallback}
    </div>
  )
}

// Lazy loaded training modules
const TrainingModules = () => {
  return (
    <div className="space-y-4">
      {modules.map((module, index) => (
        <LazyComponent
          key={module.id}
          threshold={0.1}
          rootMargin="100px"
          fallback={<ModuleSkeleton />}
        >
          <TrainingModuleCard module={module} />
        </LazyComponent>
      ))}
    </div>
  )
}
```

## 🖼️ Image Optimization

### Optimized Image Component

```typescript
// Advanced image optimization with multiple formats
const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  variant = 'responsive',
  priority = false,
  lazy = true,
  fallback = '/placeholder.jpg',
  className
}: OptimizedImageProps) => {
  const [imageSrc, setImageSrc] = useState(src)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  // Generate responsive image URLs
  const generateSrcSet = (baseSrc: string) => {
    const sizes = [320, 640, 768, 1024, 1280, 1920]
    return sizes
      .map(size => `${baseSrc}?w=${size}&q=75 ${size}w`)
      .join(', ')
  }

  // Handle image load
  const handleLoad = () => {
    setIsLoading(false)
  }

  // Handle image error
  const handleError = () => {
    setHasError(true)
    setImageSrc(fallback)
    setIsLoading(false)
  }

  // Variant-specific classes
  const variantClasses = {
    avatar: 'rounded-full object-cover',
    hero: 'w-full h-64 md:h-96 object-cover',
    gallery: 'rounded-lg object-cover',
    responsive: 'w-full h-auto object-cover'
  }

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}
      
      <Image
        src={imageSrc}
        alt={alt}
        width={width}
        height={height}
        priority={priority}
        loading={lazy ? 'lazy' : 'eager'}
        srcSet={generateSrcSet(imageSrc)}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        className={`${variantClasses[variant]} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onLoad={handleLoad}
        onError={handleError}
        placeholder="blur"
        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
      />
      
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-400">
          <span>Image not available</span>
        </div>
      )}
    </div>
  )
}
```

## 📊 Performance Monitoring

### Web Vitals Tracking

```typescript
// Comprehensive performance monitoring
const performanceMonitor = {
  // Track Web Vitals
  trackWebVitals: () => {
    getCLS(metric => {
      analytics.track('web_vital', {
        name: 'CLS',
        value: metric.value,
        rating: metric.rating
      })
    })

    getFID(metric => {
      analytics.track('web_vital', {
        name: 'FID',
        value: metric.value,
        rating: metric.rating
      })
    })

    getFCP(metric => {
      analytics.track('web_vital', {
        name: 'FCP',
        value: metric.value,
        rating: metric.rating
      })
    })

    getLCP(metric => {
      analytics.track('web_vital', {
        name: 'LCP',
        value: metric.value,
        rating: metric.rating
      })
    })

    getTTFB(metric => {
      analytics.track('web_vital', {
        name: 'TTFB',
        value: metric.value,
        rating: metric.rating
      })
    })
  },

  // Track custom metrics
  trackCustomMetric: (name: string, value: number, unit = 'ms') => {
    analytics.track('custom_metric', {
      name,
      value,
      unit,
      timestamp: Date.now()
    })
  },

  // Track component render performance
  trackComponentRender: (componentName: string, renderTime: number) => {
    if (renderTime > 16) { // Slower than 60fps
      analytics.track('slow_component_render', {
        component: componentName,
        renderTime,
        timestamp: Date.now()
      })
    }
  }
}

// Performance monitoring hook
const usePerformanceMonitor = (componentName: string) => {
  const renderStartTime = useRef<number>()

  useEffect(() => {
    renderStartTime.current = performance.now()
  })

  useLayoutEffect(() => {
    if (renderStartTime.current) {
      const renderTime = performance.now() - renderStartTime.current
      performanceMonitor.trackComponentRender(componentName, renderTime)
    }
  })

  const trackOperation = useCallback((operationName: string, operation: () => Promise<any>) => {
    return async () => {
      const startTime = performance.now()
      try {
        const result = await operation()
        const duration = performance.now() - startTime
        performanceMonitor.trackCustomMetric(`${componentName}_${operationName}`, duration)
        return result
      } catch (error) {
        const duration = performance.now() - startTime
        performanceMonitor.trackCustomMetric(`${componentName}_${operationName}_error`, duration)
        throw error
      }
    }
  }, [componentName])

  return { trackOperation }
}
```

## 🚀 Bundle Optimization

### Next.js Configuration

```javascript
// next.config.mjs - Production optimizations
const nextConfig = {
  // Image optimization
  images: {
    domains: ['supabase.co', 'your-cdn.com'],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
  },

  // Compression
  compress: true,
  poweredByHeader: false,

  // Bundle optimization
  webpack: (config, { dev, isServer }) => {
    // Optimize imports
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(process.cwd()),
    }

    // Bundle analyzer in development
    if (dev && !isServer) {
      config.plugins.push(
        new (require('webpack-bundle-analyzer').BundleAnalyzerPlugin)({
          analyzerMode: 'server',
          openAnalyzer: false,
        })
      )
    }

    return config
  },

  // Experimental features
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
  },

  // Headers for performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          }
        ]
      }
    ]
  }
}
```

## 📈 Performance Metrics

### Key Performance Indicators

```typescript
// Performance benchmarks
const performanceBenchmarks = {
  // Page load times (target < 3 seconds)
  pageLoad: {
    excellent: 1000,    // < 1s
    good: 2500,         // < 2.5s
    needsImprovement: 4000  // < 4s
  },

  // First Contentful Paint (target < 1.8s)
  fcp: {
    excellent: 1800,
    good: 3000,
    needsImprovement: 4000
  },

  // Largest Contentful Paint (target < 2.5s)
  lcp: {
    excellent: 2500,
    good: 4000,
    needsImprovement: 6000
  },

  // First Input Delay (target < 100ms)
  fid: {
    excellent: 100,
    good: 300,
    needsImprovement: 500
  },

  // Cumulative Layout Shift (target < 0.1)
  cls: {
    excellent: 0.1,
    good: 0.25,
    needsImprovement: 0.5
  },

  // Cache hit rates (target > 80%)
  cacheHitRate: {
    excellent: 90,
    good: 80,
    needsImprovement: 60
  }
}

// Performance scoring
const calculatePerformanceScore = (metrics: PerformanceMetrics) => {
  const scores = {
    fcp: getScore(metrics.fcp, performanceBenchmarks.fcp),
    lcp: getScore(metrics.lcp, performanceBenchmarks.lcp),
    fid: getScore(metrics.fid, performanceBenchmarks.fid),
    cls: getScore(metrics.cls, performanceBenchmarks.cls, true) // Lower is better
  }

  // Weighted average
  const weights = { fcp: 0.2, lcp: 0.3, fid: 0.3, cls: 0.2 }
  const totalScore = Object.entries(scores).reduce((total, [key, score]) => {
    return total + (score * weights[key])
  }, 0)

  return Math.round(totalScore)
}
```

---

**Next**: Learn about [Security Best Practices](security-best-practices.md) for production deployment.
