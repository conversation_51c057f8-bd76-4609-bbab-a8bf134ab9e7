#!/usr/bin/env node

/**
 * Database Migration Script
 * Applies all pending migrations to the Supabase database
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

/**
 * Read and execute SQL file
 */
async function executeSqlFile(filePath) {
  try {
    const sql = fs.readFileSync(filePath, 'utf8');
    console.log(`📄 Executing: ${path.basename(filePath)}`);
    
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error(`❌ Error in ${path.basename(filePath)}:`, error);
      return false;
    }
    
    console.log(`✅ Successfully executed: ${path.basename(filePath)}`);
    return true;
  } catch (err) {
    console.error(`❌ Failed to read/execute ${filePath}:`, err.message);
    return false;
  }
}

/**
 * Apply all migrations in order
 */
async function applyMigrations() {
  console.log('🚀 Starting database migration process...\n');
  
  const migrationsDir = path.join(__dirname, '../migrations');
  const functionsDir = path.join(__dirname, '../functions');
  const policiesDir = path.join(__dirname, '../policies');
  
  try {
    // 1. Apply migrations
    console.log('📁 Applying migrations...');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    for (const file of migrationFiles) {
      const success = await executeSqlFile(path.join(migrationsDir, file));
      if (!success) {
        console.error('❌ Migration failed, stopping process');
        process.exit(1);
      }
    }
    
    // 2. Apply functions
    console.log('\n📁 Applying functions...');
    const functionFiles = fs.readdirSync(functionsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    for (const file of functionFiles) {
      const success = await executeSqlFile(path.join(functionsDir, file));
      if (!success) {
        console.error('❌ Function creation failed, stopping process');
        process.exit(1);
      }
    }
    
    // 3. Apply policies
    console.log('\n📁 Applying RLS policies...');
    const policyFiles = fs.readdirSync(policiesDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    for (const file of policyFiles) {
      const success = await executeSqlFile(path.join(policiesDir, file));
      if (!success) {
        console.error('❌ Policy creation failed, stopping process');
        process.exit(1);
      }
    }
    
    console.log('\n🎉 All migrations applied successfully!');
    
  } catch (err) {
    console.error('❌ Migration process failed:', err.message);
    process.exit(1);
  }
}

// Run migrations
applyMigrations();
