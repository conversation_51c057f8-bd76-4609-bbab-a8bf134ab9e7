import { NextResponse } from 'next/server';
import { ensureLessonsTableSchema } from '@/lib/migrations/update-lessons-schema';

/**
 * API route to check and apply necessary database migrations
 * Can be called during app initialization
 */
export async function GET() {
  try {
    // Check and apply lessons table migration if needed
    const lessonsMigrationResult = await ensureLessonsTableSchema();
    
    if (!lessonsMigrationResult.success) {
      console.error('Error applying lessons schema migration:', lessonsMigrationResult.error);
      return NextResponse.json(
        { success: false, error: lessonsMigrationResult.error },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Database schema is up to date' 
    });
  } catch (error) {
    console.error('Error in setup route:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error during setup' 
      },
      { status: 500 }
    );
  }
} 