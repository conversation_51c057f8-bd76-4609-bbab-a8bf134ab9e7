'use client';

import { useState, useEffect } from 'react';
import { clearAllAuthCache, quickLogoutAndClear } from '@/lib/clear-auth-cache';

export default function ClearCachePage() {
  const [isClearing, setIsClearing] = useState(false);
  const [status, setStatus] = useState<string[]>([]);
  const [autoStart, setAutoStart] = useState(false);

  // Check if we should auto-start clearing
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('auto') === 'true') {
      setAutoStart(true);
      handleClearCache();
    }
  }, []);

  const addStatus = (message: string) => {
    setStatus(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleClearCache = async () => {
    setIsClearing(true);
    setStatus([]);
    
    addStatus('🧹 Starting comprehensive cache clearing...');
    
    try {
      await clearAllAuthCache();
      addStatus('✅ All caches cleared successfully!');
      addStatus('🔄 Redirecting to login page...');
    } catch (error) {
      addStatus(`❌ Error: ${error}`);
      addStatus('🔄 Redirecting to login page anyway...');
    }
  };

  const handleQuickLogout = async () => {
    setIsClearing(true);
    setStatus([]);
    
    addStatus('⚡ Quick logout and cache clear...');
    
    try {
      await quickLogoutAndClear();
    } catch (error) {
      addStatus(`❌ Error: ${error}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Clear Authentication Cache
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Clear all cached credentials and authentication data
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          
          {!autoStart && (
            <div className="space-y-4">
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                <div className="flex">
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      <strong>This will clear:</strong>
                    </p>
                    <ul className="mt-2 text-sm text-yellow-700 list-disc list-inside">
                      <li>Supabase authentication session</li>
                      <li>Browser localStorage & sessionStorage</li>
                      <li>Authentication cookies</li>
                      <li>Application caches</li>
                      <li>Rate limiting data</li>
                      <li>Browser cache & service workers</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <button
                  onClick={handleClearCache}
                  disabled={isClearing}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                >
                  {isClearing ? 'Clearing...' : '🧹 Clear All Cache & Logout'}
                </button>

                <button
                  onClick={handleQuickLogout}
                  disabled={isClearing}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
                >
                  {isClearing ? 'Processing...' : '⚡ Quick Logout & Clear'}
                </button>

                <a
                  href="/login"
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  🔙 Back to Login
                </a>
              </div>
            </div>
          )}

          {/* Status Display */}
          {status.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Status:</h3>
              <div className="bg-gray-100 rounded-md p-4 max-h-64 overflow-y-auto">
                {status.map((message, index) => (
                  <div key={index} className="text-sm text-gray-700 mb-1 font-mono">
                    {message}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Auto-start message */}
          {autoStart && (
            <div className="text-center">
              <div className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-indigo-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Auto-clearing cache...
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              After clearing, you'll be redirected to the login page where you can sign <NAME_EMAIL>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
