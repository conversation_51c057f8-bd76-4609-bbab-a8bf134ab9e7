'use client'

import { useState, useEffect } from "react"
import { Calendar, ChevronLeft, ChevronRight, Clock, MapPin, MoreHorizontal, Plus, Video, User, CheckCircle2, AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { useToast } from "@/components/ui/use-toast"

interface Interview {
  id: string
  scheduled_at?: string
  duration_minutes: number
  status: 'scheduled' | 'completed' | 'cancelled'
  notes?: string
  meeting_link?: string
  feedback?: {
    interview_type?: string
    proposed_times?: Array<{
      startTime: string
      endTime: string
      date: string
    }>
    invitation_message?: string
    prospect_response?: {
      selected_time?: string
      prospect_notes?: string
      responded_at?: string
      declined?: boolean
      decline_reason?: string
    }
  }
  applications: {
    job_postings: {
      title: string
      bpos: {
        name: string
        logo_url?: string
      }
    }
  }
}

export function InterviewsPage() {
  const [interviews, setInterviews] = useState<Interview[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(null)
  const [showAcceptDialog, setShowAcceptDialog] = useState(false)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState('')
  const [prospectNotes, setProspectNotes] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [loadingStates, setLoadingStates] = useState<{[key: string]: boolean}>({})
  const { toast } = useToast()

  useEffect(() => {
    fetchInterviews()
  }, [])

  const fetchInterviews = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/interviews/schedule')

      if (!response.ok) {
        throw new Error('Failed to fetch interviews')
      }

      const data = await response.json()
      setInterviews(data.interviews || [])
    } catch (error) {
      console.error('Error fetching interviews:', error)
      toast({
        title: "Error loading interviews",
        description: "There was a problem loading your interviews. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleAcceptInterview = async () => {
    if (!selectedInterview || !selectedTimeSlot) return

    try {
      setIsSubmitting(true)

      const response = await fetch('/api/interviews/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interviewId: selectedInterview.id,
          selectedTime: selectedTimeSlot,
          prospectNotes: prospectNotes
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to accept interview')
      }

      const selectedTime = new Date(selectedTimeSlot)
      toast({
        title: "Interview accepted!",
        description: `Your interview has been scheduled for ${selectedTime.toLocaleDateString()} at ${selectedTime.toLocaleTimeString()}.`,
      })

      setShowAcceptDialog(false)
      setSelectedInterview(null)
      setSelectedTimeSlot('')
      setProspectNotes('')
      fetchInterviews() // Refresh the list

    } catch (error: any) {
      console.error('Error accepting interview:', error)
      toast({
        title: "Failed to accept interview",
        description: error.message || "There was an error accepting your interview. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeclineInterview = async (interview: Interview, reason: string) => {
    try {
      setLoadingStates(prev => ({ ...prev, [`decline_${interview.id}`]: true }))

      const response = await fetch('/api/interviews/accept', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interviewId: interview.id,
          reason: reason
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to decline interview')
      }

      toast({
        title: "Interview declined",
        description: "The interview invitation has been declined.",
      })

      fetchInterviews() // Refresh the list

    } catch (error: any) {
      console.error('Error declining interview:', error)
      toast({
        title: "Failed to decline interview",
        description: error.message || "There was an error declining the interview. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoadingStates(prev => ({ ...prev, [`decline_${interview.id}`]: false }))
    }
  }

  const openAcceptDialog = (interview: Interview) => {
    setSelectedInterview(interview)
    setShowAcceptDialog(true)
  }

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return {
      date: date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }

  const getStatusBadge = (interview: Interview) => {
    // Check if interview needs response
    if (interview.status === 'scheduled' && !interview.feedback?.prospect_response) {
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
          <AlertCircle className="h-3 w-3 mr-1" />
          Awaiting Response
        </Badge>
      )
    }

    switch (interview.status) {
      case 'scheduled':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Scheduled
          </Badge>
        )
      case 'completed':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Completed
          </Badge>
        )
      case 'cancelled':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Cancelled
          </Badge>
        )
      default:
        return <Badge variant="outline">{interview.status}</Badge>
    }
  }

  const isPendingResponse = (interview: Interview) => {
    return interview.status === 'scheduled' && !interview.feedback?.prospect_response
  }

  const formatTimeSlot = (timeSlot: { startTime: string; endTime: string; date: string }) => {
    const start = new Date(timeSlot.startTime)
    const end = new Date(timeSlot.endTime)
    return `${start.toLocaleDateString()} at ${start.toLocaleTimeString()} - ${end.toLocaleTimeString()}`
  }

  if (loading) {
    return (
      <div className="space-y-8 w-full">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Interviews</h1>
          <p className="text-muted-foreground">Manage your upcoming interviews and practice sessions</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      </div>
    )
  }
  const pendingInterviews = interviews.filter(i => isPendingResponse(i))
  const scheduledInterviews = interviews.filter(i => i.status === 'scheduled' && i.feedback?.prospect_response)
  const completedInterviews = interviews.filter(i => i.status === 'completed')

  return (
    <div className="space-y-8 w-full">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Interviews</h1>
        <p className="text-muted-foreground">Manage your upcoming interviews and practice sessions</p>
      </div>

      {/* Pending Interview Invitations */}
      {pendingInterviews.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Interview Invitations</h2>
            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
              {pendingInterviews.length} Pending
            </Badge>
          </div>

          <div className="grid gap-4">
            {pendingInterviews.map((interview) => (
              <Card key={interview.id} className="border-amber-200 bg-amber-50/30">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-lg">{interview.applications.job_postings.title}</CardTitle>
                      <CardDescription className="flex items-center gap-2">
                        <span>{interview.applications.job_postings.bpos.name}</span>
                        {getStatusBadge(interview)}
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      {interview.applications.job_postings.bpos.logo_url && (
                        <img
                          src={interview.applications.job_postings.bpos.logo_url}
                          alt={interview.applications.job_postings.bpos.name}
                          className="w-8 h-8 rounded object-cover"
                        />
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {interview.feedback?.invitation_message && (
                    <div className="p-3 bg-white rounded-lg border">
                      <p className="text-sm text-gray-700">{interview.feedback.invitation_message}</p>
                    </div>
                  )}

                  {interview.feedback?.proposed_times && interview.feedback.proposed_times.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Proposed Times:</h4>
                      <div className="space-y-1">
                        {interview.feedback.proposed_times.map((timeSlot, index) => (
                          <div key={index} className="text-sm text-gray-600 p-2 bg-gray-50 rounded">
                            {formatTimeSlot(timeSlot)}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      className="flex-1"
                      onClick={() => openAcceptDialog(interview)}
                    >
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      Accept Interview
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeclineInterview(interview, 'Not available')}
                      disabled={loadingStates[`decline_${interview.id}`]}
                    >
                      {loadingStates[`decline_${interview.id}`] ? 'Declining...' : 'Decline'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Upcoming Scheduled Interviews */}
      {scheduledInterviews.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Upcoming Interviews</h2>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              {scheduledInterviews.length} Scheduled
            </Badge>
          </div>

          <div className="grid gap-4">
            {scheduledInterviews.map((interview) => {
              const dateTime = interview.scheduled_at ? formatDateTime(interview.scheduled_at) : null

              return (
                <Card key={interview.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <CardTitle className="text-lg">{interview.applications.job_postings.title}</CardTitle>
                        <CardDescription className="flex items-center gap-2">
                          <span>{interview.applications.job_postings.bpos.name}</span>
                          {getStatusBadge(interview)}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        {interview.applications.job_postings.bpos.logo_url && (
                          <img
                            src={interview.applications.job_postings.bpos.logo_url}
                            alt={interview.applications.job_postings.bpos.name}
                            className="w-8 h-8 rounded object-cover"
                          />
                        )}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>Reschedule</DropdownMenuItem>
                            <DropdownMenuItem>Cancel</DropdownMenuItem>
                            <DropdownMenuItem>View Details</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {dateTime && (
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{dateTime.date}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>{dateTime.time} ({interview.duration_minutes || 60} min)</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Video className="h-4 w-4 text-muted-foreground" />
                          <span>Video Call</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>Remote</span>
                        </div>
                      </div>
                    )}
                    <div className="flex gap-2">
                      {interview.meeting_link ? (
                        <Button size="sm" className="flex-1" asChild>
                          <a href={interview.meeting_link} target="_blank" rel="noopener noreferrer">
                            <Video className="h-4 w-4 mr-2" />
                            Join Meeting
                          </a>
                        </Button>
                      ) : (
                        <Button size="sm" className="flex-1" disabled>
                          <Video className="h-4 w-4 mr-2" />
                          Meeting Link Pending
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        Prepare
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      )}

      {/* Empty State */}
      {interviews.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No interviews yet</h3>
            <p className="text-muted-foreground mb-4">
              When you apply for jobs and get selected, interview invitations will appear here.
            </p>
            <Button asChild>
              <a href="/prospect/job-board">Browse Jobs</a>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Interview Preparation Resources */}
      <Card>
        <CardHeader>
          <CardTitle>Interview Preparation</CardTitle>
          <CardDescription>Resources to help you prepare for your interviews</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100">
                    <Calendar className="h-5 w-5 text-green-700" />
                  </div>
                  <div>
                    <CardTitle>TechSupport Inc. Interview</CardTitle>
                    <CardDescription>Customer Service Representative</CardDescription>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">Actions</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>Reschedule</DropdownMenuItem>
                    <DropdownMenuItem>Cancel Interview</DropdownMenuItem>
                    <DropdownMenuItem>Add to Calendar</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Tomorrow, 10:00 AM - 11:00 AM</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Video className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Video Interview (Zoom)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Remote</span>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Interviewers</h4>
                  <div className="mt-2 flex items-center gap-2">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                      <span className="text-xs font-medium text-blue-700">JD</span>
                    </div>
                    <div className="text-sm">
                      <p className="font-medium">Jane Doe</p>
                      <p className="text-muted-foreground">HR Manager</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-wrap gap-2">
              <Button>Join Interview</Button>
              <Button variant="outline">Prepare with AI</Button>
              <Button variant="outline">View Job Details</Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100">
                    <Calendar className="h-5 w-5 text-amber-700" />
                  </div>
                  <div>
                    <CardTitle>Global BPO Services Interview</CardTitle>
                    <CardDescription>Technical Support Specialist</CardDescription>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">Actions</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>Reschedule</DropdownMenuItem>
                    <DropdownMenuItem>Cancel Interview</DropdownMenuItem>
                    <DropdownMenuItem>Add to Calendar</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">May 20, 2:30 PM - 3:30 PM</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Global BPO Office, New York</span>
                  </div>
                  <Badge className="mt-1 bg-blue-100 text-blue-800 hover:bg-blue-100">In-person Interview</Badge>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Interviewers</h4>
                  <div className="mt-2 flex items-center gap-2">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                      <span className="text-xs font-medium text-blue-700">MS</span>
                    </div>
                    <div className="text-sm">
                      <p className="font-medium">Michael Smith</p>
                      <p className="text-muted-foreground">Technical Team Lead</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-wrap gap-2">
              <Button variant="outline">Get Directions</Button>
              <Button variant="outline">Prepare with AI</Button>
              <Button variant="outline">View Job Details</Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-purple-100">
                    <Video className="h-5 w-5 text-purple-700" />
                  </div>
                  <div>
                    <CardTitle>AI Practice Interview</CardTitle>
                    <CardDescription>Customer Service Scenarios</CardDescription>
                  </div>
                </div>
                <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Practice Session</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Today, 4:00 PM - 5:00 PM</span>
                </div>
                <p className="text-sm">
                  Practice common customer service interview questions and scenarios with our AI assistant. Receive
                  feedback on your responses and improve your interview skills.
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button>Start Practice Session</Button>
            </CardFooter>
          </Card>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Interview Preparation</CardTitle>
          <CardDescription>Resources to help you prepare for your interviews</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg border p-4">
            <h3 className="font-medium">Common Interview Questions</h3>
            <p className="mt-2 text-sm">Practice answering these frequently asked questions in BPO interviews:</p>
            <ul className="mt-2 list-disc pl-5 text-sm space-y-1">
              <li>Tell me about a time you dealt with a difficult customer.</li>
              <li>How do you handle high-pressure situations?</li>
              <li>What makes you a good fit for customer service?</li>
              <li>How do you prioritize tasks when you have multiple responsibilities?</li>
              <li>Describe your experience with CRM software.</li>
            </ul>
            <Button variant="outline" size="sm" className="mt-3">
              Practice with AI
            </Button>
          </div>

          <div className="rounded-lg border p-4">
            <h3 className="font-medium">Interview Tips</h3>
            <ul className="mt-2 list-disc pl-5 text-sm space-y-1">
              <li>Research the company before your interview</li>
              <li>Prepare examples of your customer service experience</li>
              <li>Dress professionally, even for video interviews</li>
              <li>Test your technology before video interviews</li>
              <li>Prepare questions to ask the interviewer</li>
              <li>Follow up with a thank-you email after the interview</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Accept Interview Dialog */}
      <Dialog open={showAcceptDialog} onOpenChange={setShowAcceptDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5 text-green-600" />
              Accept Interview Invitation
            </DialogTitle>
            <DialogDescription>
              {selectedInterview && (
                <>Select your preferred time for the interview with {selectedInterview.applications.job_postings.bpos.name} for the {selectedInterview.applications.job_postings.title} position.</>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {selectedInterview?.feedback?.proposed_times && selectedInterview.feedback.proposed_times.length > 0 ? (
              <div className="space-y-2">
                <Label>Select Preferred Time</Label>
                <Select value={selectedTimeSlot} onValueChange={setSelectedTimeSlot}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a time slot" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedInterview.feedback.proposed_times.map((timeSlot, index) => (
                      <SelectItem key={index} value={timeSlot.startTime}>
                        {formatTimeSlot(timeSlot)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <div className="text-sm text-muted-foreground p-3 bg-amber-50 rounded-lg border border-amber-200">
                No specific time slots were proposed. The interviewer will contact you to arrange a suitable time.
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="prospect-notes">Additional Notes (Optional)</Label>
              <Input
                id="prospect-notes"
                placeholder="Any questions or special requirements..."
                value={prospectNotes}
                onChange={(e) => setProspectNotes(e.target.value)}
              />
            </div>

            <div className="text-sm text-muted-foreground">
              Duration: {selectedInterview?.duration_minutes || 60} minutes
            </div>
          </div>

          <DialogFooter className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setShowAcceptDialog(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAcceptInterview}
              disabled={isSubmitting || (selectedInterview?.feedback?.proposed_times?.length > 0 && !selectedTimeSlot)}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? "Accepting..." : "Accept Interview"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
