import { NextResponse } from 'next/server';
import { AppError, ErrorType, createError, logError } from '@/lib/utils';

/**
 * Standard API error response format
 */
export interface ApiErrorResponse {
  success: false;
  error: {
    type: ErrorType;
    message: string;
    userMessage: string;
    code?: string;
    requestId: string;
    timestamp: string;
    details?: any;
  };
}

/**
 * Standard API success response format
 */
export interface ApiSuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
  requestId: string;
  timestamp: string;
}

/**
 * Create standardized error response for API routes
 */
export function createApiErrorResponse(
  error: AppError,
  status: number = 500
): NextResponse<ApiErrorResponse> {
  const response: ApiErrorResponse = {
    success: false,
    error: {
      type: error.type,
      message: error.message,
      userMessage: error.userMessage,
      code: error.code,
      requestId: error.requestId!,
      timestamp: error.timestamp,
      details: process.env.NODE_ENV === 'development' ? error.details : undefined
    }
  };

  return NextResponse.json(response, { status });
}

/**
 * Create standardized success response for API routes
 */
export function createApiSuccessResponse<T>(
  data: T,
  message?: string,
  status: number = 200
): NextResponse<ApiSuccessResponse<T>> {
  const response: ApiSuccessResponse<T> = {
    success: true,
    data,
    message,
    requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    timestamp: new Date().toISOString()
  };

  return NextResponse.json(response, { status });
}

/**
 * Handle errors in API routes with consistent formatting
 */
export function handleApiError(
  error: any,
  context?: string,
  customUserMessage?: string
): NextResponse<ApiErrorResponse> {
  let appError: AppError;
  let status = 500;

  // Handle different error types
  if (error?.code && (error?.message || error?.details)) {
    // Supabase error
    appError = handleSupabaseApiError(error, context);
    status = getStatusFromErrorType(appError.type);
  } else if (error instanceof Error) {
    // Standard JavaScript error
    appError = createError(
      ErrorType.SERVER_ERROR,
      error.message,
      customUserMessage || 'An internal server error occurred',
      { originalError: error.message, stack: error.stack, context }
    );
  } else {
    // Unknown error type
    appError = createError(
      ErrorType.UNKNOWN,
      String(error),
      customUserMessage || 'An unexpected error occurred',
      { originalError: error, context }
    );
  }

  // Override user message if provided
  if (customUserMessage) {
    appError.userMessage = customUserMessage;
  }

  // Log the error
  logError(appError, context);

  return createApiErrorResponse(appError, status);
}

/**
 * Handle Supabase errors specifically for API routes
 */
function handleSupabaseApiError(error: any, context?: string): AppError {
  let errorType = ErrorType.DATABASE;
  let userMessage = '';

  // Handle specific Supabase error codes
  if (error?.code) {
    switch (error.code) {
      case 'PGRST301':
      case '42501':
        errorType = ErrorType.AUTHORIZATION;
        userMessage = 'You don\'t have permission to access this resource.';
        break;
      case 'PGRST116':
        errorType = ErrorType.NOT_FOUND;
        userMessage = 'The requested resource was not found.';
        break;
      case '23505':
        errorType = ErrorType.VALIDATION;
        userMessage = 'This record already exists.';
        break;
      case '23503':
        errorType = ErrorType.VALIDATION;
        userMessage = 'Cannot delete this record because it\'s being used elsewhere.';
        break;
      case '23502':
        errorType = ErrorType.VALIDATION;
        userMessage = 'Required field is missing.';
        break;
      case '22001':
        errorType = ErrorType.VALIDATION;
        userMessage = 'Input value is too long.';
        break;
      case 'PGRST102':
        errorType = ErrorType.VALIDATION;
        userMessage = 'Invalid request format.';
        break;
      default:
        errorType = ErrorType.DATABASE;
        userMessage = 'A database error occurred. Please try again.';
    }
  }

  return createError(
    errorType,
    `Supabase error [${error?.code}]: ${error?.message || 'Unknown database error'}`,
    userMessage,
    { 
      supabaseError: {
        code: error?.code,
        message: error?.message,
        details: error?.details,
        hint: error?.hint
      }, 
      context 
    }
  );
}

/**
 * Get appropriate HTTP status code from error type
 */
function getStatusFromErrorType(errorType: ErrorType): number {
  switch (errorType) {
    case ErrorType.AUTHENTICATION:
      return 401;
    case ErrorType.AUTHORIZATION:
      return 403;
    case ErrorType.VALIDATION:
      return 400;
    case ErrorType.NOT_FOUND:
      return 404;
    case ErrorType.RATE_LIMIT:
      return 429;
    case ErrorType.DATABASE:
    case ErrorType.SERVER_ERROR:
      return 500;
    case ErrorType.NETWORK:
      return 502;
    case ErrorType.FILE_UPLOAD:
      return 400;
    default:
      return 500;
  }
}

/**
 * Wrapper for API route handlers with automatic error handling
 */
export function withApiErrorHandler<T>(
  handler: () => Promise<T>,
  context?: string
) {
  return async (): Promise<NextResponse> => {
    try {
      const result = await handler();
      return createApiSuccessResponse(result);
    } catch (error) {
      return handleApiError(error, context);
    }
  };
}

/**
 * Validate request body and handle validation errors
 */
export function validateRequestBody<T>(
  body: any,
  requiredFields: string[],
  context?: string
): T {
  if (!body) {
    throw createError(
      ErrorType.VALIDATION,
      'Request body is required',
      'Request body is missing',
      { context, requiredFields }
    );
  }

  const missingFields = requiredFields.filter(field => 
    body[field] === undefined || body[field] === null || body[field] === ''
  );

  if (missingFields.length > 0) {
    throw createError(
      ErrorType.VALIDATION,
      `Missing required fields: ${missingFields.join(', ')}`,
      `Please provide: ${missingFields.join(', ')}`,
      { context, missingFields, providedFields: Object.keys(body) }
    );
  }

  return body as T;
}

/**
 * Handle file upload errors
 */
export function handleFileUploadError(error: any, context?: string): AppError {
  let userMessage = 'File upload failed. Please try again.';
  
  if (error?.message) {
    if (error.message.includes('size')) {
      userMessage = 'File is too large. Please choose a smaller file.';
    } else if (error.message.includes('type') || error.message.includes('format')) {
      userMessage = 'File type not supported. Please choose a different file.';
    } else if (error.message.includes('network')) {
      userMessage = 'Network error during upload. Please check your connection.';
    }
  }

  return createError(
    ErrorType.FILE_UPLOAD,
    `File upload error: ${error?.message || 'Unknown upload error'}`,
    userMessage,
    { uploadError: error, context }
  );
}

/**
 * Rate limiting error handler
 */
export function createRateLimitError(
  limit: number,
  windowMs: number,
  context?: string
): AppError {
  const windowMinutes = Math.ceil(windowMs / 60000);
  
  return createError(
    ErrorType.RATE_LIMIT,
    `Rate limit exceeded: ${limit} requests per ${windowMinutes} minutes`,
    `Too many requests. Please wait ${windowMinutes} minutes and try again.`,
    { limit, windowMs, context }
  );
}
