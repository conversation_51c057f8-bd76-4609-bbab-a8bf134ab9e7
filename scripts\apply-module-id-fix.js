const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyModuleIdFix() {
  console.log('🔧 Applying Module ID Fix Migration...\n');

  try {
    // 1. Apply the fix directly
    console.log('1️⃣ Making module_id column nullable...');
    
    const { error: alterError } = await supabase.rpc('sql', {
      query: 'ALTER TABLE public.files ALTER COLUMN module_id DROP NOT NULL;'
    }).catch(async () => {
      // Try alternative approach
      const { data, error } = await supabase
        .from('_sql')
        .insert({ sql: 'ALTER TABLE public.files ALTER COLUMN module_id DROP NOT NULL;' });
      return { error };
    });

    if (alterError) {
      console.log('⚠️  Direct SQL execution not available. Applying fix manually...');
      
      // Manual approach - check current schema first
      const { data: schemaCheck } = await supabase
        .from('information_schema.columns')
        .select('column_name, is_nullable')
        .eq('table_schema', 'public')
        .eq('table_name', 'files')
        .eq('column_name', 'module_id')
        .single();

      if (schemaCheck && schemaCheck.is_nullable === 'NO') {
        console.log('❌ module_id is currently NOT NULL');
        console.log('💡 Please run this SQL manually in Supabase SQL Editor:');
        console.log('   ALTER TABLE public.files ALTER COLUMN module_id DROP NOT NULL;');
        return;
      } else if (schemaCheck && schemaCheck.is_nullable === 'YES') {
        console.log('✅ module_id is already nullable');
      }
    } else {
      console.log('✅ Successfully made module_id nullable');
    }

    // 2. Test the fix
    console.log('\n2️⃣ Testing file insert with null module_id...');
    
    const { data: prospects } = await supabase
      .from('prospects')
      .select('id')
      .limit(1);

    if (!prospects || prospects.length === 0) {
      console.log('❌ No prospects found for testing');
      return;
    }

    const testData = {
      prospect_id: prospects[0].id,
      module_id: null, // This should now work
      file_type: 'document',
      file_category: 'uploaded',
      title: 'Test Upload After Fix',
      file_url: 'https://example.com/test-fix.pdf',
      original_filename: 'test-fix.pdf',
      file_size: 12345,
      mime_type: 'application/pdf',
      issued_at: new Date().toISOString()
    };

    const { data: testResult, error: testError } = await supabase
      .from('files')
      .insert(testData)
      .select()
      .single();

    if (testError) {
      console.error('❌ Test insert failed:', testError);
      console.log('💡 The database schema may still need manual fixing');
    } else {
      console.log('✅ Test insert successful! Upload should now work.');
      
      // Clean up test record
      await supabase.from('files').delete().eq('id', testResult.id);
      console.log('🧹 Test record cleaned up');
    }

    // 3. Verify schema
    console.log('\n3️⃣ Verifying final schema...');
    const { data: finalSchema } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_schema', 'public')
      .eq('table_name', 'files')
      .in('column_name', ['module_id', 'prospect_id', 'file_type', 'title', 'file_url']);

    console.log('📋 Key columns schema:');
    finalSchema?.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });

  } catch (error) {
    console.error('❌ Fix failed:', error);
    console.log('\n💡 Manual steps to fix:');
    console.log('1. Go to Supabase SQL Editor');
    console.log('2. Run: ALTER TABLE public.files ALTER COLUMN module_id DROP NOT NULL;');
    console.log('3. Try the upload again');
  }
}

// Run the fix
applyModuleIdFix();
