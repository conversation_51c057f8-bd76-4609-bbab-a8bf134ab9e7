-- Complete Empty Tables Migration
-- Adds proper column definitions for tables that currently have no structure

-- =============================================================================
-- APPLICATIONS TABLE
-- =============================================================================

-- Drop and recreate applications table with proper structure
DROP TABLE IF EXISTS public.applications CASCADE;

CREATE TABLE public.applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL,
    prospect_id UUID NOT NULL,
    status application_status DEFAULT 'submitted',
    cover_letter TEXT,
    additional_notes TEXT,
    resume_url TEXT,
    portfolio_url TEXT,
    expected_salary_min INTEGER,
    expected_salary_max INTEGER,
    availability_start_date DATE,
    is_remote_preferred BOOLEAN DEFAULT FALSE,
    submitted_at TIMESTAMPTZ DEFAULT NOW(),
    reviewed_at TIMESTAMPTZ,
    reviewed_by UUID,
    rejection_reason TEXT,
    interview_scheduled_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key constraints for applications
ALTER TABLE public.applications 
ADD CONSTRAINT fk_applications_job_id 
FOREIGN KEY (job_id) REFERENCES public.job_postings(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.applications 
ADD CONSTRAINT fk_applications_prospect_id 
FOREIGN KEY (prospect_id) REFERENCES public.prospects(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.applications 
ADD CONSTRAINT fk_applications_reviewed_by 
FOREIGN KEY (reviewed_by) REFERENCES public.users(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Add unique constraint to prevent duplicate applications
ALTER TABLE public.applications 
ADD CONSTRAINT uk_applications_job_prospect 
UNIQUE (job_id, prospect_id);

-- Add check constraints
ALTER TABLE public.applications 
ADD CONSTRAINT chk_applications_salary_range 
CHECK (expected_salary_min <= expected_salary_max);

-- Add indexes for applications
CREATE INDEX idx_applications_job_id ON public.applications(job_id);
CREATE INDEX idx_applications_prospect_id ON public.applications(prospect_id);
CREATE INDEX idx_applications_status ON public.applications(status);
CREATE INDEX idx_applications_submitted_at ON public.applications(submitted_at);

-- Add update timestamp trigger
CREATE TRIGGER update_applications_timestamp
BEFORE UPDATE ON public.applications
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- =============================================================================
-- INTERVIEWS TABLE
-- =============================================================================

-- Drop and recreate interviews table with proper structure
DROP TABLE IF EXISTS public.interviews CASCADE;

CREATE TABLE public.interviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_id UUID NOT NULL,
    interviewer_id UUID NOT NULL,
    scheduled_at TIMESTAMPTZ NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    interview_type VARCHAR(50) DEFAULT 'video_call',
    meeting_url TEXT,
    meeting_id TEXT,
    status interview_status DEFAULT 'scheduled',
    notes TEXT,
    score INTEGER,
    feedback TEXT,
    recommendation TEXT,
    conducted_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    cancelled_reason TEXT,
    rescheduled_from UUID, -- Reference to previous interview if rescheduled
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key constraints for interviews
ALTER TABLE public.interviews 
ADD CONSTRAINT fk_interviews_application_id 
FOREIGN KEY (application_id) REFERENCES public.applications(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.interviews 
ADD CONSTRAINT fk_interviews_interviewer_id 
FOREIGN KEY (interviewer_id) REFERENCES public.users(id) 
ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE public.interviews 
ADD CONSTRAINT fk_interviews_rescheduled_from 
FOREIGN KEY (rescheduled_from) REFERENCES public.interviews(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Add check constraints
ALTER TABLE public.interviews 
ADD CONSTRAINT chk_interviews_duration_positive 
CHECK (duration_minutes > 0);

ALTER TABLE public.interviews 
ADD CONSTRAINT chk_interviews_score_range 
CHECK (score IS NULL OR (score >= 0 AND score <= 100));

ALTER TABLE public.interviews 
ADD CONSTRAINT chk_interviews_scheduled_future 
CHECK (scheduled_at > created_at);

-- Add indexes for interviews
CREATE INDEX idx_interviews_application_id ON public.interviews(application_id);
CREATE INDEX idx_interviews_interviewer_id ON public.interviews(interviewer_id);
CREATE INDEX idx_interviews_status ON public.interviews(status);
CREATE INDEX idx_interviews_scheduled_at ON public.interviews(scheduled_at);

-- Add update timestamp trigger
CREATE TRIGGER update_interviews_timestamp
BEFORE UPDATE ON public.interviews
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- =============================================================================
-- CERTIFICATES TABLE
-- =============================================================================

-- Drop and recreate certificates table with proper structure
DROP TABLE IF EXISTS public.certificates CASCADE;

CREATE TABLE public.certificates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    prospect_id UUID NOT NULL,
    module_id UUID,
    assessment_id UUID,
    certificate_type VARCHAR(50) NOT NULL, -- 'module_completion', 'assessment_pass', 'course_completion'
    title VARCHAR(255) NOT NULL,
    description TEXT,
    issued_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    certificate_url TEXT, -- URL to generated certificate PDF
    verification_code VARCHAR(100) UNIQUE,
    is_verified BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key constraints for certificates
ALTER TABLE public.certificates 
ADD CONSTRAINT fk_certificates_prospect_id 
FOREIGN KEY (prospect_id) REFERENCES public.prospects(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.certificates 
ADD CONSTRAINT fk_certificates_module_id 
FOREIGN KEY (module_id) REFERENCES public.training_modules(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE public.certificates 
ADD CONSTRAINT fk_certificates_assessment_id 
FOREIGN KEY (assessment_id) REFERENCES public.assessments(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Add check constraints
ALTER TABLE public.certificates 
ADD CONSTRAINT chk_certificates_type_valid 
CHECK (certificate_type IN ('module_completion', 'assessment_pass', 'course_completion'));

-- Add indexes for certificates
CREATE INDEX idx_certificates_prospect_id ON public.certificates(prospect_id);
CREATE INDEX idx_certificates_module_id ON public.certificates(module_id);
CREATE INDEX idx_certificates_assessment_id ON public.certificates(assessment_id);
CREATE INDEX idx_certificates_type ON public.certificates(certificate_type);
CREATE INDEX idx_certificates_verification_code ON public.certificates(verification_code);
CREATE INDEX idx_certificates_issued_at ON public.certificates(issued_at);

-- Add update timestamp trigger
CREATE TRIGGER update_certificates_timestamp
BEFORE UPDATE ON public.certificates
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- =============================================================================
-- AI_CONVERSATIONS TABLE
-- =============================================================================

-- Drop and recreate ai_conversations table with proper structure
DROP TABLE IF EXISTS public.ai_conversations CASCADE;

CREATE TABLE public.ai_conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    conversation_type VARCHAR(50) NOT NULL, -- 'career_guidance', 'interview_prep', 'skill_assessment', 'general_support'
    title VARCHAR(255),
    messages JSONB NOT NULL DEFAULT '[]',
    context JSONB DEFAULT '{}', -- Additional context like current module, assessment, etc.
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'completed', 'archived'
    total_messages INTEGER DEFAULT 0,
    last_message_at TIMESTAMPTZ DEFAULT NOW(),
    ai_model_used VARCHAR(50) DEFAULT 'gpt-3.5-turbo',
    tokens_used INTEGER DEFAULT 0,
    cost_usd DECIMAL(10,4) DEFAULT 0.0000,
    satisfaction_rating INTEGER, -- 1-5 rating from user
    feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key constraints for ai_conversations
ALTER TABLE public.ai_conversations 
ADD CONSTRAINT fk_ai_conversations_user_id 
FOREIGN KEY (user_id) REFERENCES public.users(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Add check constraints
ALTER TABLE public.ai_conversations 
ADD CONSTRAINT chk_ai_conversations_type_valid 
CHECK (conversation_type IN ('career_guidance', 'interview_prep', 'skill_assessment', 'general_support'));

ALTER TABLE public.ai_conversations 
ADD CONSTRAINT chk_ai_conversations_status_valid 
CHECK (status IN ('active', 'completed', 'archived'));

ALTER TABLE public.ai_conversations 
ADD CONSTRAINT chk_ai_conversations_rating_range 
CHECK (satisfaction_rating IS NULL OR (satisfaction_rating >= 1 AND satisfaction_rating <= 5));

ALTER TABLE public.ai_conversations 
ADD CONSTRAINT chk_ai_conversations_messages_positive 
CHECK (total_messages >= 0);

ALTER TABLE public.ai_conversations 
ADD CONSTRAINT chk_ai_conversations_tokens_positive 
CHECK (tokens_used >= 0);

-- Add indexes for ai_conversations
CREATE INDEX idx_ai_conversations_user_id ON public.ai_conversations(user_id);
CREATE INDEX idx_ai_conversations_type ON public.ai_conversations(conversation_type);
CREATE INDEX idx_ai_conversations_status ON public.ai_conversations(status);
CREATE INDEX idx_ai_conversations_last_message_at ON public.ai_conversations(last_message_at);
CREATE INDEX idx_ai_conversations_created_at ON public.ai_conversations(created_at);

-- Add update timestamp trigger
CREATE TRIGGER update_ai_conversations_timestamp
BEFORE UPDATE ON public.ai_conversations
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- =============================================================================
-- ENABLE ROW LEVEL SECURITY ON NEW TABLES
-- =============================================================================

ALTER TABLE public.applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.interviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.certificates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_conversations ENABLE ROW LEVEL SECURITY;

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Verify all tables have proper structure
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name IN ('applications', 'interviews', 'certificates', 'ai_conversations')
ORDER BY table_name, ordinal_position;

-- Verify foreign key constraints
SELECT 
    tc.table_name,
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND tc.table_name IN ('applications', 'interviews', 'certificates', 'ai_conversations')
ORDER BY tc.table_name;
