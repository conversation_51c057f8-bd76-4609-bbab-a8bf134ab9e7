import { Suspense } from "react"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { FilesPage as FilesPageComponent } from "@/components/files-page"
import { Database } from "@/types/database.types"

async function FilesData() {
  try {
    const supabase = createServerComponentClient<Database>({ cookies })
    
    // Check if user is authenticated using getUser
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) {
      console.error("Authentication error:", userError)
      redirect("/login")
    }
    if (!user) {
      redirect("/login")
    }
    
    // Get the prospect ID from the users table
    const { data: userData, error: userDataError } = await supabase
      .from("users")
      .select("id, role")
      .eq("id", user.id)
      .single()
    
    if (userDataError) {
      console.error("User data error:", userDataError)
      redirect("/unauthorized")
    }
    if (!userData || userData.role !== "prospect") {
      redirect("/unauthorized")
    }
    
    // Get the prospect's profile
    const { data: prospectData, error: prospectError } = await supabase
      .from("prospects")
      .select("id")
      .eq("user_id", userData.id)
      .single()
    
    if (prospectError) {
      console.error("Prospect error:", prospectError)
      return <div>Error loading prospect data. Please try again later.</div>
    }
    if (!prospectData) {
      return <div>Prospect profile not found.</div>
    }
    
    // Fetch all files for this prospect
    const { data: files, error: filesError } = await supabase
      .from("files")
      .select(`
        id,
        prospect_id,
        module_id,
        training_modules (
          title,
          description
        ),
        file_name,
        file_type,
        file_url,
        file_size,
        upload_date,
        issue_date,
        expiry_date,
        verification_code,
        created_at,
        updated_at
      `)
      .eq("prospect_id", prospectData.id)
      .order("upload_date", { ascending: false })

    if (filesError) {
      console.error("Files error:", filesError)
      return <div>Error loading files. Please try again later.</div>
    }

    // Transform files data for the component
    const transformedFiles = files?.map(file => ({
      id: file.id,
      file_name: file.file_name || "",
      file_type: file.file_type || "",
      file_url: file.file_url || "",
      file_size: file.file_size || 0,
      upload_date: file.upload_date || "",
      module_title: file.training_modules?.title || "",
      module_description: file.training_modules?.description || "",
      issue_date: file.issue_date || "",
      expiry_date: file.expiry_date || "",
      verification_code: file.verification_code || "",
      created_at: file.created_at,
      updated_at: file.updated_at
    })) || []

    return (
      <FilesPageComponent
        files={transformedFiles}
        prospectId={prospectData.id}
      />
    )
  } catch (error) {
    console.error("Unexpected error:", error)
    return <div>An unexpected error occurred. Please try again later.</div>
  }
}

export default function FilesPageWrapper() {
  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <Suspense fallback={<div>Loading...</div>}>
        <FilesData />
      </Suspense>
    </div>
  )
}