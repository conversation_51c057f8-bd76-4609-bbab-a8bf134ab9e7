-- =============================================================================
-- FIX INTERVIEWS TABLE STRUCTURE
-- =============================================================================
-- This migration fixes the interviews table to support the interview invitation workflow
-- and resolves inconsistencies between the schema and API implementation.

-- First, update the interview_status enum to include pending_scheduling
ALTER TYPE interview_status ADD VALUE IF NOT EXISTS 'pending_scheduling';

-- Drop and recreate the interviews table with the correct structure
DROP TABLE IF EXISTS public.interviews CASCADE;

CREATE TABLE public.interviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_id UUID NOT NULL,
    interviewer_id UUID NOT NULL, -- Changed from bpo_user_id to match API
    scheduled_at TIMESTAMPTZ, -- Made nullable to support pending_scheduling status
    duration_minutes INTEGER DEFAULT 60,
    interview_type VARCHAR(50) DEFAULT 'video_call',
    meeting_url TEXT,
    meeting_id TEXT,
    status interview_status DEFAULT 'pending_scheduling', -- Changed default to pending_scheduling
    notes TEXT,
    score INTEGER,
    feedback TEXT,
    recommendation TEXT,
    conducted_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    cancelled_reason TEXT,
    rescheduled_from UUID, -- Reference to previous interview if rescheduled
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key constraints for interviews
ALTER TABLE public.interviews 
ADD CONSTRAINT fk_interviews_application_id 
FOREIGN KEY (application_id) REFERENCES public.applications(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.interviews 
ADD CONSTRAINT fk_interviews_interviewer_id 
FOREIGN KEY (interviewer_id) REFERENCES public.users(id) 
ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE public.interviews 
ADD CONSTRAINT fk_interviews_rescheduled_from 
FOREIGN KEY (rescheduled_from) REFERENCES public.interviews(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Add check constraints
ALTER TABLE public.interviews 
ADD CONSTRAINT chk_interviews_duration_positive 
CHECK (duration_minutes > 0);

ALTER TABLE public.interviews 
ADD CONSTRAINT chk_interviews_score_range 
CHECK (score IS NULL OR (score >= 0 AND score <= 100));

-- Updated constraint: scheduled_at can be null for pending_scheduling status
ALTER TABLE public.interviews 
ADD CONSTRAINT chk_interviews_scheduled_at_required 
CHECK (
    (status = 'pending_scheduling' AND scheduled_at IS NULL) OR
    (status != 'pending_scheduling' AND scheduled_at IS NOT NULL)
);

-- Add indexes for interviews
CREATE INDEX idx_interviews_application_id ON public.interviews(application_id);
CREATE INDEX idx_interviews_interviewer_id ON public.interviews(interviewer_id);
CREATE INDEX idx_interviews_status ON public.interviews(status);
CREATE INDEX idx_interviews_scheduled_at ON public.interviews(scheduled_at);

-- Add update timestamp trigger
CREATE TRIGGER update_interviews_timestamp
BEFORE UPDATE ON public.interviews
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- =============================================================================
-- CREATE INTERVIEW_INVITATIONS TABLE
-- =============================================================================
-- This table tracks the invitation process and communication with prospects

CREATE TABLE public.interview_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    interview_id UUID NOT NULL,
    prospect_id UUID NOT NULL,
    interviewer_id UUID NOT NULL,
    invitation_message TEXT,
    invitation_sent_at TIMESTAMPTZ DEFAULT NOW(),
    invitation_status VARCHAR(50) DEFAULT 'sent', -- 'sent', 'viewed', 'responded', 'expired'
    response_message TEXT,
    responded_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'), -- Invitations expire after 7 days
    reminder_sent_at TIMESTAMPTZ,
    reminder_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key constraints for interview_invitations
ALTER TABLE public.interview_invitations 
ADD CONSTRAINT fk_interview_invitations_interview_id 
FOREIGN KEY (interview_id) REFERENCES public.interviews(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.interview_invitations 
ADD CONSTRAINT fk_interview_invitations_prospect_id 
FOREIGN KEY (prospect_id) REFERENCES public.prospects(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.interview_invitations 
ADD CONSTRAINT fk_interview_invitations_interviewer_id 
FOREIGN KEY (interviewer_id) REFERENCES public.users(id) 
ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add check constraints
ALTER TABLE public.interview_invitations 
ADD CONSTRAINT chk_interview_invitations_status_valid 
CHECK (invitation_status IN ('sent', 'viewed', 'responded', 'expired'));

ALTER TABLE public.interview_invitations 
ADD CONSTRAINT chk_interview_invitations_reminder_count_positive 
CHECK (reminder_count >= 0);

-- Add indexes for interview_invitations
CREATE INDEX idx_interview_invitations_interview_id ON public.interview_invitations(interview_id);
CREATE INDEX idx_interview_invitations_prospect_id ON public.interview_invitations(prospect_id);
CREATE INDEX idx_interview_invitations_interviewer_id ON public.interview_invitations(interviewer_id);
CREATE INDEX idx_interview_invitations_status ON public.interview_invitations(invitation_status);
CREATE INDEX idx_interview_invitations_sent_at ON public.interview_invitations(invitation_sent_at);
CREATE INDEX idx_interview_invitations_expires_at ON public.interview_invitations(expires_at);

-- Add update timestamp trigger
CREATE TRIGGER update_interview_invitations_timestamp
BEFORE UPDATE ON public.interview_invitations
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- =============================================================================
-- CREATE INTERVIEW_TIME_SLOTS TABLE
-- =============================================================================
-- This table stores available time slots that prospects can choose from

CREATE TABLE public.interview_time_slots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    interview_id UUID NOT NULL,
    proposed_at TIMESTAMPTZ NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    is_selected BOOLEAN DEFAULT FALSE,
    is_available BOOLEAN DEFAULT TRUE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key constraints for interview_time_slots
ALTER TABLE public.interview_time_slots 
ADD CONSTRAINT fk_interview_time_slots_interview_id 
FOREIGN KEY (interview_id) REFERENCES public.interviews(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Add check constraints
ALTER TABLE public.interview_time_slots 
ADD CONSTRAINT chk_interview_time_slots_duration_positive 
CHECK (duration_minutes > 0);

-- Only one slot can be selected per interview
CREATE UNIQUE INDEX idx_interview_time_slots_selected_unique 
ON public.interview_time_slots(interview_id) 
WHERE is_selected = TRUE;

-- Add indexes for interview_time_slots
CREATE INDEX idx_interview_time_slots_interview_id ON public.interview_time_slots(interview_id);
CREATE INDEX idx_interview_time_slots_proposed_at ON public.interview_time_slots(proposed_at);
CREATE INDEX idx_interview_time_slots_is_selected ON public.interview_time_slots(is_selected);
CREATE INDEX idx_interview_time_slots_is_available ON public.interview_time_slots(is_available);

-- Add update timestamp trigger
CREATE TRIGGER update_interview_time_slots_timestamp
BEFORE UPDATE ON public.interview_time_slots
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- =============================================================================
-- GRANT PERMISSIONS
-- =============================================================================

-- Grant permissions for interviews table
GRANT ALL ON public.interviews TO authenticated;
GRANT ALL ON public.interviews TO service_role;

-- Grant permissions for interview_invitations table
GRANT ALL ON public.interview_invitations TO authenticated;
GRANT ALL ON public.interview_invitations TO service_role;

-- Grant permissions for interview_time_slots table
GRANT ALL ON public.interview_time_slots TO authenticated;
GRANT ALL ON public.interview_time_slots TO service_role;

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Verify the interviews table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'interviews'
ORDER BY ordinal_position;

-- Verify the interview_invitations table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'interview_invitations'
ORDER BY ordinal_position;

-- Verify the interview_time_slots table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'interview_time_slots'
ORDER BY ordinal_position;
