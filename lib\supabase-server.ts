import { createClient } from '@supabase/supabase-js';
import {
  createServerComponentClient,
  createRouteHandlerClient
} from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/types/database.types';

/**
 * Create a Supabase client for server components with user authentication
 * Use this in server components that need user authentication
 */
export const createServerClient = async () => {
  const cookieStore = await cookies();
  return createServerComponentClient<Database>({
    cookies: () => cookieStore
  });
};

/**
 * Create a Supabase client for API route handlers with user authentication
 * Use this in API routes that need user authentication
 */
export const createApiClient = async () => {
  const cookieStore = await cookies();
  return createRouteHandlerClient<Database>({
    cookies: () => cookieStore
  });
};

/**
 * Create a Supabase admin client (service role) for server operations
 * ONLY use this when you need to bypass RLS policies
 */
export function createAdminClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase environment variables');
  }

  return createClient<Database>(
    supabaseUrl,
    supabaseKey,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      }
    }
  );
}