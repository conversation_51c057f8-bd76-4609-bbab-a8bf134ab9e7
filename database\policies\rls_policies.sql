-- ROW LEVEL SECURITY POLICIES
-- Non-recursive policies using security definer functions

-- =============================================================================
-- ENABLE RLS ON ALL TABLES
-- =============================================================================

ALTER TABLE public.bpo_teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.job_postings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.prospects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.training_modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lessons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.progress_records ENABLE ROW LEVEL SECURITY;

-- =============================================================================
-- BPO_TEAMS TABLE POLICIES
-- =============================================================================

-- Platform admins can do everything
CREATE POLICY "platform_admins_all_bpo_teams" ON public.bpo_teams
FOR ALL
TO authenticated
USING (public.is_platform_admin(auth.uid()))
WITH CHECK (public.is_platform_admin(auth.uid()));

-- Users can view their own team memberships
CREATE POLICY "users_view_own_bpo_teams" ON public.bpo_teams
FOR SELECT
TO authenticated
USING (user_id = auth.uid());

-- BPO admins can manage their BPO's team (non-recursive)
CREATE POLICY "bpo_admins_manage_teams" ON public.bpo_teams
FOR ALL
TO authenticated
USING (public.is_bpo_admin(auth.uid(), bpo_id))
WITH CHECK (public.is_bpo_admin(auth.uid(), bpo_id));

-- =============================================================================
-- JOB_POSTINGS TABLE POLICIES
-- =============================================================================

-- Platform admins can do everything
CREATE POLICY "platform_admins_all_job_postings" ON public.job_postings
FOR ALL
TO authenticated
USING (public.is_platform_admin(auth.uid()))
WITH CHECK (public.is_platform_admin(auth.uid()));

-- Public can view published job postings
CREATE POLICY "public_view_published_jobs" ON public.job_postings
FOR SELECT
TO authenticated
USING (status = 'published');

-- BPO team members can view their BPO's job postings
CREATE POLICY "bpo_team_view_own_jobs" ON public.job_postings
FOR SELECT
TO authenticated
USING (
  bpo_id IN (
    SELECT bpo_id FROM public.get_user_bpo_memberships(auth.uid())
  )
);

-- BPO admins can manage their BPO's job postings
CREATE POLICY "bpo_admins_manage_jobs" ON public.job_postings
FOR ALL
TO authenticated
USING (public.is_bpo_admin(auth.uid(), bpo_id))
WITH CHECK (public.is_bpo_admin(auth.uid(), bpo_id));

-- =============================================================================
-- USERS TABLE POLICIES
-- =============================================================================

-- Platform admins can do everything
CREATE POLICY "platform_admins_all_users" ON public.users
FOR ALL
TO authenticated
USING (public.is_platform_admin(auth.uid()))
WITH CHECK (public.is_platform_admin(auth.uid()));

-- Users can view and update their own profile
CREATE POLICY "users_own_profile" ON public.users
FOR ALL
TO authenticated
USING (id = auth.uid())
WITH CHECK (id = auth.uid());

-- BPO admins can view user data for applicants to their job postings
CREATE POLICY "bpo_admins_view_applicant_users" ON public.users
FOR SELECT
TO authenticated
USING (id IN (
  SELECT p.user_id FROM public.prospects p
  JOIN public.applications a ON a.prospect_id = p.id
  JOIN public.job_postings jp ON jp.id = a.job_id
  WHERE public.is_bpo_admin(auth.uid(), jp.bpo_id)
));

-- =============================================================================
-- PROSPECTS TABLE POLICIES
-- =============================================================================

-- Platform admins can do everything
CREATE POLICY "platform_admins_all_prospects" ON public.prospects
FOR ALL
TO authenticated
USING (public.is_platform_admin(auth.uid()))
WITH CHECK (public.is_platform_admin(auth.uid()));

-- Users can view and update their own prospect profile
CREATE POLICY "users_own_prospect_profile" ON public.prospects
FOR ALL
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- BPO admins can view prospect profiles for applications to their job postings
CREATE POLICY "bpo_admins_view_applicant_prospects" ON public.prospects
FOR SELECT
TO authenticated
USING (id IN (
  SELECT a.prospect_id FROM public.applications a
  JOIN public.job_postings jp ON jp.id = a.job_id
  WHERE public.is_bpo_admin(auth.uid(), jp.bpo_id)
));

-- =============================================================================
-- TRAINING MODULES POLICIES
-- =============================================================================

-- Platform admins can do everything
CREATE POLICY "platform_admins_all_training_modules" ON public.training_modules
FOR ALL
TO authenticated
USING (public.is_platform_admin(auth.uid()))
WITH CHECK (public.is_platform_admin(auth.uid()));

-- All authenticated users can view published modules
CREATE POLICY "users_view_published_modules" ON public.training_modules
FOR SELECT
TO authenticated
USING (status = 'published');

-- =============================================================================
-- PROGRESS_RECORDS POLICIES
-- =============================================================================

-- Platform admins can do everything
CREATE POLICY "platform_admins_all_progress_records" ON public.progress_records
FOR ALL
TO authenticated
USING (public.is_platform_admin(auth.uid()))
WITH CHECK (public.is_platform_admin(auth.uid()));

-- Prospects can view and update their own progress
CREATE POLICY "prospects_own_progress" ON public.progress_records
FOR ALL
TO authenticated
USING (prospect_id IN (
  SELECT id FROM public.prospects WHERE user_id = auth.uid()
))
WITH CHECK (prospect_id IN (
  SELECT id FROM public.prospects WHERE user_id = auth.uid()
));

-- =============================================================================
-- APPLICATIONS TABLE POLICIES
-- =============================================================================

-- Platform admins can do everything
CREATE POLICY "platform_admins_all_applications" ON public.applications
FOR ALL
TO authenticated
USING (public.is_platform_admin(auth.uid()))
WITH CHECK (public.is_platform_admin(auth.uid()));

-- Prospects can manage their own applications
CREATE POLICY "prospects_own_applications" ON public.applications
FOR ALL
TO authenticated
USING (prospect_id IN (
  SELECT id FROM public.prospects WHERE user_id = auth.uid()
))
WITH CHECK (prospect_id IN (
  SELECT id FROM public.prospects WHERE user_id = auth.uid()
));

-- BPO admins can view applications for their job postings
CREATE POLICY "bpo_admins_view_applications" ON public.applications
FOR SELECT
TO authenticated
USING (job_id IN (
  SELECT jp.id FROM public.job_postings jp
  WHERE public.is_bpo_admin(auth.uid(), jp.bpo_id)
));

-- BPO admins can update application status for their job postings
CREATE POLICY "bpo_admins_update_applications" ON public.applications
FOR UPDATE
TO authenticated
USING (job_id IN (
  SELECT jp.id FROM public.job_postings jp
  WHERE public.is_bpo_admin(auth.uid(), jp.bpo_id)
))
WITH CHECK (job_id IN (
  SELECT jp.id FROM public.job_postings jp
  WHERE public.is_bpo_admin(auth.uid(), jp.bpo_id)
));

-- =============================================================================
-- INTERVIEWS TABLE POLICIES
-- =============================================================================

-- Platform admins can do everything
CREATE POLICY "platform_admins_all_interviews" ON public.interviews
FOR ALL
TO authenticated
USING (public.is_platform_admin(auth.uid()))
WITH CHECK (public.is_platform_admin(auth.uid()));

-- Prospects can view their own interviews
CREATE POLICY "prospects_view_own_interviews" ON public.interviews
FOR SELECT
TO authenticated
USING (application_id IN (
  SELECT a.id FROM public.applications a
  JOIN public.prospects p ON p.id = a.prospect_id
  WHERE p.user_id = auth.uid()
));

-- BPO team members can manage interviews for their BPO's applications
CREATE POLICY "bpo_team_manage_interviews" ON public.interviews
FOR ALL
TO authenticated
USING (application_id IN (
  SELECT a.id FROM public.applications a
  JOIN public.job_postings jp ON jp.id = a.job_id
  WHERE jp.bpo_id IN (SELECT bpo_id FROM public.get_user_bpo_memberships(auth.uid()))
))
WITH CHECK (application_id IN (
  SELECT a.id FROM public.applications a
  JOIN public.job_postings jp ON jp.id = a.job_id
  WHERE jp.bpo_id IN (SELECT bpo_id FROM public.get_user_bpo_memberships(auth.uid()))
));

-- =============================================================================
-- CERTIFICATES TABLE POLICIES
-- =============================================================================

-- Platform admins can do everything
CREATE POLICY "platform_admins_all_certificates" ON public.certificates
FOR ALL
TO authenticated
USING (public.is_platform_admin(auth.uid()))
WITH CHECK (public.is_platform_admin(auth.uid()));

-- Prospects can view their own certificates
CREATE POLICY "prospects_view_own_certificates" ON public.certificates
FOR SELECT
TO authenticated
USING (prospect_id IN (
  SELECT id FROM public.prospects WHERE user_id = auth.uid()
));

-- System can create certificates (for automated certificate generation)
CREATE POLICY "system_create_certificates" ON public.certificates
FOR INSERT
TO authenticated
WITH CHECK (true); -- This will be restricted by application logic

-- Public can verify certificates by verification code
CREATE POLICY "public_verify_certificates" ON public.certificates
FOR SELECT
TO authenticated
USING (verification_code IS NOT NULL AND is_verified = true);

-- =============================================================================
-- AI_CONVERSATIONS TABLE POLICIES
-- =============================================================================

-- Platform admins can do everything
CREATE POLICY "platform_admins_all_ai_conversations" ON public.ai_conversations
FOR ALL
TO authenticated
USING (public.is_platform_admin(auth.uid()))
WITH CHECK (public.is_platform_admin(auth.uid()));

-- Users can manage their own AI conversations
CREATE POLICY "users_own_ai_conversations" ON public.ai_conversations
FOR ALL
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());
