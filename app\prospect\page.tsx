import React from "react"
import { DashboardOverview } from "@/components/dashboard/dashboard-overview"
import { getServerAuthUser } from "@/lib/auth"
import { createServerClient, createAdminClient } from "@/lib/supabase-server"
import { redirect } from "next/navigation"

// Force dynamic rendering for this page due to authentication requirements
export const dynamic = 'force-dynamic'

// Move interface outside of function to fix linting
interface ModuleData {
  id: string
  title: string
  description: string
  currentLesson: string
  nextLessonId?: string
  progress: number
  duration: number
  color: string
}

export default async function ProspectDashboard(): Promise<React.JSX.Element> {
  // Use standardized server authentication
  const authResult = await getServerAuthUser()

  if (!authResult.user) {
    redirect('/login')
  }

  const user = authResult.user

  // Only prospects can access this page
  if (user.role !== 'prospect') {
    redirect('/unauthorized')
  }

  // Extract first name from full name
  const firstName = user.full_name ? user.full_name.split(' ')[0] : user.email.split('@')[0] || "User"

  const supabase = await createServerClient()
  const adminClient = createAdminClient()

  // Get the prospect's profile using proper RLS (no admin client needed!)
  let prospectData = null

  try {
    const { data, error } = await supabase
      .from("prospects")
      .select("id, training_status")
      .eq("user_id", user.id as any)
      .single()

    if (error) {
      console.error("Error fetching prospect data:", error)
    } else {
      prospectData = data
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error("Error fetching prospect data:", errorMessage)
  }

  const prospectId = (prospectData as any)?.id
  
  // Fetch training statistics with optimized queries
  let trainingStats = {
    totalModules: 0,
    completedModules: 0,
    inProgressModules: 0,
    progressPercentage: 0,
    badges: 0,
    callPracticeHours: 0,
    callPracticeScore: 0,
    completedAssessments: 0,
    totalAssessments: 0
  }

  // Fetch additional data for notifications
  let notificationData = {
    newJobMatches: 0,
    recentBadges: 0,
    upcomingInterviews: 0
  }

  if (prospectId) {
    try {
      // OPTIMIZED: Use Promise.all to run queries in parallel
      const [
        { data: modules },
        { data: progress },
        { data: badges },
        { data: callPractice }
      ] = await Promise.all([
        // Get all published modules
        supabase
          .from("training_modules")
          .select("id, title, description, cover_image_url, duration_minutes")
          .eq("status", "published"),

        // Get all progress with nested data in one query
        adminClient
          .from("progress_records")
          .select(`
            id,
            status,
            activities(
              id,
              lessons(
                id,
                module_id
              )
            )
          `)
          .eq("prospect_id", prospectId),

        // Get badge count
        adminClient
          .from("badges")
          .select("id", { count: 'exact', head: true })
          .eq("prospect_id", prospectId),

        // Get call practice stats
        adminClient
          .from("call_practice_sessions")
          .select("duration_minutes, score")
          .eq("prospect_id", prospectId)
      ])

      trainingStats.totalModules = modules?.length || 0
      trainingStats.badges = badges?.length || 0

      // Calculate in-progress modules count
      let inProgressModulesCount = 0

      // Process call practice stats
      if (callPractice && callPractice.length > 0) {
        trainingStats.callPracticeHours = callPractice.reduce(
          (sum, session) => sum + ((session.duration_minutes || 0) / 60),
          0
        )
        const totalScore = callPractice.reduce(
          (sum, session) => sum + (session.score || 0),
          0
        )
        trainingStats.callPracticeScore = Math.round(totalScore / callPractice.length)
      }

      // SIMPLIFIED: Calculate module progress based on completed activities
      if (modules && modules.length > 0) {
        const moduleProgressMap = new Map() // Track modules with any progress
        let completedModulesCount = 0

        if (progress && progress.length > 0) {
          // Process progress records to find modules with activity
          for (const record of progress) {
            const activity = record.activities as { lessons?: { module_id?: string } }
            if (activity?.lessons?.module_id) {
              const moduleId = activity.lessons.module_id
              if (!moduleProgressMap.has(moduleId)) {
                moduleProgressMap.set(moduleId, { completed: 0, total: 0 })
              }

              const moduleData = moduleProgressMap.get(moduleId)
              moduleData.total++
              if (record.status === 'completed') {
                moduleData.completed++
              }
            }
          }

          // Count completed and in-progress modules
          for (const [moduleId, data] of moduleProgressMap) {
            if (data.completed === data.total && data.total > 0) {
              completedModulesCount++
            } else if (data.completed > 0) {
              inProgressModulesCount++
            }
          }
        }

        // If user has no progress at all, consider all published modules as available (in progress)
        if (inProgressModulesCount === 0 && completedModulesCount === 0) {
          inProgressModulesCount = trainingStats.totalModules
        }

        trainingStats.completedModules = completedModulesCount
        trainingStats.inProgressModules = inProgressModulesCount
        trainingStats.progressPercentage = trainingStats.totalModules > 0
          ? Math.round((trainingStats.completedModules / trainingStats.totalModules) * 100)
          : 0
      }

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error("Error fetching training statistics:", errorMessage)
    }
  }
  
  // Get top 3 in-progress modules (OPTIMIZED)
  let inProgressModules: ModuleData[] = []

  try {
    if (prospectId) {
      // Fetching in-progress modules for prospect

      // OPTIMIZED: Get modules and progress in parallel with minimal data
      const [
        { data: modulesWithDetails },
        { data: progressRecords },
        { data: assessmentCompletions },
        { data: totalAssessments },
        { data: newJobMatches },
        { data: userCertificates }
      ] = await Promise.all([
        adminClient
          .from("training_modules")
          .select(`
            id,
            title,
            description,
            duration_minutes,
            lessons!inner (
              id,
              title,
              order_index
            )
          `)
          .eq("status", "published")
          .limit(10), // Limit to first 10 modules for performance

        adminClient
          .from("progress_records")
          .select(`
            status,
            activities!inner(
              lesson_id,
              lessons!inner(
                id,
                title,
                module_id,
                order_index
              )
            )
          `)
          .eq("prospect_id", prospectId)
          .eq("status", "completed" as any),

        // Get assessment completion data
        supabase
          .from("assessment_completions")
          .select("id, assessment_id, status, score")
          .eq("user_id", user.id as any),

        // Get total assessments count
        supabase
          .from("assessments")
          .select("id")
          .eq("is_active", true),

        // Get new job matches (jobs posted in last 7 days)
        supabase
          .from("job_postings")
          .select("id")
          .eq("status", "published" as any)
          .gte("created_at", new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()),

        // Get certificates/badges for achievements
        supabase
          .from("certificates")
          .select("id, certificate_type, issued_at")
          .eq("prospect_id", prospectId)
      ])

      // Update training stats with real data
      trainingStats.totalAssessments = totalAssessments?.length || 0
      trainingStats.completedAssessments = assessmentCompletions?.filter(ac => ac.status === 'completed').length || 0
      trainingStats.badges = userCertificates?.length || 0

      // Update notification data
      notificationData.newJobMatches = newJobMatches?.length || 0
      notificationData.recentBadges = userCertificates?.filter(cert => {
        const issuedDate = new Date(cert.issued_at)
        const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        return issuedDate > sevenDaysAgo
      }).length || 0

      if (modulesWithDetails && modulesWithDetails.length > 0) {
        // Found published modules

        // SIMPLIFIED: Create a map of completed lessons by module
        const completedLessonsByModule = new Map<string, Set<string>>()

        if (progressRecords && progressRecords.length > 0) {
          // Found progress records

          for (const record of progressRecords) {
            const activity = record.activities as { lessons?: { module_id?: string; id?: string } }
            if (activity?.lessons?.module_id && activity?.lessons?.id) {
              const moduleId = activity.lessons.module_id
              const lessonId = activity.lessons.id

              if (!completedLessonsByModule.has(moduleId)) {
                completedLessonsByModule.set(moduleId, new Set())
              }
              completedLessonsByModule.get(moduleId)!.add(lessonId)
            }
          }
        }

        // SIMPLIFIED: Process modules to find in-progress ones
        for (const module of modulesWithDetails.slice(0, 5)) { // Limit to first 5 for performance
          const lessons = (module.lessons as Array<{ id?: string; title?: string; order_index?: number }>) || []
          const totalLessons = lessons.length
          const completedLessons = completedLessonsByModule.get(module.id)?.size || 0

          // A module is in-progress if it has some but not all lessons completed
          const isInProgress = completedLessons > 0 && completedLessons < totalLessons

          if (isInProgress || (completedLessons === 0 && inProgressModules.length < 2)) {
            const progress = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0
            const sortedLessons = [...lessons].sort((a, b) => (a.order_index || 0) - (b.order_index || 0))

            // Find the next lesson to continue with
            let nextLessonTitle = "Introduction"
            let nextLessonId = sortedLessons[0]?.id

            if (completedLessons > 0) {
              // Find the first lesson that hasn't been completed
              const completedLessonIds = completedLessonsByModule.get(module.id) || new Set()
              const nextLesson = sortedLessons.find(lesson => !completedLessonIds.has(lesson.id || ''))
              if (nextLesson) {
                nextLessonTitle = nextLesson.title || "Next Lesson"
                nextLessonId = nextLesson.id
              } else {
                // All lessons completed, show first lesson for review
                nextLessonTitle = sortedLessons[0]?.title || "Review"
                nextLessonId = sortedLessons[0]?.id
              }
            } else {
              // No lessons completed, start with first lesson
              nextLessonTitle = sortedLessons[0]?.title || "Introduction"
              nextLessonId = sortedLessons[0]?.id
            }

            inProgressModules.push({
              id: module.id,
              title: module.title,
              description: module.description || "",
              currentLesson: nextLessonTitle,
              nextLessonId: nextLessonId,
              progress: Math.max(progress, 5), // Minimum 5% to show as started
              duration: Math.round((module.duration_minutes || 60) / 60),
              color: ["blue", "green", "purple"][inProgressModules.length % 3]
            })

            // Module progress calculated
          }

          // Stop after finding 3 modules
          if (inProgressModules.length >= 3) break
        }
      }

      // Fallback: If no modules found, show first available module
      if (inProgressModules.length === 0 && modulesWithDetails && modulesWithDetails.length > 0) {
        const firstModule = modulesWithDetails[0]
        const firstLesson = (firstModule.lessons as Array<{ id?: string; title?: string }>)?.[0]

        inProgressModules.push({
          id: firstModule.id,
          title: firstModule.title,
          description: firstModule.description || "",
          currentLesson: firstLesson?.title || "Introduction",
          nextLessonId: firstLesson?.id,
          progress: 5,
          duration: Math.round((firstModule.duration_minutes || 60) / 60),
          color: "blue"
        })

        // Using first available module as fallback
      }

      // Displaying in-progress modules
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error("Error fetching in-progress modules:", errorMessage)

    // Simple fallback
    inProgressModules = [{
      id: "fallback-1",
      title: "Getting Started",
      description: "Begin your BPO training journey",
      currentLesson: "Introduction",
      nextLessonId: undefined,
      progress: 0,
      duration: 2,
      color: "blue"
    }]
  }
  
  // Prepare dashboard data
  const dashboardData = {
    userInfo: {
      name: firstName,
      email: user.email || "",
      avatar_url: user.avatar_url || null
    },
    trainingStats,
    inProgressModules,
    notificationData
  }
  
  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <DashboardOverview data={dashboardData} />
    </div>
  )
} 