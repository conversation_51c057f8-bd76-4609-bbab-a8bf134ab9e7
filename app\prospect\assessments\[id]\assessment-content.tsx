"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Clock, CheckCircle, Play } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { getAssessmentById, getAssessmentQuestions, getAssessmentWithStatus, startAssessment } from '@/lib/api/assessments';
import { Assessment, AssessmentQuestion, AssessmentWithStatus } from '@/types/assessment';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/components/ui/use-toast';

interface AssessmentContentProps {
  id: string;
}

export function AssessmentContent({ id }: AssessmentContentProps) {
  const [assessment, setAssessment] = useState<AssessmentWithStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, loading: userLoading } = useAuth();
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    async function loadAssessment() {
      try {
        if (!user && !userLoading) {
          setError('You must be logged in to view assessments');
          setLoading(false);
          return;
        }

        if (user) {
          const assessmentWithStatus = await getAssessmentWithStatus(id, user.id);
          setAssessment(assessmentWithStatus);
        }
      } catch (err: any) {
        console.error('Error loading assessment:', err);
        setError(err.message || 'Failed to load assessment');
      } finally {
        setLoading(false);
      }
    }

    if (!userLoading) {
      loadAssessment();
    }
  }, [id, user, userLoading]);

  const handleStartAssessment = async () => {
    if (!user || !assessment) return;

    try {
      await startAssessment(assessment.id, user.id);
      router.push(`/prospect/assessments/${id}/take`);
    } catch (err: any) {
      console.error('Error starting assessment:', err);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to start assessment',
      });
    }
  };

  const handleContinueAssessment = () => {
    router.push(`/prospect/assessments/${id}/take`);
  };

  const handleViewResults = () => {
    router.push(`/prospect/assessments/${id}/results`);
  };

  if (loading) {
    return (
      <div className="max-w-3xl mx-auto p-4">
        <Card>
          <CardHeader>
            <div className="h-8 w-3/4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse mt-2"></div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-3xl mx-auto p-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!assessment) {
    return (
      <div className="max-w-3xl mx-auto p-4">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Assessment Not Found</AlertTitle>
          <AlertDescription>
            The assessment you're looking for doesn't exist or has been removed.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Custom success and warning alert classes
  const successAlertClass = "bg-green-50 border-green-200 text-green-800 [&>svg]:text-green-600";
  const warningAlertClass = "bg-amber-50 border-amber-200 text-amber-800 [&>svg]:text-amber-600";

  return (
    <div className="max-w-3xl mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">{assessment.title}</CardTitle>
          <CardDescription>
            <div className="flex items-center gap-2 mt-1">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>{assessment.duration}</span>
            </div>
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Assessment description */}
          <div>
            <h3 className="text-lg font-medium mb-2">Description</h3>
            <p className="text-muted-foreground">{assessment.description}</p>
          </div>

          <Separator />

          {/* What it checks */}
          <div>
            <h3 className="text-lg font-medium mb-2">What it checks</h3>
            <p className="text-muted-foreground">{assessment.what_it_checks}</p>
          </div>

          {/* What to expect */}
          <div>
            <h3 className="text-lg font-medium mb-2">What to expect</h3>
            <p className="text-muted-foreground">{assessment.what_to_expect}</p>
          </div>

          <Separator />

          {/* Instructions */}
          <div>
            <h3 className="text-lg font-medium mb-2">Instructions</h3>
            <div className="bg-muted p-4 rounded-md">
              <p className="whitespace-pre-line">{assessment.instructions}</p>
            </div>
          </div>

          {/* Status banner */}
          {assessment.status && (
            <Alert 
              variant="default"
              className={
                assessment.status === 'completed' 
                  ? successAlertClass
                  : assessment.status === 'in_progress' 
                    ? warningAlertClass
                    : ""
              }
            >
              {assessment.status === 'completed' && (
                <>
                  <CheckCircle className="h-4 w-4" />
                  <AlertTitle>Assessment Completed</AlertTitle>
                  <AlertDescription>
                    You completed this assessment with a score of {assessment.score}%.
                  </AlertDescription>
                </>
              )}
              {assessment.status === 'in_progress' && (
                <>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Assessment In Progress</AlertTitle>
                  <AlertDescription>
                    You've started this assessment but haven't completed it yet.
                  </AlertDescription>
                </>
              )}
              {assessment.status === 'not_started' && (
                <>
                  <Play className="h-4 w-4" />
                  <AlertTitle>Assessment Not Started</AlertTitle>
                  <AlertDescription>
                    You haven't started this assessment yet.
                  </AlertDescription>
                </>
              )}
            </Alert>
          )}
        </CardContent>

        <CardFooter className="flex justify-end">
          {assessment.status === 'not_started' && (
            <Button onClick={handleStartAssessment}>
              Start Assessment
            </Button>
          )}
          {assessment.status === 'in_progress' && (
            <Button onClick={handleContinueAssessment}>
              Continue Assessment
            </Button>
          )}
          {assessment.status === 'completed' && (
            <Button onClick={handleViewResults}>
              View Results
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
} 