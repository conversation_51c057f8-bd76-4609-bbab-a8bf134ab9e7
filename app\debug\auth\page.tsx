'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { checkBPOTeamMembership, getUserRole } from '@/lib/auth-utils';

export default function AuthDebugPage() {
  const [user, setUser] = useState<any>(null);
  const [bpoMembership, setBpoMembership] = useState<any>(null);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchAuthData() {
      try {
        setLoading(true);
        setError(null);
        
        // Get current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          console.error('Session error:', sessionError);
          setError('Error fetching session: ' + sessionError.message);
          return;
        }
        
        if (!session) {
          setError('No active session found. Please login first.');
          return;
        }
        
        setUser(session.user);
        
        // Check BPO membership
        const membershipResult = await checkBPOTeamMembership(supabase, session.user.id);
        setBpoMembership(membershipResult);
        
        // Get user role
        const role = await getUserRole(supabase, session.user.id);
        setUserRole(role);
        
      } catch (err) {
        console.error('Auth debug error:', err);
        setError('An unexpected error occurred: ' + (err instanceof Error ? err.message : String(err)));
      } finally {
        setLoading(false);
      }
    }
    
    fetchAuthData();
  }, []);

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Authentication Debug</h1>
      
      {loading ? (
        <div className="text-center p-4">Loading authentication data...</div>
      ) : error ? (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
          <p className="font-bold">Error</p>
          <p>{error}</p>
        </div>
      ) : (
        <div className="space-y-6">
          <section className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">User Information</h2>
            <div className="space-y-2">
              <p><strong>ID:</strong> {user?.id}</p>
              <p><strong>Email:</strong> {user?.email}</p>
              <p><strong>Created At:</strong> {new Date(user?.created_at).toLocaleString()}</p>
              <p><strong>Last Sign In:</strong> {user?.last_sign_in_at ? new Date(user?.last_sign_in_at).toLocaleString() : 'N/A'}</p>
              <p><strong>Role:</strong> {userRole || 'No role found'}</p>
            </div>
          </section>
          
          <section className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">BPO Team Membership</h2>
            {bpoMembership?.error ? (
              <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4">
                <p className="font-bold">Error checking BPO membership</p>
                <p>{bpoMembership.error.message}</p>
                {bpoMembership.policyError && (
                  <div className="mt-2">
                    <p className="font-bold">Database Policy Error Detected</p>
                    <p className="text-sm">This is likely an issue with Row Level Security (RLS) policies in the database.</p>
                    <p className="text-sm mt-2">Run the SQL fix script in sql/fix_bpo_teams_policy.sql to resolve this issue.</p>
                  </div>
                )}
              </div>
            ) : bpoMembership?.isMember ? (
              <div className="space-y-2">
                <p className="text-green-600 font-medium">✓ User is a BPO team member</p>
                <p><strong>BPO ID:</strong> {bpoMembership.bpoId}</p>
              </div>
            ) : (
              <p className="text-red-600 font-medium">✗ User is not a BPO team member</p>
            )}
          </section>
          
          <section className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Auth Tokens</h2>
            <div>
              <p className="text-sm text-gray-500">Authentication token is present: {user?.aud ? '✓' : '✗'}</p>
            </div>
          </section>
        </div>
      )}
      
      <div className="mt-8 text-center">
        <a href="/" className="text-blue-600 hover:underline">Back to Home</a>
      </div>
    </div>
  );
} 