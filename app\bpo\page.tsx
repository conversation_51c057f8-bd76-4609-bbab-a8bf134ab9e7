'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { checkBPOTeamMembership } from '@/lib/auth-utils';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  Briefcase, 
  Calendar,
  ArrowRight, 
  TrendingUp,
  UserCheck,
  Clock,
  User,
  CheckCircle2,
  XCircle,
  FileText,
  AlertTriangle
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export default function BPODashboard() {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [bpoData, setBPOData] = useState<any>(null);
  const [accessError, setAccessError] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalTeamMembers: 0,
    activeVacancies: 0,
    totalCandidates: 0,
    upcomingInterviews: 0,
  });
  const [recentActivities, setRecentActivities] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        
        // Get the current user
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          setLoading(false);
          return;
        }
        
        setUser(session.user);
        
        // Get the BPO company this user belongs to using our utility function
        console.log('Checking BPO team membership for user:', session.user.id);
        const teamMemberResult = await checkBPOTeamMembership(supabase, session.user.id);
        
        if (teamMemberResult.error || teamMemberResult.policyError) {
          console.error('Error fetching team member:', teamMemberResult.error || 'Policy error detected');
          
          // Special handling for policy errors
          if (teamMemberResult.policyError || 
              (teamMemberResult.error && teamMemberResult.error.message && 
               (teamMemberResult.error.message.includes('infinite recursion') ||
                // Access code safely with type guard
                (typeof teamMemberResult.error === 'object' && 
                 'code' in teamMemberResult.error && 
                 teamMemberResult.error.code === '42P17')))) {
            
            // Try to continue loading with a warning message instead of blocking access
            setAccessError('Warning: Database security policy issue detected. Some features may be limited. Our team has been notified.');
            
            // If we have a bpoId despite the error, we can try to continue
            if (teamMemberResult.bpoId) {
              console.log('Continuing with BPO ID despite policy error:', teamMemberResult.bpoId);
            } else {
              // If no bpoId, we can't proceed with loading BPO data
              setLoading(false);
              return;
            }
          } else {
            setAccessError('There was an error accessing BPO team data. Please try again later.');
            setLoading(false);
            return;
          }
        }
        
        if (!teamMemberResult.isMember || !teamMemberResult.bpoId) {
          console.log('User is not associated with any BPO team');
          setAccessError('You are not associated with any BPO team. If you believe this is an error, please contact support.');
          setLoading(false);
          return;
        }
        
        const bpoId = teamMemberResult.bpoId;
        if (!bpoId) {
          console.log('No BPO ID found for this user');
          setAccessError('No BPO team data found for your account. Please contact your administrator.');
          setLoading(false);
          return;
        }
        
        // Get BPO data
        const { data: bpo, error: bpoError } = await supabase
          .from('bpos')
          .select('*')
          .eq('id', bpoId)
          .single();
          
        if (bpoError || !bpo) {
          console.error('Error fetching BPO data:', bpoError);
          setLoading(false);
          return;
        }
        
        setBPOData(bpo);
        
        // Set default stats
        const defaultStats = {
          totalTeamMembers: 0,
          activeVacancies: 0,
          totalCandidates: 0,
          upcomingInterviews: 0,
        };
        
        // Start with default stats
        setStats(defaultStats);
        
        try {
          // Get team members count
          let teamCount = 0;
          try {
            // Simplified approach for team counting
            const { data: teamData } = await supabase
              .from('bpo_teams')
              .select('id')
              .eq('bpo_id', bpoId);
              
            teamCount = teamData?.length || 0;
          } catch (teamCountError) {
            console.log('Team count error, using 0 as default:', teamCountError);
          }
          
          // Get active vacancies count
          let vacancyCount = 0;
          let jobIds: string[] = [];
          
          try {
            // Get all job postings for this BPO
            const { data: allVacancies } = await supabase
              .from('job_postings')
              .select('id, status')
              .eq('bpo_id', bpoId);
              
            if (allVacancies && allVacancies.length > 0) {
              jobIds = allVacancies.map(job => job.id);
              // Count active vacancies
              vacancyCount = allVacancies.filter(v => v.status === 'published').length;
            }
          } catch (vacancyError) {
            console.log('Vacancy count error, using 0 as default:', vacancyError);
          }
          
          // Safely update stats with the values we have
          setStats({
            ...defaultStats,
            totalTeamMembers: teamCount,
            activeVacancies: vacancyCount,
          });
          
          // Set empty activities for now
          setRecentActivities([]);
          
          // Set up timeout to prevent the process from hanging indefinitely
          const timeout = setTimeout(() => {
            if (loading) {
              setLoading(false);
              console.log('Dashboard loading timed out, showing partial data');
            }
          }, 5000);
          
          // Try to fetch applications count - but handle errors gracefully
          let candidateCount = 0;
          try {
            if (jobIds.length > 0) {
              const jobIdsParam = jobIds.slice(0, 20).join(','); // Limit to first 20 for safety
              
              const { data: appData } = await supabase
                .from('applications')
                .select('id')
                .filter('job_id', 'in', `(${jobIdsParam})`)
                .limit(1000);
                
              candidateCount = appData?.length || 0;
            }
          } catch (appError) {
            console.log('Application count error, using 0 as default:', appError);
          }
          
          // Try to fetch upcoming interview count
          let interviewCount = 0;
          try {
            const now = new Date().toISOString();
            const { data: interviews } = await supabase
              .from('interviews')
              .select('id')
              .eq('status', 'scheduled')
              .gt('scheduled_at', now)
              .limit(1000);
              
            interviewCount = interviews?.length || 0;
          } catch (interviewError) {
            console.log('Interview count error, using 0 as default:', interviewError);
          }
          
          // Update stats with what we have gathered
          setStats(prevStats => ({
            ...prevStats,
            totalCandidates: candidateCount,
            upcomingInterviews: interviewCount
          }));
          
          // Try to get recent activities
          try {
            // Get a few recent applications
            const { data: recentApps } = await supabase
              .from('applications')
              .select('id, status, submitted_at, job_id, prospect_id')
              .order('submitted_at', { ascending: false })
              .limit(5);
              
            // Get a few recent interviews
            const { data: recentInterviews } = await supabase
              .from('interviews')
              .select('id, status, scheduled_at, application_id')
              .order('scheduled_at', { ascending: false })
              .limit(5);
              
            // Format the activities without trying to get additional info
            const applicationActivities = (recentApps || []).map(app => ({
              type: 'application',
              id: app.id,
              timestamp: app.submitted_at,
              status: app.status,
              title: 'Job Application',
              jobId: app.job_id,
              prospectId: app.prospect_id
            }));
            
            const interviewActivities = (recentInterviews || []).map(interview => ({
              type: 'interview',
              id: interview.id,
              timestamp: interview.scheduled_at,
              status: interview.status,
              title: 'Interview',
              applicationId: interview.application_id
            }));
            
            // Combine and sort
            const activities = [...applicationActivities, ...interviewActivities];
            activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
            
            // Update activities
            setRecentActivities(activities.slice(0, 10));
          } catch (activitiesError) {
            console.log('Error fetching activities, using empty list:', activitiesError);
          }
          
          // Clear the timeout if we complete normally
          clearTimeout(timeout);
          
        } catch (statsError) {
          console.error('Error fetching statistics:', statsError);
          // Default to zeros if there's an error
          setStats({
            totalTeamMembers: 0,
            activeVacancies: 0,
            totalCandidates: 0,
            upcomingInterviews: 0,
          });
        }
        
      } catch (error) {
        console.error('Error fetching data:', error);
        // Set empty state on error
        setRecentActivities([]);
        setStats({
          totalTeamMembers: 0,
          activeVacancies: 0,
          totalCandidates: 0,
          upcomingInterviews: 0,
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  return (
    <div className="container p-4 sm:p-6 lg:p-8 space-y-8">
      {loading ? (
        <div className="h-64 flex items-center justify-center">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
          <span className="ml-3">Loading dashboard...</span>
        </div>
      ) : accessError ? (
        <div className="mt-10">
          <Alert variant="destructive" className="mb-6">
            <AlertTriangle className="h-4 w-4 mr-2" />
            <AlertTitle>Access Error</AlertTitle>
            <AlertDescription>{accessError}</AlertDescription>
          </Alert>
          
          <div className="flex flex-col items-center justify-center p-8 bg-gray-50 dark:bg-gray-800 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-center">Unable to access BPO Dashboard</h2>
            <p className="text-center text-gray-600 dark:text-gray-400 max-w-md">
              You don't have the correct permissions to access this BPO dashboard. This may be because you're not assigned to a BPO team.
            </p>
            
            <div className="flex space-x-4 mt-6">
              <Button variant="outline" onClick={() => router.push('/prospect/dashboard')}>
                Go to Prospect Dashboard
              </Button>
              <Button onClick={() => router.push('/login')}>
                Go to Login
              </Button>
            </div>
          </div>
        </div>
      ) : bpoData ? (
        <>
          {/* Welcome and BPO Info */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Welcome to {bpoData?.name || 'Your BPO'} Dashboard
            </h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {bpoData?.description || 'Manage your team, vacancies, and candidate pipeline all in one place.'}
            </p>
          </div>
          
          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-white dark:bg-gray-800 shadow overflow-hidden">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-blue-500 rounded-md p-3">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                        Team Members
                      </dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                          {stats.totalTeamMembers}
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 px-6 py-3">
                <div className="text-sm">
                  <Link
                    href="/bpo/team"
                    className="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 flex items-center"
                  >
                    View team
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </div>
              </div>
            </Card>
            
            <Card className="bg-white dark:bg-gray-800 shadow overflow-hidden">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                    <Briefcase className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                        Active Vacancies
                      </dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                          {stats.activeVacancies}
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 px-6 py-3">
                <div className="text-sm">
                  <Link
                    href="/bpo/vacancies"
                    className="font-medium text-green-600 dark:text-green-400 hover:text-green-500 flex items-center"
                  >
                    Manage vacancies
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </div>
              </div>
            </Card>
            
            <Card className="bg-white dark:bg-gray-800 shadow overflow-hidden">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-purple-500 rounded-md p-3">
                    <UserCheck className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                        Candidates
                      </dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                          {stats.totalCandidates}
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 px-6 py-3">
                <div className="text-sm">
                  <Link
                    href="/bpo/candidates"
                    className="font-medium text-purple-600 dark:text-purple-400 hover:text-purple-500 flex items-center"
                  >
                    View candidates
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </div>
              </div>
            </Card>
            
            <Card className="bg-white dark:bg-gray-800 shadow overflow-hidden">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-amber-500 rounded-md p-3">
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                        Upcoming Interviews
                      </dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                          {stats.upcomingInterviews}
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 px-6 py-3">
                <div className="text-sm">
                  <Link
                    href="/bpo/interviews"
                    className="font-medium text-amber-600 dark:text-amber-400 hover:text-amber-500 flex items-center"
                  >
                    Schedule interviews
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </div>
              </div>
            </Card>
          </div>
          
          {/* Quick Actions */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Quick Actions
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link
                href="/bpo/vacancies/create"
                className="inline-flex items-center justify-center gap-2 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4 text-center hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <Briefcase className="h-5 w-5 text-blue-500" />
                <span className="text-sm font-medium">Create New Vacancy</span>
              </Link>
              
              <Link
                href="/bpo/candidates/review"
                className="inline-flex items-center justify-center gap-2 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4 text-center hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <UserCheck className="h-5 w-5 text-green-500" />
                <span className="text-sm font-medium">Review Candidates</span>
              </Link>
              
              <Link
                href="/bpo/interviews/schedule"
                className="inline-flex items-center justify-center gap-2 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4 text-center hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <Calendar className="h-5 w-5 text-purple-500" />
                <span className="text-sm font-medium">Schedule Interviews</span>
              </Link>
            </div>
          </div>
          
          {/* Performance Metrics */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Performance Metrics
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              Track your recruitment and team performance.
            </p>
            <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
              <div className="text-center px-4">
                <TrendingUp className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500 dark:text-gray-400">Performance metrics visualization coming soon</p>
              </div>
            </div>
          </div>
          
          {/* Recent Activities */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Recent Activities
            </h3>
            <div className="space-y-4">
              {recentActivities.length > 0 ? (
                recentActivities.map((activity, index) => (
                  <div 
                    key={`${activity.type}-${activity.id}`}
                    className="flex items-start space-x-4 p-3 rounded-lg bg-gray-50 dark:bg-gray-900/50"
                  >
                    <div className={`
                      rounded-full p-2 flex-shrink-0
                      ${activity.type === 'application' ? 'bg-green-100 text-green-600 dark:bg-green-900/50 dark:text-green-400' : ''}
                      ${activity.type === 'interview' ? 'bg-purple-100 text-purple-600 dark:bg-purple-900/50 dark:text-purple-400' : ''}
                    `}>
                      {activity.type === 'application' && <FileText className="h-5 w-5" />}
                      {activity.type === 'interview' && <Calendar className="h-5 w-5" />}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {activity.type === 'application' ? 'New application for ' : 'Interview scheduled for '}
                        <span className="font-semibold">{activity.title}</span>
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(activity.timestamp).toLocaleString()}
                      </p>
                    </div>
                    
                    <div>
                      {activity.status === 'accepted' && (
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-400">
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          Accepted
                        </Badge>
                      )}
                      {activity.status === 'rejected' && (
                        <Badge className="bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-400">
                          <XCircle className="h-3 w-3 mr-1" />
                          Rejected
                        </Badge>
                      )}
                      {activity.status === 'scheduled' && (
                        <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-400">
                          Scheduled
                        </Badge>
                      )}
                      {activity.status === 'completed' && (
                        <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                          Completed
                        </Badge>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500 dark:text-gray-400">No recent activities</p>
                </div>
              )}
            </div>
          </div>
        </>
      ) : (
        <div className="mt-10">
          <Alert className="mb-6">
            <AlertTriangle className="h-4 w-4 mr-2" />
            <AlertTitle>No BPO Data Found</AlertTitle>
            <AlertDescription>Unable to load BPO data at this time. Please try again later.</AlertDescription>
          </Alert>
          
          <div className="flex flex-col items-center justify-center p-8 bg-gray-50 dark:bg-gray-800 rounded-lg space-y-4">
            <h2 className="text-xl font-semibold text-center">BPO Dashboard Error</h2>
            <p className="text-center text-gray-600 dark:text-gray-400 max-w-md">
              We couldn't load your BPO data. This could be due to a temporary issue.
            </p>
            
            <div className="flex space-x-4 mt-6">
              <Button variant="outline" onClick={() => window.location.reload()}>
                Refresh Page
              </Button>
              <Button onClick={() => router.push('/')}>
                Go to Home
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 