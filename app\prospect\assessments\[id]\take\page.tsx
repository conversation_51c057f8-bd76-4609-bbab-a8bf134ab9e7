import React from 'react';
import { ErrorBoundary } from '@/components/error-boundary';
import { AssessmentTake } from './assessment-take';
import { Suspense } from 'react';
import { AuthGuard } from '@/components/auth/auth-guard';

// Loading fallback for assessment loading
function AssessmentLoading() {
  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="h-10 w-3/4 bg-gray-200 rounded animate-pulse mb-4"></div>
      <div className="h-6 w-1/2 bg-gray-200 rounded animate-pulse mb-8"></div>
      <div className="space-y-4">
        <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-40 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  );
}

// This is a server component that can properly handle params
export default function AssessmentTakePage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params promise with React.use()
  const resolvedParams = React.use(params);
  const id = resolvedParams.id;
  
  return (
    <ErrorBoundary>
      <Suspense fallback={<AssessmentLoading />}>
        <AuthGuard fallbackUrl="/auth/login?next=/prospect/assessments">
          <AssessmentTake assessmentId={id} />
        </AuthGuard>
      </Suspense>
    </ErrorBoundary>
  );
} 