import { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database.types';

/**
 * Check if user is a platform admin using secure function
 * @param supabase Supabase client
 * @param userId User ID to check
 */
export async function isPlatformAdmin(
  supabase: SupabaseClient<Database>,
  userId: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .rpc('is_platform_admin', { user_id_param: userId });

    if (error) {
      console.error('Error checking platform admin status:', error);
      return false;
    }

    return data === true;
  } catch (err) {
    console.error('Error in isPlatformAdmin:', err);
    return false;
  }
}

/**
 * Check if user is a BPO admin for a specific BPO using secure function
 * @param supabase Supabase client
 * @param userId User ID to check
 * @param bpoId BPO ID to check admin status for
 */
export async function isBPOAdmin(
  supabase: SupabaseClient<Database>,
  userId: string,
  bpoId: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .rpc('is_bpo_admin', {
        user_id_param: userId,
        bpo_id_param: bpoId
      });

    if (error) {
      console.error('Error checking BPO admin status:', error);
      return false;
    }

    return data === true;
  } catch (err) {
    console.error('Error in isBPOAdmin:', err);
    return false;
  }
}

/**
 * Get user's BPO memberships using secure function
 * @param supabase Supabase client
 * @param userId User ID to check
 */
export async function getUserBPOMemberships(
  supabase: SupabaseClient<Database>,
  userId: string
) {
  try {
    const { data, error } = await supabase
      .rpc('get_user_bpo_memberships', { user_id_param: userId });

    if (error) {
      // Don't log as error if user simply has no BPO memberships
      if (error.message?.includes('no rows') || error.code === 'PGRST116') {
        return {
          memberships: [],
          error: null
        };
      }

      console.error('Error getting BPO memberships:', error);
      return {
        memberships: [],
        error: error
      };
    }

    return {
      memberships: data || [],
      error: null
    };
  } catch (err) {
    // Don't log as error for prospects who don't have BPO memberships
    console.log('No BPO memberships found for user (this is normal for prospects)');
    return {
      memberships: [],
      error: null // Return null error for graceful handling
    };
  }
}

/**
 * Checks if a user is a member of a BPO team using secure functions
 * @param supabase Supabase client
 * @param userId User ID to check
 * @returns Object containing membership information
 */
export async function checkBPOTeamMembership(
  supabase: SupabaseClient<Database>,
  userId: string
) {
  try {
    const { memberships, error } = await getUserBPOMemberships(supabase, userId);

    if (error) {
      return {
        isMember: false,
        bpoId: null,
        role: null,
        error: error,
        policyError: false
      };
    }

    // Return the first membership (users typically have one BPO)
    const primaryMembership = memberships[0];

    return {
      isMember: !!primaryMembership,
      bpoId: primaryMembership?.bpo_id || null,
      role: primaryMembership?.role || null,
      allMemberships: memberships,
      error: null,
      policyError: false
    };
  } catch (err) {
    console.error('Error checking BPO team membership:', err);
    return {
      isMember: false,
      bpoId: null,
      role: null,
      error: err instanceof Error ? err : new Error('Unknown error checking team membership'),
      policyError: false
    };
  }
}

/**
 * Fetches a user's role from the database
 * @param supabase Supabase client
 * @param userId User ID to check
 * @returns User's role or null on error
 */
export async function getUserRole(
  supabase: SupabaseClient<Database>,
  userId: string
) {
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single();
      
    if (error) {
      console.error('Error fetching user role:', error);
      return null;
    }
    
    return user?.role || null;
  } catch (err) {
    console.error('Error in getUserRole:', err);
    return null;
  }
} 