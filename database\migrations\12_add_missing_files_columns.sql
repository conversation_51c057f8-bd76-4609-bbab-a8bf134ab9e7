-- =============================================================================
-- MIGRATION: Add missing columns to files table
-- =============================================================================
-- This migration adds all the missing columns to the existing files table
-- to match the expected schema for the Files page functionality.

-- Add missing columns to files table
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS assessment_id UUID;
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS file_type VARCHAR(50) NOT NULL DEFAULT 'document';
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS file_category VARCHAR(50) NOT NULL DEFAULT 'uploaded';
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS title VARCHAR(255) NOT NULL DEFAULT 'Untitled File';
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS file_url TEXT NOT NULL DEFAULT '';
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS original_filename VARCHAR(255);
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS file_size INTEGER DEFAULT 0;
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS mime_type VARCHAR(100);
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS issued_at TIMESTAMPTZ DEFAULT NOW();
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS expires_at TIMESTAMPTZ;
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT TRUE;
ALTER TABLE public.files ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- Add foreign key constraint for assessment_id (with error handling)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'fk_files_assessment_id'
    ) THEN
        ALTER TABLE public.files
        ADD CONSTRAINT fk_files_assessment_id
        FOREIGN KEY (assessment_id) REFERENCES public.assessments(id)
        ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;
END $$;

-- Add check constraints (with error handling)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'chk_files_type_valid'
    ) THEN
        ALTER TABLE public.files
        ADD CONSTRAINT chk_files_type_valid
        CHECK (file_type IN ('certificate', 'document', 'training_certificate'));
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'chk_files_category_valid'
    ) THEN
        ALTER TABLE public.files
        ADD CONSTRAINT chk_files_category_valid
        CHECK (file_category IN ('uploaded', 'system_generated'));
    END IF;
END $$;

-- Add indexes for new columns
CREATE INDEX IF NOT EXISTS idx_files_type ON public.files(file_type);
CREATE INDEX IF NOT EXISTS idx_files_category ON public.files(file_category);
CREATE INDEX IF NOT EXISTS idx_files_issued_at ON public.files(issued_at);
CREATE INDEX IF NOT EXISTS idx_files_assessment_id ON public.files(assessment_id);

-- Add update timestamp trigger if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'update_files_timestamp' 
        AND tgrelid = 'public.files'::regclass
    ) THEN
        CREATE TRIGGER update_files_timestamp
        BEFORE UPDATE ON public.files
        FOR EACH ROW
        EXECUTE FUNCTION update_timestamp();
    END IF;
END $$;

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Verify the files table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'files'
ORDER BY ordinal_position;

-- Show current files count
SELECT COUNT(*) as files_count FROM public.files;
