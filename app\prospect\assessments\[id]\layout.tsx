import React from 'react';
import { Metadata } from 'next';

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  // Properly await params since it's a Promise in newer Next.js
  const resolvedParams = await params;
  const id = resolvedParams.id;
  
  // Convert kebab-case to title case
  const title = id
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return {
    title: `${title} Assessment | BPO Training Platform`,
    description: "Complete this assessment to showcase your skills and abilities",
  };
}

export default function AssessmentLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
} 