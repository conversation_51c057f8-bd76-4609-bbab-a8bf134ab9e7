import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Create a test interview
    const testInterview = {
      bpo_user_id: session.user.id,
      scheduled_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      duration_minutes: 60,
      status: 'scheduled',
      interview_type: 'video_call',
      location: 'virtual',
      notes: 'Test interview created for debugging',
      meeting_link: 'https://zoom.us/j/test123456'
    };

    const { data: interview, error: interviewError } = await supabase
      .from('interviews')
      .insert(testInterview)
      .select()
      .single();

    if (interviewError) {
      console.error('Error creating test interview:', interviewError);
      return NextResponse.json(
        { error: 'Failed to create test interview', details: interviewError },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      interview,
      message: 'Test interview created successfully'
    });

  } catch (error) {
    console.error('Error in create test interview API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
