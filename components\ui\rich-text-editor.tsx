'use client';

import { use<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Menu, type Editor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import { useState, useEffect, useCallback } from 'react';
import { 
  Bold, Italic, List, ListOrdered, Quote, Link as LinkIcon, 
  Image as ImageIcon, AlignLeft, AlignCenter, AlignRight, Heading1, Heading2,
  ClipboardPaste, Indent, Outdent
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Extension } from '@tiptap/core';
import { EditorState, Transaction } from '@tiptap/pm/state';
import { Node as ProseMirrorNode } from '@tiptap/pm/model';
import { Command, CommandProps } from '@tiptap/core';

// Extend the global ChainedCommands interface to include our custom commands
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    indent: {
      /**
       * Indent content
       */
      indent: () => ReturnType;
      /**
       * Outdent content
       */
      outdent: () => ReturnType;
    }
  }
}

// Custom extension for indentation
const IndentExtension = Extension.create({
  name: 'indent',
  addGlobalAttributes() {
    return [
      {
        types: ['paragraph', 'heading', 'blockquote'],
        attributes: {
          indent: {
            default: 0,
            renderHTML: attributes => {
              if (!attributes.indent) {
                return {};
              }
              return {
                style: `margin-left: ${attributes.indent * 2}em`,
              };
            },
            parseHTML: element => {
              const style = element.getAttribute('style') || '';
              const marginLeft = style.match(/margin-left: (\d+)em/);
              if (marginLeft) {
                return parseInt(marginLeft[1], 10) / 2;
              }
              return 0;
            },
          },
        },
      },
      {
        // Handle lists properly to keep bullets/numbers with text
        types: ['bulletList', 'orderedList'],
        attributes: {
          indent: {
            default: 0,
            renderHTML: attributes => {
              if (!attributes.indent) {
                return {};
              }
              return {
                class: `indent-${attributes.indent}`,
                style: `margin-left: ${attributes.indent * 2}em`,
              };
            },
            parseHTML: element => {
              // Check for our custom class first
              const className = element.getAttribute('class') || '';
              const indentClass = className.match(/indent-(\d+)/);
              if (indentClass) {
                return parseInt(indentClass[1], 10);
              }
              
              // Fall back to style-based parsing
              const style = element.getAttribute('style') || '';
              const marginLeft = style.match(/margin-left: (\d+)em/);
              if (marginLeft) {
                return parseInt(marginLeft[1], 10) / 2;
              }
              return 0;
            },
          },
        },
      },
    ];
  },
  addCommands() {
    return {
      indent: () => ({ tr, state, dispatch }: CommandProps) => {
        const { selection } = state;
        const { from, to } = selection;
        
        let modified = false;
        
        state.doc.nodesBetween(from, to, (node: ProseMirrorNode, pos: number) => {
          const nodeType = node.type;
          // Apply indentation to the whole list rather than individual items
          if (nodeType.name === 'paragraph' || nodeType.name === 'heading' || 
              nodeType.name === 'blockquote' || nodeType.name === 'bulletList' || 
              nodeType.name === 'orderedList') {
            const indent = node.attrs.indent || 0;
            if (indent < 5) { // Maximum indentation level
              if (dispatch) {
                tr.setNodeMarkup(pos, undefined, {
                  ...node.attrs,
                  indent: indent + 1,
                });
              }
              modified = true;
            }
          }
          return true;
        });
        
        return modified;
      },
      outdent: () => ({ tr, state, dispatch }: CommandProps) => {
        const { selection } = state;
        const { from, to } = selection;
        
        let modified = false;
        
        state.doc.nodesBetween(from, to, (node: ProseMirrorNode, pos: number) => {
          const nodeType = node.type;
          // Apply outdentation to the whole list rather than individual items
          if (nodeType.name === 'paragraph' || nodeType.name === 'heading' || 
              nodeType.name === 'blockquote' || nodeType.name === 'bulletList' || 
              nodeType.name === 'orderedList') {
            const indent = node.attrs.indent || 0;
            if (indent > 0) {
              if (dispatch) {
                tr.setNodeMarkup(pos, undefined, {
                  ...node.attrs,
                  indent: indent - 1,
                });
              }
              modified = true;
            }
          }
          return true;
        });
        
        return modified;
      },
    };
  },
});

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  minHeight?: string;
}

export function RichTextEditor({
  value,
  onChange,
  placeholder = 'Write something...',
  minHeight = '200px',
}: RichTextEditorProps) {
  const [isMounted, setIsMounted] = useState(false);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {
          keepMarks: true,
          keepAttributes: true,
          HTMLAttributes: {
            class: 'list-disc pl-6 my-2',
          },
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: true,
          HTMLAttributes: {
            class: 'list-decimal pl-6 my-2',
          },
        },
        listItem: {
          HTMLAttributes: {
            class: 'pl-1 my-1',
          },
        },
        code: {
          HTMLAttributes: {
            class: 'bg-muted px-1.5 py-0.5 rounded font-mono text-sm',
          },
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary underline underline-offset-4',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-md max-w-full',
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
      IndentExtension,
    ],
    content: value,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose dark:prose-invert max-w-none focus:outline-none',
      },
      transformPastedHTML(html) {
        // Clean potentially unsafe HTML while preserving important formatting
        return html
          // Keep the basic structure but clean anything potentially harmful
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
          .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
          // Keep the formatting tags that we want to support
          .replace(/class="[^"]*"/g, '')
          .replace(/style="[^"]*"/g, '');
      },
      handlePaste: (view, event, slice) => {
        if (!event.clipboardData) return false;
        
        // Handle HTML pasting
        if (event.clipboardData.types.includes('text/html')) {
          const html = event.clipboardData.getData('text/html');
          
          // Let the editor handle the paste from the cleaned HTML
          const doc = new DOMParser().parseFromString(html, 'text/html');
          const cleanedHtml = doc.body.innerHTML;
          
          // Try to preserve structure like lists, headings, etc.
          if (html.indexOf('<li>') > -1 || html.indexOf('<h') > -1 || html.indexOf('<p>') > -1) {
            if (editor) {
              editor.commands.insertContent(cleanedHtml);
              return true;
            }
          }
        }
        
        // Handle plain text pasting
        if (event.clipboardData.types.includes('text/plain')) {
          const text = event.clipboardData.getData('text/plain');
          if (text) {
            // Check if text has multiple lines or other formatting hints
            if (text.includes('\n\n') || text.includes('• ') || /^\d+\.\s/.test(text)) {
              let formattedText = text;
              
              // Convert bullet points to list items
              if (text.includes('• ')) {
                const bulletLines = text.split('\n').filter(line => line.trim().startsWith('• '));
                if (bulletLines.length > 0 && editor) {
                  const listItems = bulletLines.map(line => 
                    `<li>${line.trim().substring(2)}</li>`
                  ).join('');
                  editor.commands.insertContent(`<ul>${listItems}</ul>`);
                  return true;
                }
              }
              
              // Convert numbered lists
              if (/^\d+\.\s/.test(text)) {
                const numberedLines = text.split('\n').filter(line => /^\d+\.\s/.test(line.trim()));
                if (numberedLines.length > 0 && editor) {
                  const listItems = numberedLines.map(line => 
                    `<li>${line.trim().replace(/^\d+\.\s/, '')}</li>`
                  ).join('');
                  editor.commands.insertContent(`<ol>${listItems}</ol>`);
                  return true;
                }
              }
              
              // Handle regular paragraphs
              formattedText = formattedText
                .replace(/\n\n/g, '</p><p>') // Paragraphs
                .replace(/\n/g, '<br>'); // Line breaks
              
              if (editor) {
                editor.commands.insertContent(`<p>${formattedText}</p>`);
                return true;
              }
            }
          }
        }
        
        // Let the editor handle other paste events normally
        return false;
      },
    },
    parseOptions: {
      preserveWhitespace: 'full',
    },
  });

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value, false);
    }
  }, [editor, value]);

  const addLink = useCallback(() => {
    if (!editor) return;
    
    const url = window.prompt('URL');
    
    if (url) {
      editor
        .chain()
        .focus()
        .extendMarkRange('link')
        .setLink({ href: url })
        .run();
    }
  }, [editor]);

  const addImage = useCallback(() => {
    if (!editor) return;
    
    const url = window.prompt('URL');
    
    if (url) {
      editor
        .chain()
        .focus()
        .setImage({ src: url })
        .run();
    }
  }, [editor]);

  const pasteAsPlainText = useCallback(() => {
    if (!editor) return;
    
    navigator.clipboard.readText().then(text => {
      if (text) {
        // Insert as plain text, without any formatting
        editor.commands.insertContent(text);
      }
    }).catch(err => {
      console.error('Failed to read clipboard contents: ', err);
    });
  }, [editor]);

  const indentText = useCallback(() => {
    if (!editor) return;
    // Use the commands directly to avoid TypeScript errors
    editor.commands.indent();
  }, [editor]);

  const outdentText = useCallback(() => {
    if (!editor) return;
    // Use the commands directly to avoid TypeScript errors
    editor.commands.outdent();
  }, [editor]);

  if (!isMounted) {
    return null;
  }

  return (
    <div 
      className="relative border rounded-md overflow-hidden focus-within:ring-1 focus-within:ring-ring"
      style={{ minHeight }}
    >
      {editor && (
        <BubbleMenu
          editor={editor}
          tippyOptions={{ duration: 150 }}
          className="bg-white shadow rounded-md border overflow-hidden flex divide-x"
        >
          <ToolbarButtonGroup>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBold().run()}
              active={editor.isActive('bold')}
              title="Bold"
              icon={<Bold className="h-4 w-4" />}
            />
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleItalic().run()}
              active={editor.isActive('italic')}
              title="Italic"
              icon={<Italic className="h-4 w-4" />}
            />
          </ToolbarButtonGroup>

          <ToolbarButtonGroup>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
              active={editor.isActive('heading', { level: 1 })}
              title="Heading 1"
              icon={<Heading1 className="h-4 w-4" />}
            />
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
              active={editor.isActive('heading', { level: 2 })}
              title="Heading 2"
              icon={<Heading2 className="h-4 w-4" />}
            />
          </ToolbarButtonGroup>

          <ToolbarButtonGroup>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              active={editor.isActive('bulletList')}
              title="Bullet List"
              icon={<List className="h-4 w-4" />}
            />
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              active={editor.isActive('orderedList')}
              title="Ordered List"
              icon={<ListOrdered className="h-4 w-4" />}
            />
            <ToolbarButton
              onClick={indentText}
              title="Indent"
              icon={<Indent className="h-4 w-4" />}
            />
            <ToolbarButton
              onClick={outdentText}
              title="Outdent"
              icon={<Outdent className="h-4 w-4" />}
            />
          </ToolbarButtonGroup>

          <ToolbarButtonGroup>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              active={editor.isActive('blockquote')}
              title="Quote"
              icon={<Quote className="h-4 w-4" />}
            />
          </ToolbarButtonGroup>
          
          <ToolbarButtonGroup>
            <ToolbarButton
              onClick={addLink}
              active={editor.isActive('link')}
              title="Link"
              icon={<LinkIcon className="h-4 w-4" />}
            />
            <ToolbarButton
              onClick={addImage}
              title="Image"
              icon={<ImageIcon className="h-4 w-4" />}
            />
          </ToolbarButtonGroup>

          <ToolbarButtonGroup>
            <ToolbarButton
              onClick={pasteAsPlainText}
              title="Paste as plain text"
              icon={<ClipboardPaste className="h-4 w-4" />}
            />
          </ToolbarButtonGroup>
        </BubbleMenu>
      )}

      <div className="bg-muted p-2 flex flex-wrap gap-1 border-b">
        <ToolbarButton
          onClick={() => editor?.chain().focus().toggleBold().run()}
          active={editor?.isActive('bold')}
          title="Bold"
          icon={<Bold className="h-4 w-4" />}
        />
        <ToolbarButton
          onClick={() => editor?.chain().focus().toggleItalic().run()}
          active={editor?.isActive('italic')}
          title="Italic"
          icon={<Italic className="h-4 w-4" />}
        />
        <ToolbarButton
          onClick={() => editor?.chain().focus().toggleHeading({ level: 1 }).run()}
          active={editor?.isActive('heading', { level: 1 })}
          title="Heading 1"
          icon={<Heading1 className="h-4 w-4" />}
        />
        <ToolbarButton
          onClick={() => editor?.chain().focus().toggleHeading({ level: 2 }).run()}
          active={editor?.isActive('heading', { level: 2 })}
          title="Heading 2"
          icon={<Heading2 className="h-4 w-4" />}
        />
        <ToolbarButton
          onClick={() => editor?.chain().focus().toggleBulletList().run()}
          active={editor?.isActive('bulletList')}
          title="Bullet List"
          icon={<List className="h-4 w-4" />}
        />
        <ToolbarButton
          onClick={() => editor?.chain().focus().toggleOrderedList().run()}
          active={editor?.isActive('orderedList')}
          title="Ordered List"
          icon={<ListOrdered className="h-4 w-4" />}
        />
        <ToolbarButton
          onClick={indentText}
          title="Indent"
          icon={<Indent className="h-4 w-4" />}
        />
        <ToolbarButton
          onClick={outdentText}
          title="Outdent"
          icon={<Outdent className="h-4 w-4" />}
        />
        <ToolbarButton
          onClick={() => editor?.chain().focus().toggleBlockquote().run()}
          active={editor?.isActive('blockquote')}
          title="Quote"
          icon={<Quote className="h-4 w-4" />}
        />
        <ToolbarButton
          onClick={addLink}
          active={editor?.isActive('link')}
          title="Link"
          icon={<LinkIcon className="h-4 w-4" />}
        />
        <ToolbarButton
          onClick={addImage}
          title="Image"
          icon={<ImageIcon className="h-4 w-4" />}
        />
        <ToolbarButton
          onClick={pasteAsPlainText}
          title="Paste as plain text"
          icon={<ClipboardPaste className="h-4 w-4" />}
        />
      </div>

      <div className="p-3 prose dark:prose-invert prose-ul:list-disc prose-ol:list-decimal max-w-none">
        <EditorContent editor={editor} />
      </div>
    </div>
  );
}

interface ToolbarButtonProps {
  onClick: () => void;
  icon: React.ReactNode;
  active?: boolean;
  title?: string;
}

function ToolbarButton({ onClick, icon, active, title }: ToolbarButtonProps) {
  return (
    <Button
      variant="ghost"
      size="sm"
      type="button"
      className={cn("h-8 px-2 text-muted-foreground", active && "bg-accent text-accent-foreground")}
      onClick={onClick}
      title={title}
    >
      {icon}
    </Button>
  );
}

interface ToolbarButtonGroupProps {
  children: React.ReactNode;
}

function ToolbarButtonGroup({ children }: ToolbarButtonGroupProps) {
  return <div className="flex items-center divide-x">{children}</div>;
} 