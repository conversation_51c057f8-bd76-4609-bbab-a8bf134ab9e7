// This script tests the delete API directly to see what's happening
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testDeleteDirect() {
  console.log('🧪 Testing Delete API directly...\n');

  try {
    // First, let's see if we can get the current user
    console.log('1️⃣ Checking authentication...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('❌ Auth error:', authError);
      console.log('ℹ️  This is expected if running outside of authenticated context');
    } else if (user) {
      console.log('✅ User authenticated:', user.id);
    } else {
      console.log('ℹ️  No authenticated user (expected in script context)');
    }

    // Let's check what files exist
    console.log('\n2️⃣ Checking available files...');
    const { data: files, error: filesError } = await supabase
      .from('files')
      .select('id, title, prospect_id, file_type')
      .limit(5);

    if (filesError) {
      console.error('❌ Files query error:', filesError);
    } else {
      console.log(`✅ Found ${files?.length || 0} files`);
      files?.forEach(file => {
        console.log(`  - ${file.title} (${file.id}) - Prospect: ${file.prospect_id}`);
      });
    }

    // Let's check users table
    console.log('\n3️⃣ Checking users table structure...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, role')
      .limit(3);

    if (usersError) {
      console.error('❌ Users query error:', usersError);
    } else {
      console.log(`✅ Found ${users?.length || 0} users`);
      users?.forEach(user => {
        console.log(`  - User ${user.id}: ${user.role}`);
      });
    }

    // Let's check prospects table
    console.log('\n4️⃣ Checking prospects table...');
    const { data: prospects, error: prospectsError } = await supabase
      .from('prospects')
      .select('id, user_id')
      .limit(3);

    if (prospectsError) {
      console.error('❌ Prospects query error:', prospectsError);
    } else {
      console.log(`✅ Found ${prospects?.length || 0} prospects`);
      prospects?.forEach(prospect => {
        console.log(`  - Prospect ${prospect.id}: User ${prospect.user_id}`);
      });
    }

    // Test the actual API endpoint structure
    console.log('\n5️⃣ Testing API endpoint accessibility...');
    
    // Try to make a request to the API (this will fail due to auth, but we can see the error)
    try {
      const response = await fetch('http://localhost:3000/api/files/test-id', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      console.log('API Response status:', response.status);
      const text = await response.text();
      console.log('API Response:', text);
    } catch (fetchError) {
      console.log('ℹ️  Fetch error (expected):', fetchError.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testDeleteDirect();
