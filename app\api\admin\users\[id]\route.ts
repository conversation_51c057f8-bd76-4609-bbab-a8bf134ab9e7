import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/types/database.types';

export async function PATCH(
  req: NextRequest, 
  { params }: { params: { id: string } }
) {
  try {
    // Get session to verify user is authenticated and an admin
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized - You must be logged in' },
        { status: 401 }
      );
    }
    
    // Verify user is an admin
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();
    
    if (userError || !userData || userData.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - You must be an admin to perform this action' },
        { status: 403 }
      );
    }
    
    // Get the user ID from the URL
    const userId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { status } = body;
    
    // Validate required fields
    if (!status || !['active', 'inactive'].includes(status)) {
      return NextResponse.json(
        { error: 'Status is required and must be either "active" or "inactive"' },
        { status: 400 }
      );
    }
    
    // Update user status
    const { error: updateError } = await supabase
      .from('users')
      .update({ status })
      .eq('id', userId);
    
    if (updateError) {
      return NextResponse.json(
        { error: updateError.message || 'Failed to update user status' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ 
      success: true, 
      message: `User ${status === 'active' ? 'activated' : 'deactivated'} successfully` 
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
} 