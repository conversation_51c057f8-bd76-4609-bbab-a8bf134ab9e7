import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

// Create admin client for bypassing RLS policies
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || "";
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey);

// Secret API key for authorization 
// This should be set in your environment variables
const API_SECRET_KEY = process.env.API_SECRET_KEY || "bpo-training-platform-api-key";

export async function POST(req: NextRequest) {
  try {
    // Verify the authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || authHeader !== `Bearer ${API_SECRET_KEY}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Parse the request body
    const body = await req.json();
    const { table, id, data } = body;
    
    // Validate the request
    if (!table || !id || !data) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Only allow specific tables for security
    const allowedTables = ['prospects', 'users', 'certificates', 'progress_records'];
    if (!allowedTables.includes(table)) {
      return NextResponse.json({ error: 'Invalid table' }, { status: 400 });
    }
    
    console.log(`Direct Update API - Updating ${table} with ID ${id}`);
    console.log('Direct Update API - Data:', data);
    
    // Perform the direct update using the admin client (bypasses RLS)
    const { data: updateResult, error } = await adminClient
      .from(table)
      .update(data)
      .eq('id', id)
      .select();
    
    if (error) {
      console.error('Direct Update API - Error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    console.log('Direct Update API - Success:', updateResult);
    
    return NextResponse.json({
      success: true,
      message: 'Update successful',
      data: updateResult
    });
  } catch (error: any) {
    console.error('Direct Update API - Error:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 