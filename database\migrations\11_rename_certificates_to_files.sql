-- =============================================================================
-- MIGRATION: Rename certificates table to files and update structure
-- =============================================================================

-- First, create the new files table with enhanced structure
CREATE TABLE public.files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    prospect_id UUID NOT NULL,
    module_id UUID,
    assessment_id UUID,
    file_type VARCHAR(50) NOT NULL, -- 'certificate', 'document', 'training_certificate'
    file_category VARCHAR(50) NOT NULL, -- 'uploaded', 'system_generated'
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_url TEXT NOT NULL, -- URL to the file
    original_filename VARCHAR(255), -- Original filename when uploaded
    file_size INTEGER, -- File size in bytes
    mime_type VARCHAR(100), -- MIME type of the file
    issued_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    verification_code VARCHAR(100) UNIQUE,
    is_verified BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Copy data from certificates table to files table
INSERT INTO public.files (
    id, prospect_id, module_id, assessment_id, file_type, file_category,
    title, description, file_url, issued_at, expires_at, verification_code,
    is_verified, metadata, created_at, updated_at
)
SELECT 
    id, prospect_id, module_id, assessment_id, 
    CASE 
        WHEN certificate_type = 'module_completion' THEN 'training_certificate'
        WHEN certificate_type = 'assessment_pass' THEN 'training_certificate'
        WHEN certificate_type = 'course_completion' THEN 'training_certificate'
        ELSE 'certificate'
    END as file_type,
    'system_generated' as file_category,
    title, description, certificate_url as file_url, issued_at, expires_at, 
    verification_code, is_verified, metadata, created_at, updated_at
FROM public.certificates;

-- Add foreign key constraints for files table
ALTER TABLE public.files 
ADD CONSTRAINT fk_files_prospect_id 
FOREIGN KEY (prospect_id) REFERENCES public.prospects(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.files 
ADD CONSTRAINT fk_files_module_id 
FOREIGN KEY (module_id) REFERENCES public.training_modules(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE public.files 
ADD CONSTRAINT fk_files_assessment_id 
FOREIGN KEY (assessment_id) REFERENCES public.assessments(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Add check constraints
ALTER TABLE public.files 
ADD CONSTRAINT chk_files_type_valid 
CHECK (file_type IN ('certificate', 'document', 'training_certificate'));

ALTER TABLE public.files 
ADD CONSTRAINT chk_files_category_valid 
CHECK (file_category IN ('uploaded', 'system_generated'));

-- Add indexes for files table
CREATE INDEX idx_files_prospect_id ON public.files(prospect_id);
CREATE INDEX idx_files_module_id ON public.files(module_id);
CREATE INDEX idx_files_assessment_id ON public.files(assessment_id);
CREATE INDEX idx_files_type ON public.files(file_type);
CREATE INDEX idx_files_category ON public.files(file_category);
CREATE INDEX idx_files_verification_code ON public.files(verification_code);
CREATE INDEX idx_files_issued_at ON public.files(issued_at);

-- Add update timestamp trigger
CREATE TRIGGER update_files_timestamp
BEFORE UPDATE ON public.files
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- Drop the old certificates table (after confirming data migration)
-- DROP TABLE public.certificates CASCADE;

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Verify data migration
-- SELECT 
--     'certificates' as table_name, COUNT(*) as count 
-- FROM public.certificates
-- UNION ALL
-- SELECT 
--     'files' as table_name, COUNT(*) as count 
-- FROM public.files;

-- Verify file types distribution
-- SELECT file_type, file_category, COUNT(*) as count
-- FROM public.files
-- GROUP BY file_type, file_category
-- ORDER BY file_type, file_category;
