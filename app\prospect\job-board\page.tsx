import { JobBoardPage } from "@/components/job-board-page"
import { cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { Database } from "@/types/supabase"
import { format } from "date-fns"

// Force dynamic rendering for this page due to authentication requirements
export const dynamic = 'force-dynamic'

export default async function JobBoard() {
  const supabase = createServerComponentClient<Database>({ cookies })
  
  // Fetch published job postings
  const { data: jobPostings, error } = await supabase
    .from("job_postings")
    .select(`
      *,
      bpos(id, name, logo_url)
    `)
    .eq("status", "published")
    .order("created_at", { ascending: false })
  
  if (error) {
    console.error("Error fetching job postings:", error)
  }

  // Format job postings data for the component
  const formattedJobs = jobPostings?.map(job => {
    // Format creation date for "posted X days ago" display
    const createdDate = job.created_at ? new Date(job.created_at) : new Date()
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - createdDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    // Parse salary range if it exists
    let salaryDisplay = "Not specified"
    if (job.salary_range && typeof job.salary_range === 'object') {
      const salaryRange = job.salary_range as any
      if (salaryRange.min && salaryRange.max) {
        salaryDisplay = `$${salaryRange.min.toLocaleString()} - $${salaryRange.max.toLocaleString()}`
      } else if (salaryRange.min) {
        salaryDisplay = `From $${salaryRange.min.toLocaleString()}`
      } else if (salaryRange.max) {
        salaryDisplay = `Up to $${salaryRange.max.toLocaleString()}`
      }
    }
    
    // Format location
    let locationDisplay = "Remote"
    if (job.location && typeof job.location === 'object') {
      const location = job.location as any
      if (location.city && location.country) {
        locationDisplay = `${location.city}, ${location.country}`
      } else if (location.city) {
        locationDisplay = location.city
      } else if (location.country) {
        locationDisplay = location.country
      } else if (location.remote) {
        locationDisplay = "Remote"
      }
    }
    
    // Format required skills as tags
    const skillTags = Array.isArray(job.required_skills) 
      ? job.required_skills
          .filter((skill): skill is NonNullable<typeof skill> => skill !== null)
          .map(skill => {
            if (typeof skill === 'string') return skill;
            if (typeof skill === 'object' && skill !== null && 'name' in skill) {
              return skill.name as string || '';
            }
            return '';
          })
          .filter(Boolean)
      : []
    
    return {
      id: job.id,
      title: job.title,
      company: job.bpos?.name || "Unknown Company",
      companyLogo: job.bpos?.logo_url || null,
      location: locationDisplay,
      jobType: job.job_type,
      salary: salaryDisplay,
      postedDate: `Posted ${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`,
      description: job.description,
      skills: skillTags,
      isNew: diffDays <= 2,
      deadline: job.application_deadline 
        ? format(new Date(job.application_deadline), 'MMM dd, yyyy')
        : null
    }
  }) || []
  
  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <JobBoardPage jobs={formattedJobs} />
    </div>
  )
} 