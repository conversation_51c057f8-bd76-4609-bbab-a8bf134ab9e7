import { ModuleCard } from "@/components/ui/module-card"
import { ProgressOverview } from "@/components/ui/progress-indicator"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from '@supabase/supabase-js'
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { Database } from "@/types/supabase"

// Create admin client for operations that need to bypass RLS
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ""
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ""
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey)

export default async function Training() {
  const supabase = createServerComponentClient<Database>({ cookies })
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect("/login")
  }
  
  // Get the prospect ID from the users table
  const { data: userData, error: userError } = await supabase
    .from("users")
    .select("id, role")
    .eq("id", session.user.id)
    .single()
  
  if (userError || !userData || userData.role !== "prospect") {
    redirect("/unauthorized")
  }
  
  // Get the prospect's profile with admin client to bypass RLS
  let prospectData = null
  
  try {
    // Using the admin client that bypasses RLS policies
    const { data, error } = await adminClient
      .from("prospects")
      .select("id, training_status")
      .eq("user_id", userData.id)
    
    if (error) {
      throw error
    }
    
    // Take the first matching prospect if found
    if (data && data.length > 0) {
      prospectData = data[0]
    }
  } catch (error: any) {
    console.error("Error fetching prospect data:", error.message || error)
    // Continue without prospect data - will show modules without progress
  }
  
  const prospectId = prospectData?.id
  
  // Fetch published training modules using admin client to bypass RLS
  const { data: modules, error: modulesError } = await adminClient
    .from("training_modules")
    .select(`
      *,
      lessons: lessons(count)
    `)
    .eq("status", "published")
    .order("required_order", { ascending: true })

  if (modulesError) {
    console.error("Error fetching modules:", modulesError)
  }

  // Debug logging
  console.log("Modules query result:", { modules, modulesError })
  console.log("Number of modules found:", modules?.length || 0)
  
  // Fetch progress records for this prospect using admin client to bypass RLS
  let progress = null
  if (prospectId) {
    try {
      const { data: progressData, error: progressError } = await adminClient
        .from("progress_records")
        .select(`
          id,
          activity_id,
          status,
          score,
          completed_at,
          activities: activities(
            id,
            lesson_id
          )
        `)
        .eq("prospect_id", prospectId)
      
      if (progressError) {
        throw progressError
      }
      
      progress = progressData
    } catch (error: any) {
      console.error("Error fetching progress data:", error.message || error)
      // Continue without progress data
    }
  }
  
  // Calculate overall progress statistics
  const completedModules = []
  const inProgressModules = []
  const notStartedModules = []
  let totalLearningTimeHours = 0
  
  if (modules && progress) {
    for (const module of modules) {
      try {
        // First get all lessons for this module
        const { data: moduleLessons, error: lessonsError } = await adminClient
          .from("lessons")
          .select("id, title")
          .eq("module_id", module.id)
          .order("order_index", { ascending: true })
        
        if (lessonsError) {
          throw lessonsError
        }
        
        if (!moduleLessons || moduleLessons.length === 0) {
          // No lessons, consider not started
          notStartedModules.push({
            ...module,
            progressPercentage: 0
          })
          continue
        }
        
        // Now get all activities for each lesson
        const completedLessons = []
        const inProgressLessons = []
        
        for (const lesson of moduleLessons) {
          const { data: lessonActivities, error: activitiesError } = await adminClient
            .from("activities")
            .select("id")
            .eq("lesson_id", lesson.id)
          
          if (activitiesError) {
            console.error(`Error fetching activities for lesson ${lesson.id}:`, activitiesError)
            continue
          }
          
          // If no activities, this lesson can't be tracked, skip it
          if (!lessonActivities || lessonActivities.length === 0) {
            continue
          }
          
          // Check if all activities for this lesson are completed
          const activityIds = lessonActivities.map(activity => activity.id)
          const completedActivities = progress.filter(
            p => activityIds.includes(p.activity_id) && p.status === "completed"
          )
          
          // If all activities are completed, count the lesson as completed
          if (completedActivities.length === lessonActivities.length) {
            completedLessons.push(lesson)
          } else if (completedActivities.length > 0) {
            inProgressLessons.push(lesson)
          }
        }
        
        // Calculate progress percentage based on completed lessons
        const progressPercentage = Math.round((completedLessons.length / moduleLessons.length) * 100)
        
        console.log(`Module ${module.title}: ${completedLessons.length}/${moduleLessons.length} lessons completed (${progressPercentage}%)`)
          
        // Add duration to total learning time if available
        if (module.duration_minutes) {
          totalLearningTimeHours += module.duration_minutes / 60
        }
        
        // Determine module status
        if (progressPercentage === 100) {
          // Add the module with its progress percentage to the completed modules
          completedModules.push({
            ...module, 
            progressPercentage
          })
        } else if (progressPercentage > 0 || inProgressLessons.length > 0) {
          inProgressModules.push({
            ...module, 
            progressPercentage
          })
        } else {
          notStartedModules.push({
            ...module, 
            progressPercentage: 0
          })
        }
      } catch (error: any) {
        console.error("Error calculating module progress:", error.message || error)
        continue
      }
    }
  } else if (modules) {
    // No progress data available, consider all modules as not started
    notStartedModules.push(...modules.map(module => ({...module, progressPercentage: 0})))
    
    // Still calculate total learning time
    for (const module of modules) {
      if (module.duration_minutes) {
        totalLearningTimeHours += module.duration_minutes / 60
      }
    }
  }
  
  // For debugging
  console.log("Completed modules count:", completedModules.length);
  console.log("Completed modules:", completedModules.map(m => m.title));
  
  // Calculate total progress for logging
  const totalModules = completedModules.length + inProgressModules.length + notStartedModules.length;
  let totalProgressSum = 0;
  
  // Add up all progress percentages
  completedModules.forEach(m => totalProgressSum += 100);
  inProgressModules.forEach(m => totalProgressSum += m.progressPercentage);
  // Not started modules have 0% progress
  
  const overallProgressPercentage = totalModules > 0 ? Math.round(totalProgressSum / totalModules) : 0;
  console.log("Overall progress percentage:", overallProgressPercentage + "%");

  // Prepare data for TrainingPage component
  const trainingData = {
    modules: [
      ...completedModules,
      ...inProgressModules,
      ...notStartedModules
    ],
    progress,
    stats: {
      completed: completedModules.length,
      inProgress: inProgressModules.length,
      notStarted: notStartedModules.length,
      totalCount: modules?.length || 0,
      learningTimeHours: totalLearningTimeHours.toFixed(1)
    },
    continueLearning: [...inProgressModules].sort((a, b) => b.progressPercentage - a.progressPercentage).slice(0, 3),
    prospectData
  }

  // Debug logging for training data
  console.log("Training data being passed to component:", {
    modulesCount: trainingData.modules?.length || 0,
    rawModulesCount: modules?.length || 0,
    completedCount: completedModules.length,
    inProgressCount: inProgressModules.length,
    notStartedCount: notStartedModules.length,
    progressCount: progress?.length || 0,
    stats: trainingData.stats
  })
  
  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <OriginalTrainingPage trainingData={trainingData} />
    </div>
  )
}

// Original design TrainingPage component with proper UI components
function OriginalTrainingPage({ trainingData }: { trainingData: any }) {
  console.log("🔥 TrainingPage component called with data:", trainingData)

  if (!trainingData) {
    console.error("❌ TrainingPage: No trainingData provided")
    return <div className="p-8 text-center text-red-500">Error: No training data provided</div>
  }

  const { modules, stats, continueLearning } = trainingData

  console.log("✅ TrainingPage processing data:", {
    modulesCount: modules?.length || 0,
    statsReceived: !!stats
  })

  try {
    console.log("🎯 TrainingPage attempting to render...")

    return (
      <div className="space-y-8">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold">Training</h1>
          <p className="text-gray-600 mt-1">Access your training modules and practice sessions</p>
        </div>

        {/* Progress Overview using the original component */}
        <ProgressOverview
          stats={{
            completed: stats.completed,
            inProgress: stats.inProgress,
            notStarted: stats.notStarted,
            total: stats.totalCount
          }}
          title="Your Learning Progress"
          description={
            stats.completed > 0
              ? `Great progress! You've completed ${stats.completed} module${stats.completed > 1 ? 's' : ''}.`
              : stats.inProgress > 0
                ? `You're making progress on ${stats.inProgress} module${stats.inProgress > 1 ? 's' : ''}.`
                : "Start your training journey today!"
          }
          timeSpent={`${stats.learningTimeHours} hours total`}
        />

        {/* Training Modules - Horizontal Layout */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Your Modules</h2>

          {modules && modules.length > 0 ? (
            <Carousel
              opts={{
                align: "start",
                loop: false,
              }}
              className="w-full"
            >
              <CarouselContent className="-ml-2 md:-ml-4">
                {modules.map((module: any) => (
                  <CarouselItem key={module.id} className="pl-2 md:pl-4 basis-full sm:basis-1/2 lg:basis-1/3">
                    <ModuleCard
                      module={{
                        id: module.id,
                        title: module.title,
                        description: module.description,
                        cover_image_url: module.cover_image_url,
                        duration_minutes: module.duration_minutes,
                        progressPercentage: module.progressPercentage
                      }}
                      href={`/prospect/training/${module.id}`}
                      variant="default"
                      showProgress={true}
                      showDuration={true}
                      showDescription={false}
                    />
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious />
              <CarouselNext />
            </Carousel>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>No modules found</p>
            </div>
          )}
        </div>

        {/* Continue Learning Section - Below the horizontal modules */}
        {continueLearning && continueLearning.length > 0 && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Continue Learning</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {continueLearning.map((module: any) => (
                <ModuleCard
                  key={module.id}
                  module={{
                    id: module.id,
                    title: module.title,
                    description: module.description,
                    cover_image_url: module.cover_image_url,
                    duration_minutes: module.duration_minutes,
                    progressPercentage: module.progressPercentage
                  }}
                  href={`/prospect/training/${module.id}`}
                  variant="default"
                  showProgress={true}
                  showDuration={true}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    )
  } catch (error) {
    console.error("💥 Error in TrainingPage render:", error)
    return (
      <div className="p-8 text-center text-red-500">
        <h2 className="text-xl font-bold mb-2">Error Rendering Training Page</h2>
        <p>Please check the console for details.</p>
        <pre className="text-xs bg-gray-100 p-2 mt-2 text-left">
          {error instanceof Error ? error.message : String(error)}
        </pre>
      </div>
    )
  }
}