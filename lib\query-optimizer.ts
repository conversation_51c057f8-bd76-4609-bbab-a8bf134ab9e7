/**
 * Database Query Optimization
 * Optimized queries with caching, batching, and performance monitoring
 */

import { createAdminClient } from '@/lib/supabase'
import { trainingCache, userCache, createCacheKey } from '@/lib/cache-manager'
import type { Database } from '@/types/database.types'

// =============================================================================
// QUERY PERFORMANCE MONITORING
// =============================================================================

interface QueryMetrics {
  query: string
  duration: number
  timestamp: number
  cacheHit: boolean
  resultCount?: number
}

class QueryPerformanceMonitor {
  private metrics: QueryMetrics[] = []
  private maxMetrics = 1000

  logQuery(query: string, duration: number, cacheHit: boolean, resultCount?: number) {
    this.metrics.push({
      query,
      duration,
      timestamp: Date.now(),
      cacheHit,
      resultCount
    })

    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }
  }

  getStats() {
    const totalQueries = this.metrics.length
    const cacheHits = this.metrics.filter(m => m.cacheHit).length
    const avgDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0) / totalQueries
    const slowQueries = this.metrics.filter(m => m.duration > 1000) // > 1 second

    return {
      totalQueries,
      cacheHitRate: totalQueries > 0 ? (cacheHits / totalQueries) * 100 : 0,
      averageDuration: avgDuration,
      slowQueriesCount: slowQueries.length,
      slowQueries: slowQueries.slice(-10) // Last 10 slow queries
    }
  }
}

export const queryMonitor = new QueryPerformanceMonitor()

// =============================================================================
// OPTIMIZED QUERY FUNCTIONS
// =============================================================================

/**
 * Get training modules with optimized caching and minimal data transfer
 */
export async function getOptimizedTrainingModules(
  userId?: string,
  includeProgress = false
) {
  const startTime = Date.now()
  const cacheKey = createCacheKey('training-modules', userId, includeProgress)
  
  try {
    const cachedResult = await trainingCache.get(cacheKey)
    if (cachedResult) {
      queryMonitor.logQuery('getOptimizedTrainingModules', Date.now() - startTime, true, cachedResult.length)
      return cachedResult
    }

    const supabase = createAdminClient()
    
    // Base query with only essential fields
    let query = supabase
      .from('training_modules')
      .select(`
        id,
        title,
        description,
        cover_image_url,
        duration_minutes,
        required_order,
        status,
        created_at
      `)
      .eq('status', 'published')
      .order('required_order', { ascending: true })

    const { data: modules, error } = await query

    if (error) throw error

    let result = modules || []

    // Add progress data if requested and user provided
    if (includeProgress && userId) {
      const progressData = await getOptimizedProgressData(userId, result.map(m => m.id))
      
      result = result.map(module => ({
        ...module,
        progressPercentage: progressData[module.id]?.percentage || 0,
        isCompleted: progressData[module.id]?.isCompleted || false,
        currentLessonId: progressData[module.id]?.currentLessonId
      }))
    }

    // Cache the result
    trainingCache.set(cacheKey, result, 5 * 60 * 1000) // 5 minutes

    queryMonitor.logQuery('getOptimizedTrainingModules', Date.now() - startTime, false, result.length)
    return result

  } catch (error) {
    queryMonitor.logQuery('getOptimizedTrainingModules', Date.now() - startTime, false, 0)
    console.error('Error fetching training modules:', error)
    throw error
  }
}

/**
 * Get progress data for multiple modules in a single optimized query
 */
export async function getOptimizedProgressData(userId: string, moduleIds: string[]) {
  const startTime = Date.now()
  const cacheKey = createCacheKey('progress-data', userId, moduleIds.join(','))
  
  try {
    const cachedResult = await userCache.get(cacheKey)
    if (cachedResult) {
      queryMonitor.logQuery('getOptimizedProgressData', Date.now() - startTime, true)
      return cachedResult
    }

    const supabase = createAdminClient()

    // Get all lessons for the modules
    const { data: lessons, error: lessonsError } = await supabase
      .from('lessons')
      .select('id, module_id')
      .in('module_id', moduleIds)

    if (lessonsError) throw lessonsError

    const lessonIds = lessons?.map(l => l.id) || []
    
    if (lessonIds.length === 0) {
      const emptyResult = {}
      userCache.set(cacheKey, emptyResult, 2 * 60 * 1000) // 2 minutes
      return emptyResult
    }

    // Get progress for all lessons in one query
    const { data: progressRecords, error: progressError } = await supabase
      .from('progress')
      .select('lesson_id, status, completed_at')
      .eq('prospect_id', userId)
      .in('lesson_id', lessonIds)

    if (progressError) throw progressError

    // Process progress data by module
    const progressByModule: Record<string, any> = {}
    
    for (const moduleId of moduleIds) {
      const moduleLessons = lessons?.filter(l => l.module_id === moduleId) || []
      const moduleProgress = progressRecords?.filter(p => 
        moduleLessons.some(l => l.id === p.lesson_id)
      ) || []

      const totalLessons = moduleLessons.length
      const completedLessons = moduleProgress.filter(p => p.status === 'completed').length
      const percentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0

      progressByModule[moduleId] = {
        percentage,
        isCompleted: percentage === 100,
        completedLessons,
        totalLessons,
        currentLessonId: moduleLessons[completedLessons]?.id || moduleLessons[0]?.id
      }
    }

    // Cache the result
    userCache.set(cacheKey, progressByModule, 2 * 60 * 1000) // 2 minutes

    queryMonitor.logQuery('getOptimizedProgressData', Date.now() - startTime, false)
    return progressByModule

  } catch (error) {
    queryMonitor.logQuery('getOptimizedProgressData', Date.now() - startTime, false)
    console.error('Error fetching progress data:', error)
    throw error
  }
}

/**
 * Batch fetch user data with optimized queries
 */
export async function getOptimizedUserData(userId: string) {
  const startTime = Date.now()
  const cacheKey = createCacheKey('user-data', userId)
  
  try {
    const cachedResult = await userCache.get(cacheKey)
    if (cachedResult) {
      queryMonitor.logQuery('getOptimizedUserData', Date.now() - startTime, true)
      return cachedResult
    }

    const supabase = createAdminClient()

    // Batch multiple queries using Promise.all for parallel execution
    const [
      { data: user, error: userError },
      { data: prospect, error: prospectError },
      { data: bpoMemberships, error: bpoError }
    ] = await Promise.all([
      supabase
        .from('users')
        .select('id, email, full_name, role, status, avatar_url, last_login')
        .eq('id', userId)
        .single(),
      
      supabase
        .from('prospects')
        .select('id, training_status, profile_visibility, intro_video_url, resume_url')
        .eq('user_id', userId)
        .single(),
      
      supabase
        .from('bpo_team_members')
        .select(`
          id,
          role,
          permissions,
          bpo:bpos(id, name, logo_url)
        `)
        .eq('user_id', userId)
        .eq('is_placeholder', false)
    ])

    if (userError) throw userError

    const result = {
      user,
      prospect,
      bpoMemberships: bpoMemberships || [],
      isProspect: !!prospect,
      isBPOAdmin: bpoMemberships?.some(m => m.role === 'admin') || false,
      isPlatformAdmin: user?.role === 'admin'
    }

    // Cache the result
    userCache.set(cacheKey, result, 10 * 60 * 1000) // 10 minutes

    queryMonitor.logQuery('getOptimizedUserData', Date.now() - startTime, false)
    return result

  } catch (error) {
    queryMonitor.logQuery('getOptimizedUserData', Date.now() - startTime, false)
    console.error('Error fetching user data:', error)
    throw error
  }
}

/**
 * Get dashboard stats with optimized aggregation queries
 */
export async function getOptimizedDashboardStats(bpoId?: string) {
  const startTime = Date.now()
  const cacheKey = createCacheKey('dashboard-stats', bpoId)
  
  try {
    const cachedResult = await userCache.get(cacheKey)
    if (cachedResult) {
      queryMonitor.logQuery('getOptimizedDashboardStats', Date.now() - startTime, true)
      return cachedResult
    }

    const supabase = createAdminClient()

    // Use count queries for better performance
    const queries = []

    if (bpoId) {
      // BPO-specific stats
      queries.push(
        supabase
          .from('job_postings')
          .select('*', { count: 'exact', head: true })
          .eq('bpo_id', bpoId)
          .eq('status', 'published'),
        
        supabase
          .from('applications')
          .select('*', { count: 'exact', head: true })
          .eq('job_postings.bpo_id', bpoId),
        
        supabase
          .from('bpo_team_members')
          .select('*', { count: 'exact', head: true })
          .eq('bpo_id', bpoId)
          .eq('is_placeholder', false)
      )
    } else {
      // Platform-wide stats
      queries.push(
        supabase
          .from('users')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'active'),
        
        supabase
          .from('training_modules')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'published'),
        
        supabase
          .from('bpos')
          .select('*', { count: 'exact', head: true })
      )
    }

    const results = await Promise.all(queries)
    
    const stats = bpoId ? {
      activeJobs: results[0].count || 0,
      totalApplications: results[1].count || 0,
      teamMembers: results[2].count || 0
    } : {
      totalUsers: results[0].count || 0,
      totalModules: results[1].count || 0,
      totalBPOs: results[2].count || 0
    }

    // Cache the result
    userCache.set(cacheKey, stats, 5 * 60 * 1000) // 5 minutes

    queryMonitor.logQuery('getOptimizedDashboardStats', Date.now() - startTime, false)
    return stats

  } catch (error) {
    queryMonitor.logQuery('getOptimizedDashboardStats', Date.now() - startTime, false)
    console.error('Error fetching dashboard stats:', error)
    throw error
  }
}

/**
 * Preload critical data for faster page loads
 */
export async function preloadCriticalData(userId: string) {
  try {
    // Preload in parallel
    await Promise.all([
      getOptimizedUserData(userId),
      getOptimizedTrainingModules(userId, true),
      getOptimizedDashboardStats()
    ])
  } catch (error) {
    console.error('Error preloading critical data:', error)
  }
}

/**
 * Invalidate related caches when data changes
 */
export function invalidateRelatedCaches(type: 'user' | 'training' | 'progress', id?: string) {
  switch (type) {
    case 'user':
      userCache.invalidatePattern(id ? `user-data:${id}` : 'user-data:.*')
      break
    case 'training':
      trainingCache.invalidatePattern('training-modules:.*')
      break
    case 'progress':
      userCache.invalidatePattern(id ? `progress-data:${id}` : 'progress-data:.*')
      trainingCache.invalidatePattern('training-modules:.*') // Progress affects module data
      break
  }
}

// =============================================================================
// QUERY OPTIMIZATION UTILITIES
// =============================================================================

/**
 * Batch multiple database operations
 */
export async function batchOperations<T>(
  operations: Array<() => Promise<T>>,
  batchSize = 5
): Promise<T[]> {
  const results: T[] = []
  
  for (let i = 0; i < operations.length; i += batchSize) {
    const batch = operations.slice(i, i + batchSize)
    const batchResults = await Promise.all(batch.map(op => op()))
    results.push(...batchResults)
  }
  
  return results
}

/**
 * Debounced query execution to prevent excessive API calls
 */
export function createDebouncedQuery<T extends (...args: any[]) => Promise<any>>(
  queryFn: T,
  delay = 300
): T {
  let timeoutId: NodeJS.Timeout
  let lastPromise: Promise<any>
  
  return ((...args: Parameters<T>) => {
    return new Promise((resolve, reject) => {
      clearTimeout(timeoutId)
      
      timeoutId = setTimeout(async () => {
        try {
          lastPromise = queryFn(...args)
          const result = await lastPromise
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }, delay)
    })
  }) as T
}
