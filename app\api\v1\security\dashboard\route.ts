/**
 * Security Dashboard API
 * Provides security metrics and monitoring data for administrators
 */

import { NextRequest, NextResponse } from 'next/server';
import { withApiErrorHandler } from '@/lib/api-error-handler';
import { getAuthenticatedUser, requirePlatformAdmin } from '@/lib/auth';
import { withApiSecurity, RATE_LIMITS } from '@/lib/api-security';
import { withApiVersioning, createVersionedResponse } from '@/lib/api-versioning';
import { securityAudit, SecurityEventType, SecuritySeverity } from '@/lib/security-audit';

/**
 * GET /api/v1/security/dashboard
 * Get security dashboard metrics (Admin only)
 */
export const GET = withApiSecurity(
  withApiVersioning(
    withApiErrorHandler(async (request: NextRequest, version: string) => {
      // Require platform admin access
      const authResult = await requirePlatformAdmin();
      if (!authResult.user) {
        securityAudit.logEvent(
          SecurityEventType.UNAUTHORIZED_ACCESS,
          'Attempted access to security dashboard without admin privileges',
          { endpoint: '/api/v1/security/dashboard' },
          { request }
        );
        throw new Error('Platform admin access required');
      }

      const user = authResult.user;
      
      // Log admin access to security dashboard
      securityAudit.logEvent(
        SecurityEventType.ADMIN_ACCESS,
        `Admin ${user.email} accessed security dashboard`,
        { action: 'view_security_dashboard' },
        { request, userId: user.id, userEmail: user.email, userRole: user.role }
      );

      // Get query parameters for time range
      const url = new URL(request.url);
      const timeRangeParam = url.searchParams.get('timeRange') || '24h';
      
      // Convert time range to milliseconds
      const timeRangeMs = parseTimeRange(timeRangeParam);
      
      // Get security metrics
      const metrics = securityAudit.getSecurityMetrics(timeRangeMs);
      const recentEvents = securityAudit.getRecentEvents(50);
      const criticalEvents = securityAudit.getRecentEvents(20, SecuritySeverity.CRITICAL);
      const highSeverityEvents = securityAudit.getRecentEvents(20, SecuritySeverity.HIGH);

      // Calculate additional metrics
      const authFailures = recentEvents.filter(e => 
        e.type === SecurityEventType.LOGIN_FAILURE ||
        e.type === SecurityEventType.UNAUTHORIZED_ACCESS
      ).length;

      const rateLimitViolations = recentEvents.filter(e => 
        e.type === SecurityEventType.RATE_LIMIT_EXCEEDED
      ).length;

      const fileUploadBlocks = recentEvents.filter(e => 
        e.type === SecurityEventType.FILE_UPLOAD_BLOCKED ||
        e.type === SecurityEventType.MALICIOUS_FILE_DETECTED
      ).length;

      const suspiciousActivity = recentEvents.filter(e => 
        e.type === SecurityEventType.SUSPICIOUS_ACTIVITY ||
        e.type === SecurityEventType.XSS_ATTEMPT ||
        e.type === SecurityEventType.SQL_INJECTION_ATTEMPT
      ).length;

      // Get threat level based on recent activity
      const threatLevel = calculateThreatLevel(metrics, criticalEvents.length, highSeverityEvents.length);

      // Return comprehensive security dashboard data
      return NextResponse.json(
        createVersionedResponse(
          {
            overview: {
              threatLevel,
              totalEvents: metrics.totalEvents,
              averageRiskScore: Math.round(metrics.riskScore * 100) / 100,
              timeRange: timeRangeParam,
              lastUpdated: new Date().toISOString()
            },
            
            eventsSummary: {
              total: metrics.totalEvents,
              bySeverity: metrics.eventsBySeverity,
              byType: metrics.eventsByType
            },
            
            securityMetrics: {
              authenticationFailures: authFailures,
              rateLimitViolations,
              fileUploadBlocks,
              suspiciousActivity,
              privilegeEscalationAttempts: recentEvents.filter(e => 
                e.type === SecurityEventType.PRIVILEGE_ESCALATION
              ).length
            },
            
            topRisks: metrics.topRisks.map(risk => ({
              type: risk.type,
              count: risk.count,
              averageRiskScore: Math.round(risk.avgRisk * 100) / 100,
              severity: getSeverityFromRiskScore(risk.avgRisk)
            })),
            
            recentCriticalEvents: criticalEvents.map(event => ({
              id: event.id,
              type: event.type,
              severity: event.severity,
              timestamp: event.timestamp,
              message: event.message,
              userEmail: event.userEmail,
              ipAddress: event.ipAddress,
              endpoint: event.endpoint,
              riskScore: event.riskScore
            })),
            
            recentHighSeverityEvents: highSeverityEvents.map(event => ({
              id: event.id,
              type: event.type,
              severity: event.severity,
              timestamp: event.timestamp,
              message: event.message,
              userEmail: event.userEmail,
              ipAddress: event.ipAddress,
              endpoint: event.endpoint,
              riskScore: event.riskScore
            })),
            
            recommendations: generateSecurityRecommendations(metrics, recentEvents),
            
            systemHealth: {
              apiSecurity: 'operational',
              rateLimiting: 'operational',
              fileUploadSecurity: 'operational',
              authenticationSystem: 'operational',
              auditLogging: 'operational'
            }
          },
          version
        )
      );
    })
  ),
  {
    rateLimit: RATE_LIMITS.ADMIN,
    maxRequestSize: 1024
  }
);

/**
 * GET /api/v1/security/events
 * Get detailed security events (Admin only)
 */
export const POST = withApiSecurity(
  withApiVersioning(
    withApiErrorHandler(async (request: NextRequest, version: string) => {
      // Require platform admin access
      const authResult = await requirePlatformAdmin();
      if (!authResult.user) {
        throw new Error('Platform admin access required');
      }

      const user = authResult.user;
      const body = await request.json();
      
      const {
        limit = 100,
        severity,
        eventType,
        startDate,
        endDate,
        userId,
        ipAddress
      } = body;

      // Log admin access to detailed events
      securityAudit.logEvent(
        SecurityEventType.ADMIN_ACCESS,
        `Admin ${user.email} queried security events`,
        { 
          action: 'query_security_events',
          filters: { severity, eventType, startDate, endDate, userId, ipAddress }
        },
        { request, userId: user.id, userEmail: user.email, userRole: user.role }
      );

      // Get filtered events
      let events = securityAudit.getRecentEvents(limit * 2); // Get more to filter

      // Apply filters
      if (severity) {
        events = events.filter(e => e.severity === severity);
      }
      
      if (eventType) {
        events = events.filter(e => e.type === eventType);
      }
      
      if (startDate) {
        const start = new Date(startDate);
        events = events.filter(e => new Date(e.timestamp) >= start);
      }
      
      if (endDate) {
        const end = new Date(endDate);
        events = events.filter(e => new Date(e.timestamp) <= end);
      }
      
      if (userId) {
        events = events.filter(e => e.userId === userId);
      }
      
      if (ipAddress) {
        events = events.filter(e => e.ipAddress === ipAddress);
      }

      // Limit results
      events = events.slice(0, limit);

      return NextResponse.json(
        createVersionedResponse(
          {
            events: events.map(event => ({
              ...event,
              // Remove sensitive details for API response
              details: event.severity === SecuritySeverity.LOW ? undefined : event.details
            })),
            totalFound: events.length,
            filters: { severity, eventType, startDate, endDate, userId, ipAddress }
          },
          version
        )
      );
    })
  ),
  {
    rateLimit: RATE_LIMITS.ADMIN,
    maxRequestSize: 2 * 1024 // 2KB for query parameters
  }
);

// Helper functions
function parseTimeRange(timeRange: string): number {
  const ranges: Record<string, number> = {
    '1h': 60 * 60 * 1000,
    '6h': 6 * 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000,
    '7d': 7 * 24 * 60 * 60 * 1000,
    '30d': 30 * 24 * 60 * 60 * 1000
  };
  
  return ranges[timeRange] || ranges['24h'];
}

function calculateThreatLevel(
  metrics: any,
  criticalEvents: number,
  highSeverityEvents: number
): 'low' | 'medium' | 'high' | 'critical' {
  if (criticalEvents > 5 || metrics.riskScore > 8) {
    return 'critical';
  }
  
  if (criticalEvents > 2 || highSeverityEvents > 10 || metrics.riskScore > 6) {
    return 'high';
  }
  
  if (highSeverityEvents > 5 || metrics.riskScore > 3) {
    return 'medium';
  }
  
  return 'low';
}

function getSeverityFromRiskScore(riskScore: number): SecuritySeverity {
  if (riskScore >= 8) return SecuritySeverity.CRITICAL;
  if (riskScore >= 6) return SecuritySeverity.HIGH;
  if (riskScore >= 3) return SecuritySeverity.MEDIUM;
  return SecuritySeverity.LOW;
}

function generateSecurityRecommendations(metrics: any, recentEvents: any[]): string[] {
  const recommendations: string[] = [];
  
  if (metrics.riskScore > 6) {
    recommendations.push('High risk score detected. Review recent security events and consider implementing additional security measures.');
  }
  
  const authFailures = recentEvents.filter(e => e.type === SecurityEventType.LOGIN_FAILURE).length;
  if (authFailures > 10) {
    recommendations.push('High number of authentication failures detected. Consider implementing account lockout policies.');
  }
  
  const rateLimitViolations = recentEvents.filter(e => e.type === SecurityEventType.RATE_LIMIT_EXCEEDED).length;
  if (rateLimitViolations > 20) {
    recommendations.push('Frequent rate limit violations detected. Consider adjusting rate limits or investigating potential abuse.');
  }
  
  const fileUploadBlocks = recentEvents.filter(e => 
    e.type === SecurityEventType.FILE_UPLOAD_BLOCKED ||
    e.type === SecurityEventType.MALICIOUS_FILE_DETECTED
  ).length;
  if (fileUploadBlocks > 5) {
    recommendations.push('Multiple file upload security violations detected. Review file upload policies and user education.');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Security posture appears healthy. Continue monitoring for any changes.');
  }
  
  return recommendations;
}
