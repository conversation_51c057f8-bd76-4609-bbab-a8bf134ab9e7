-- Create BPO schedules table for managing team member availability
-- Copy and paste this entire script into your Supabase SQL Editor

-- Step 1: Drop existing table if it exists (to fix structure)
DROP TABLE IF EXISTS bpo_schedules CASCADE;

-- Step 2: Create the corrected table structure
CREATE TABLE bpo_schedules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    schedule_data JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,

    -- Ensure one schedule per user
    CONSTRAINT unique_user_schedule UNIQUE (user_id)
);

-- The schedule_data JSONB will store the complete weekly schedule like:
-- {
--   "0": [{"start": "09:00", "end": "12:00"}, {"start": "14:00", "end": "17:00"}], // Sunday
--   "1": [{"start": "08:00", "end": "16:00"}], // Monday
--   "2": [], // Tuesday (no availability)
--   ...
-- }

-- Step 3: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bpo_schedules_user_id ON bpo_schedules(user_id);
CREATE INDEX IF NOT EXISTS idx_bpo_schedules_schedule_data ON bpo_schedules USING GIN (schedule_data);

-- Step 3: Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create trigger for updated_at (drop first if exists)
DROP TRIGGER IF EXISTS update_bpo_schedules_updated_at ON bpo_schedules;
CREATE TRIGGER update_bpo_schedules_updated_at
    BEFORE UPDATE ON bpo_schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Step 5: Enable Row Level Security
ALTER TABLE bpo_schedules ENABLE ROW LEVEL SECURITY;

-- Step 6: Create RLS policies
CREATE POLICY "Users can view their own schedules" ON bpo_schedules
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own schedules" ON bpo_schedules
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own schedules" ON bpo_schedules
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own schedules" ON bpo_schedules
    FOR DELETE USING (auth.uid() = user_id);

-- Step 7: Grant permissions
GRANT ALL ON bpo_schedules TO authenticated;

-- Step 8: Add helpful comments
COMMENT ON TABLE bpo_schedules IS 'Stores complete weekly availability schedules for BPO team members';
COMMENT ON COLUMN bpo_schedules.user_id IS 'Reference to the BPO team member';
COMMENT ON COLUMN bpo_schedules.schedule_data IS 'JSONB containing weekly schedule: {"0": [{"start": "09:00", "end": "17:00"}], "1": [...], ...} where keys are day numbers (0=Sunday, 6=Saturday)';

-- Success message
SELECT 'bpo_schedules table created successfully!' as result;
