'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Building2, Mail, Phone, Globe, MapPin, Upload } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { checkBPOMembership } from '@/lib/bpo-auth-check';

export default function BPOProfilePage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [bpoData, setBpoData] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    industry: '',
    size_range: '',
    founded_year: '',
    website_url: '',
    contact_email: '',
    phone: '',
    address: '',
    city: '',
    country: '',
  });

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        
        // Use the standardized membership check
        const membershipResult = await checkBPOMembership(supabase);
        
        if (!membershipResult.authorized) {
          console.log('User not authorized:', membershipResult.error?.message || 'Not a BPO team member');
          toast({
            title: "Access error",
            description: "You don't have permission to access this BPO profile.",
            variant: "destructive",
          });
          setLoading(false);
          return;
        }
        
        // Store user info
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
          setUser(session.user);
        }
        
        // We now have the bpoId from the membership check
        const bpoId = membershipResult.bpoId;
        
        // Get BPO data using the verified bpoId
        const { data: bpo, error: bpoError } = await supabase
          .from('bpos')
          .select('*')
          .eq('id', bpoId)
          .single();
          
        if (bpoError || !bpo) {
          console.error('Error fetching BPO data:', bpoError);
          toast({
            title: "Profile error",
            description: "Could not load your company profile data.",
            variant: "destructive",
          });
          setLoading(false);
          return;
        }
        
        // Store BPO data
        setBpoData(bpo);
        
        // Set form data from BPO data with safe fallbacks
        setFormData({
          name: bpo.name || '',
          description: bpo.description || '',
          industry: bpo.industry || '',
          size_range: bpo.size_range || '',
          founded_year: bpo.founded_year?.toString() || '',
          website_url: bpo.website_url || '',
          contact_email: bpo.contact_email || '',
          phone: bpo.phone || '',
          address: bpo.location?.address || '',
          city: bpo.location?.city || '',
          country: bpo.location?.country || '',
        });
        
      } catch (error) {
        console.error('Error fetching profile data:', error);
        let errorMessage = "There was an error loading your profile";
        
        if (error instanceof Error) {
          errorMessage = `Error: ${error.message}`;
        }
        
        toast({
          title: "Failed to load profile",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [toast]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!bpoData) return;
    
    try {
      setLoading(true);
      
      // Prepare location object safely
      const location = {
        // Use existing location data as fallback if it exists
        ...(bpoData.location || {}),
        address: formData.address || null,
        city: formData.city || null,
        country: formData.country || null
      };
      
      const updateData = {
        name: formData.name || null,
        description: formData.description || null,
        industry: formData.industry || null,
        size_range: formData.size_range || null,
        founded_year: formData.founded_year ? parseInt(formData.founded_year) : null,
        website_url: formData.website_url || null,
        contact_email: formData.contact_email || null,
        phone: formData.phone || null,
        location: location,
        updated_at: new Date().toISOString()
      };
      
      // Check for a valid ID before updating
      if (!bpoData.id) {
        throw new Error('Missing BPO ID');
      }
      
      console.log(`Updating BPO profile for ID: ${bpoData.id}`);
      
      // Update the BPO record with error handling
      try {
        const { error, data: updatedData } = await supabase
          .from('bpos')
          .update(updateData)
          .eq('id', bpoData.id)
          .select();
          
        if (error) {
          console.error('Supabase update error details:', JSON.stringify(error));
          
          if (error.code === 'PGRST301' || error.message?.includes('permission')) {
            throw new Error('Permission denied: You may not have rights to update this profile');
          }
          
          if (error.code === '23505') {
            throw new Error('A company with this name already exists');
          }
          
          throw new Error(`Failed to update profile: ${error.message}`);
        }
        
        if (!updatedData || updatedData.length === 0) {
          console.warn('Profile update: No rows updated in database');
        } else {
          console.log('Profile updated successfully:', updatedData[0].id);
        }
        
        // Update local state with the new data (use returned data if available)
        setBpoData({
          ...bpoData,
          ...(updatedData && updatedData.length > 0 ? updatedData[0] : updateData)
        });
        
        toast({
          title: "Profile updated",
          description: "Your company profile has been updated successfully",
        });
      } catch (dbError) {
        console.error('Database update error:', dbError);
        throw dbError; // Re-throw to be caught by the outer try-catch
      }
      
    } catch (error) {
      console.error('Error updating profile:', error);
      
      // More user-friendly error message
      let errorMessage = "There was an error updating your profile";
      if (error instanceof Error) {
        errorMessage = `Update failed: ${error.message}`;
      }
      
      toast({
        title: "Update failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files.length) return;
    
    try {
      setUploading(true);
      
      // Check if bpoData and ID exist
      if (!bpoData || !bpoData.id) {
        throw new Error('Company data not loaded');
      }
      
      const file = e.target.files[0];
      
      // Validate file input
      if (!file) {
        throw new Error('No file selected');
      }
      
      // Validate file size and type
      if (file.size > 5 * 1024 * 1024) {
        throw new Error('File size exceeds 5MB limit');
      }
      
      const fileExt = file.name.split('.').pop()?.toLowerCase() || '';
      if (!['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
        throw new Error('Only image files (jpg, jpeg, png, gif) are allowed');
      }
      
      // Create form data for API request
      const formData = new FormData();
      formData.append('file', file);
      formData.append('bpoId', bpoData.id);
      
      console.log('Sending logo upload request to API');
      
      // Send the file to our API endpoint
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }
      
      const result = await response.json();
      
      if (!result.success || !result.url) {
        throw new Error('Upload completed but returned an invalid response');
      }
      
      console.log('Logo uploaded successfully');
      
      // Update local state with the new logo URL
      setBpoData({
        ...bpoData,
        logo_url: result.url
      });
      
      toast({
        title: "Logo updated",
        description: "Your company logo has been updated successfully",
      });
      
    } catch (error) {
      console.error('Error uploading logo:', error);
      
      let errorMessage = "There was an error uploading your logo";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      toast({
        title: "Upload failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      // Reset the file input
      if (e.target) {
        e.target.value = '';
      }
    }
  };

  if (loading && !bpoData) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-8">
      <Tabs defaultValue="details" className="w-full">
        <TabsList className="grid w-full md:w-[400px] grid-cols-2">
          <TabsTrigger value="details">Company Details</TabsTrigger>
          <TabsTrigger value="branding">Logo & Branding</TabsTrigger>
        </TabsList>
        
        <TabsContent value="details" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Company Profile</CardTitle>
              <CardDescription>
                Update your company information visible to prospects
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="name">Company Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Enter company name"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="industry">Industry</Label>
                    <Input
                      id="industry"
                      name="industry"
                      value={formData.industry}
                      onChange={handleChange}
                      placeholder="e.g. Customer Service, IT Support"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="size_range">Company Size</Label>
                    <Input
                      id="size_range"
                      name="size_range"
                      value={formData.size_range}
                      onChange={handleChange}
                      placeholder="e.g. 50-100 employees"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="founded_year">Founded Year</Label>
                    <Input
                      id="founded_year"
                      name="founded_year"
                      type="number"
                      value={formData.founded_year}
                      onChange={handleChange}
                      placeholder="e.g. 2010"
                    />
                  </div>
                  
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="description">Company Description</Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleChange}
                      placeholder="Describe your company, culture, and values"
                      rows={4}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="website_url">Website</Label>
                    <Input
                      id="website_url"
                      name="website_url"
                      value={formData.website_url}
                      onChange={handleChange}
                      placeholder="https://yourcompany.com"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="contact_email">Contact Email</Label>
                    <Input
                      id="contact_email"
                      name="contact_email"
                      value={formData.contact_email}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="+12345678900"
                    />
                  </div>
                </div>
                
                <div className="pt-2">
                  <h3 className="text-sm font-medium mb-3">Location</h3>
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <Label htmlFor="address">Address</Label>
                      <Input
                        id="address"
                        name="address"
                        value={formData.address}
                        onChange={handleChange}
                        placeholder="Street address"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        name="city"
                        value={formData.city}
                        onChange={handleChange}
                        placeholder="City"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <Input
                        id="country"
                        name="country"
                        value={formData.country}
                        onChange={handleChange}
                        placeholder="Country"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="pt-4">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="branding" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Company Logo</CardTitle>
              <CardDescription>
                Upload or update your company logo
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col items-center space-y-4">
                <Avatar className="h-32 w-32">
                  <AvatarImage src={bpoData?.logo_url} alt={bpoData?.name} />
                  <AvatarFallback className="text-2xl">
                    <Building2 className="h-12 w-12" />
                  </AvatarFallback>
                </Avatar>
                
                <Label
                  htmlFor="logo-upload"
                  className="cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
                >
                  <Upload className="mr-2 h-4 w-4" />
                  <span>{uploading ? 'Uploading...' : 'Upload Logo'}</span>
                  <Input
                    id="logo-upload"
                    type="file"
                    accept="image/*"
                    disabled={uploading}
                    className="hidden"
                    onChange={handleLogoUpload}
                  />
                </Label>
                
                <p className="text-sm text-gray-500 dark:text-gray-400 text-center max-w-md">
                  Upload a square image for best results. Your logo will be displayed in job listings, profiles, and across the platform.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 