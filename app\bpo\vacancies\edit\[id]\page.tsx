'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { use } from 'react';

export default function EditVacancyPage({ params }: { params: Promise<{ id: string }> }) {
  // Use React.use to unwrap the params promise
  const resolvedParams = use(params);
  const vacancyId = resolvedParams.id;
  
  const router = useRouter();

  useEffect(() => {
    // Redirect to the main vacancies page with a query parameter to open the edit dialog
    router.push(`/bpo/vacancies?edit=${vacancyId}`);
  }, [router, vacancyId]);

  return (
    <div className="flex h-[60vh] items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-500 dark:text-gray-400">Redirecting to vacancy editor...</p>
      </div>
    </div>
  );
} 