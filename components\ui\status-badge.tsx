/**
 * Status Badge Components
 * Standardized status indicators with consistent styling and semantics
 */

"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { 
  CheckCircle, 
  Clock, 
  XCircle, 
  AlertCircle, 
  Pause, 
  Play, 
  Lock, 
  Unlock,
  Eye,
  EyeOff,
  Star,
  Zap,
  Shield,
  AlertTriangle,
  Info,
  Target,
  TrendingUp,
  TrendingDown
} from "lucide-react"

// Status badge variants
const statusBadgeVariants = cva(
  "inline-flex items-center gap-1.5 px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors",
  {
    variants: {
      variant: {
        // Success states
        success: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",
        completed: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",
        active: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",
        published: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",
        
        // Progress states
        progress: "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800",
        "in-progress": "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800",
        pending: "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800",
        reviewing: "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800",
        
        // Warning states
        warning: "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",
        paused: "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",
        draft: "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",
        
        // Error states
        error: "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800",
        failed: "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800",
        rejected: "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800",
        cancelled: "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800",
        
        // Neutral states
        neutral: "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800",
        inactive: "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800",
        "not-started": "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800",
        locked: "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800",
        
        // Special states
        premium: "bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800",
        featured: "bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800",
        new: "bg-cyan-100 text-cyan-800 border-cyan-200 dark:bg-cyan-900/20 dark:text-cyan-400 dark:border-cyan-800",
        trending: "bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: "neutral",
      size: "md",
    },
  }
)

// Status icon mapping
const statusIcons = {
  // Success states
  success: CheckCircle,
  completed: CheckCircle,
  active: CheckCircle,
  published: Eye,
  
  // Progress states
  progress: Clock,
  "in-progress": Play,
  pending: Clock,
  reviewing: Eye,
  
  // Warning states
  warning: AlertTriangle,
  paused: Pause,
  draft: EyeOff,
  
  // Error states
  error: XCircle,
  failed: XCircle,
  rejected: XCircle,
  cancelled: XCircle,
  
  // Neutral states
  neutral: Info,
  inactive: Pause,
  "not-started": Target,
  locked: Lock,
  
  // Special states
  premium: Star,
  featured: Zap,
  new: TrendingUp,
  trending: TrendingUp,
} as const

// TypeScript interfaces
export interface StatusBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statusBadgeVariants> {
  status: keyof typeof statusIcons
  showIcon?: boolean
  iconOnly?: boolean
  children?: React.ReactNode
  pulse?: boolean
}

export interface ProgressStatusBadgeProps {
  percentage: number
  showPercentage?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export interface UserStatusBadgeProps {
  status: 'online' | 'offline' | 'away' | 'busy'
  showText?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export interface PriorityBadgeProps {
  priority: 'low' | 'medium' | 'high' | 'urgent'
  showText?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

/**
 * Main Status Badge Component
 */
export const StatusBadge = React.forwardRef<
  HTMLDivElement,
  StatusBadgeProps
>(({
  status,
  variant,
  size = "md",
  showIcon = true,
  iconOnly = false,
  children,
  pulse = false,
  className,
  ...props
}, ref) => {
  const Icon = statusIcons[status]
  const badgeVariant = variant || status
  
  return (
    <div
      ref={ref}
      className={cn(
        statusBadgeVariants({ variant: badgeVariant, size }),
        pulse && "animate-pulse",
        className
      )}
      {...props}
    >
      {showIcon && Icon && (
        <Icon className={cn(
          "flex-shrink-0",
          size === 'sm' && "h-3 w-3",
          size === 'md' && "h-3.5 w-3.5",
          size === 'lg' && "h-4 w-4"
        )} />
      )}
      {!iconOnly && (
        <span className="capitalize">
          {children || status.replace('-', ' ')}
        </span>
      )}
    </div>
  )
})

StatusBadge.displayName = "StatusBadge"

/**
 * Progress Status Badge Component
 */
export const ProgressStatusBadge = React.forwardRef<
  HTMLDivElement,
  ProgressStatusBadgeProps
>(({
  percentage,
  showPercentage = true,
  size = "md",
  className,
  ...props
}, ref) => {
  const getProgressStatus = (pct: number): keyof typeof statusIcons => {
    if (pct === 100) return 'completed'
    if (pct > 0) return 'in-progress'
    return 'not-started'
  }
  
  const status = getProgressStatus(percentage)
  
  return (
    <StatusBadge
      ref={ref}
      status={status}
      size={size}
      className={className}
      {...props}
    >
      {showPercentage ? `${percentage}%` : undefined}
    </StatusBadge>
  )
})

ProgressStatusBadge.displayName = "ProgressStatusBadge"

/**
 * User Status Badge Component
 */
export const UserStatusBadge = React.forwardRef<
  HTMLDivElement,
  UserStatusBadgeProps
>(({
  status,
  showText = true,
  size = "md",
  className,
  ...props
}, ref) => {
  const statusConfig = {
    online: { variant: 'success' as const, text: 'Online' },
    offline: { variant: 'neutral' as const, text: 'Offline' },
    away: { variant: 'warning' as const, text: 'Away' },
    busy: { variant: 'error' as const, text: 'Busy' }
  }
  
  const config = statusConfig[status]
  
  return (
    <div
      ref={ref}
      className={cn(
        "inline-flex items-center gap-1.5",
        className
      )}
      {...props}
    >
      <div className={cn(
        "rounded-full",
        size === 'sm' && "h-2 w-2",
        size === 'md' && "h-2.5 w-2.5",
        size === 'lg' && "h-3 w-3",
        config.variant === 'success' && "bg-green-500",
        config.variant === 'warning' && "bg-yellow-500",
        config.variant === 'error' && "bg-red-500",
        config.variant === 'neutral' && "bg-gray-400",
        status === 'online' && "animate-pulse"
      )} />
      {showText && (
        <span className={cn(
          "text-muted-foreground",
          size === 'sm' && "text-xs",
          size === 'md' && "text-sm",
          size === 'lg' && "text-base"
        )}>
          {config.text}
        </span>
      )}
    </div>
  )
})

UserStatusBadge.displayName = "UserStatusBadge"

/**
 * Priority Badge Component
 */
export const PriorityBadge = React.forwardRef<
  HTMLDivElement,
  PriorityBadgeProps
>(({
  priority,
  showText = true,
  size = "md",
  className,
  ...props
}, ref) => {
  const priorityConfig = {
    low: { variant: 'neutral' as const, icon: TrendingDown },
    medium: { variant: 'warning' as const, icon: AlertCircle },
    high: { variant: 'error' as const, icon: TrendingUp },
    urgent: { variant: 'error' as const, icon: AlertTriangle }
  }
  
  const config = priorityConfig[priority]
  const Icon = config.icon
  
  return (
    <div
      ref={ref}
      className={cn(
        statusBadgeVariants({ variant: config.variant, size }),
        priority === 'urgent' && "animate-pulse",
        className
      )}
      {...props}
    >
      <Icon className={cn(
        "flex-shrink-0",
        size === 'sm' && "h-3 w-3",
        size === 'md' && "h-3.5 w-3.5",
        size === 'lg' && "h-4 w-4"
      )} />
      {showText && (
        <span className="capitalize">{priority}</span>
      )}
    </div>
  )
})

PriorityBadge.displayName = "PriorityBadge"

// Export utility functions
export const getStatusVariant = (status: string): keyof typeof statusIcons => {
  return (status as keyof typeof statusIcons) || 'neutral'
}

export const getProgressStatusText = (percentage: number): string => {
  if (percentage === 100) return 'Completed'
  if (percentage > 0) return 'In Progress'
  return 'Not Started'
}

// Export all status types for TypeScript
export type StatusType = keyof typeof statusIcons
export type StatusVariant = VariantProps<typeof statusBadgeVariants>['variant']
