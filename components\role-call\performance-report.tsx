'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Phone, 
  Star, 
  TrendingUp, 
  Clock, 
  Target,
  Award,
  BarChart3,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface PerformanceMetric {
  label: string
  value: number
  maxValue: number
  status: 'excellent' | 'good' | 'needs-work' | 'poor'
}

interface CallSession {
  id: string
  type: string
  date: string
  duration: string
  score: number
  status: 'completed' | 'in-progress' | 'failed'
}

interface PerformanceReportProps {
  variant?: 'full' | 'summary'
  showRecentSessions?: boolean
  className?: string
}

export function PerformanceReport({ 
  variant = 'full', 
  showRecentSessions = true,
  className 
}: PerformanceReportProps) {
  // Static data - synced with dashboard KPIs and assessment reports
  // These values should match the dashboard trainingStats:
  // - callPracticeScore (dashboard) = averageScore (here)
  // - callPracticeHours (dashboard) = totalHours (here)
  const overallStats = {
    totalCalls: 24,
    averageScore: 78, // Dashboard shows 78 (out of 100)
    totalHours: 4.2, // Dashboard shows 4.2 hours
    completionRate: 92,
    improvement: 15 // percentage improvement over last month
  }

  const performanceMetrics: PerformanceMetric[] = [
    { label: 'Professional Tone', value: 8.5, maxValue: 10, status: 'excellent' },
    { label: 'Clear Communication', value: 7.2, maxValue: 10, status: 'good' },
    { label: 'Script Adherence', value: 8.8, maxValue: 10, status: 'excellent' },
    { label: 'Active Listening', value: 6.5, maxValue: 10, status: 'needs-work' },
    { label: 'Pace & Timing', value: 6.8, maxValue: 10, status: 'needs-work' },
    { label: 'Objection Handling', value: 7.5, maxValue: 10, status: 'good' }
  ]

  const recentSessions: CallSession[] = [
    { id: '1', type: 'Cold Call', date: '2024-01-15', duration: '12:34', score: 8.2, status: 'completed' },
    { id: '2', type: 'Follow-up', date: '2024-01-14', duration: '8:45', score: 7.5, status: 'completed' },
    { id: '3', type: 'Customer Service', date: '2024-01-13', duration: '15:22', score: 8.8, status: 'completed' },
    { id: '4', type: 'Sales Closing', date: '2024-01-12', duration: '18:10', score: 7.1, status: 'completed' }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
      case 'good':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
      case 'needs-work':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
      case 'poor':
        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent':
      case 'good':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'needs-work':
      case 'poor':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      default:
        return null
    }
  }

  if (variant === 'summary') {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Phone className="h-5 w-5" />
            Role-Call Performance
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-primary">{overallStats.averageScore}</p>
              <p className="text-xs text-muted-foreground">Average Score</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-primary">{overallStats.totalCalls}</p>
              <p className="text-xs text-muted-foreground">Total Calls</p>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span>{overallStats.completionRate}%</span>
            </div>
            <Progress value={overallStats.completionRate} className="h-2" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Overall Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Phone className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-2xl font-bold">{overallStats.totalCalls}</p>
                <p className="text-xs text-muted-foreground">Total Calls</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                <Star className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <p className="text-2xl font-bold">{overallStats.averageScore}</p>
                <p className="text-xs text-muted-foreground">Average Score</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Clock className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-2xl font-bold">{overallStats.totalHours}h</p>
                <p className="text-xs text-muted-foreground">Training Time</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <TrendingUp className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-2xl font-bold">+{overallStats.improvement}%</p>
                <p className="text-xs text-muted-foreground">Improvement</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {performanceMetrics.map((metric, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getStatusIcon(metric.status)}
                  <span className="text-sm font-medium">{metric.label}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">
                    {metric.value}/{metric.maxValue}
                  </span>
                  <Badge className={getStatusColor(metric.status)}>
                    {metric.status === 'excellent' ? 'Excellent' :
                     metric.status === 'good' ? 'Good' :
                     metric.status === 'needs-work' ? 'Needs Work' : 'Poor'}
                  </Badge>
                </div>
              </div>
              <Progress value={(metric.value / metric.maxValue) * 100} className="h-2" />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Recent Sessions */}
      {showRecentSessions && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Recent Training Sessions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentSessions.map((session) => (
                <div key={session.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Phone className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium text-sm">{session.type}</p>
                      <p className="text-xs text-muted-foreground">{session.date}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">{session.score}/10</p>
                    <p className="text-xs text-muted-foreground">{session.duration}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
