import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || session.user.id;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const durationMinutes = parseInt(searchParams.get('durationMinutes') || '60');
    const dateRange = searchParams.get('dateRange') || 'week';
    const additionalInterviewers = searchParams.getAll('interviewers');
    const customDates = searchParams.getAll('dates');

    // Determine which users to fetch schedules for
    const allInterviewers = [userId, ...additionalInterviewers];

    // Fetch schedules for all interviewers (new JSONB format)
    const { data: scheduleRecords, error } = await supabase
      .from('bpo_schedules')
      .select('*')
      .in('user_id', allInterviewers);

    if (error) {
      console.error('Error fetching schedule:', error);
      return NextResponse.json(
        { error: 'Failed to fetch schedule' },
        { status: 500 }
      );
    }

    // Calculate date range based on parameters
    let calculatedStartDate = startDate;
    let calculatedEndDate = endDate;

    if (!startDate || !endDate) {
      const now = new Date();

      switch (dateRange) {
        case 'week':
          calculatedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString().split('T')[0];
          calculatedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 7).toISOString().split('T')[0];
          break;
        case 'month':
          calculatedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString().split('T')[0];
          calculatedEndDate = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate()).toISOString().split('T')[0];
          break;
        case 'custom':
          if (customDates.length > 0) {
            calculatedStartDate = customDates[0];
            calculatedEndDate = customDates[customDates.length - 1];
          }
          break;
        default:
          calculatedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString().split('T')[0];
          calculatedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 7).toISOString().split('T')[0];
      }
    }

    // Generate available time slots for the requested date range
    const availableSlots = generateAvailableSlots(
      scheduleRecords || [],
      calculatedStartDate,
      calculatedEndDate,
      durationMinutes,
      allInterviewers,
      customDates
    );

    return NextResponse.json({
      success: true,
      availableSlots,
      totalSlots: availableSlots.length
    });

  } catch (error) {
    console.error('Error in available slots API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function generateAvailableSlots(
  scheduleRecords: any[],
  startDate?: string | null,
  endDate?: string | null,
  durationMinutes: number = 60,
  allInterviewers: string[] = [],
  customDates: string[] = []
): any[] {
  const slots: any[] = [];
  
  // Default to next 14 days if no date range provided
  const start = startDate ? new Date(startDate) : new Date();
  const end = endDate ? new Date(endDate) : new Date(Date.now() + 14 * 24 * 60 * 60 * 1000);
  
  // Convert JSONB schedule data to the format expected by the rest of the function
  const scheduleByUserAndDay: { [userId: string]: { [dayOfWeek: number]: { start: string; end: string }[] } } = {};

  scheduleRecords.forEach(record => {
    if (record.schedule_data) {
      scheduleByUserAndDay[record.user_id] = {};

      // Convert JSONB data to day-indexed format
      Object.keys(record.schedule_data).forEach(dayKey => {
        const dayOfWeek = parseInt(dayKey);
        scheduleByUserAndDay[record.user_id][dayOfWeek] = record.schedule_data[dayKey] || [];
      });
    }
  });

  // Generate slots for each day in the range
  const currentDate = new Date(start);
  while (currentDate <= end) {
    const dayOfWeek = currentDate.getDay();
    const dateStr = currentDate.toISOString().split('T')[0];

    // Skip this date if using custom dates and it's not in the list
    if (customDates.length > 0 && !customDates.includes(dateStr)) {
      currentDate.setDate(currentDate.getDate() + 1);
      continue;
    }

    // Find overlapping time slots for all interviewers
    const commonSlots = findCommonAvailableSlots(
      allInterviewers,
      scheduleByUserAndDay,
      dayOfWeek,
      durationMinutes
    );

    commonSlots.forEach(commonSlot => {
      const slotStart = new Date(currentDate);
      slotStart.setHours(commonSlot.startHour, commonSlot.startMinute, 0, 0);

      const slotEnd = new Date(currentDate);
      slotEnd.setHours(commonSlot.endHour, commonSlot.endMinute, 0, 0);

      // Generate time slots within this availability window
      const windowStart = new Date(slotStart);
      while (windowStart.getTime() + (durationMinutes * 60 * 1000) <= slotEnd.getTime()) {
        const windowEnd = new Date(windowStart.getTime() + (durationMinutes * 60 * 1000));

        // Only include future slots
        if (windowStart > new Date()) {
          slots.push({
            id: `slot_${windowStart.getTime()}`,
            startTime: windowStart.toISOString(),
            endTime: windowEnd.toISOString(),
            date: dateStr,
            dayOfWeek: dayOfWeek,
            duration: durationMinutes,
            isAvailable: true,
            interviewers: allInterviewers
          });
        }

        // Move to next 30-minute slot
        windowStart.setMinutes(windowStart.getMinutes() + 30);
      }
    });

    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return slots.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());
}

function findCommonAvailableSlots(
  interviewers: string[],
  scheduleByUserAndDay: { [userId: string]: { [dayOfWeek: number]: { start: string; end: string }[] } },
  dayOfWeek: number,
  durationMinutes: number
): { startHour: number; startMinute: number; endHour: number; endMinute: number }[] {
  if (interviewers.length === 0) return [];

  // If only one interviewer, return their slots
  if (interviewers.length === 1) {
    const userSlots = scheduleByUserAndDay[interviewers[0]]?.[dayOfWeek] || [];
    return userSlots.map(slot => {
      const [startHour, startMinute] = slot.start.split(':').map(Number);
      const [endHour, endMinute] = slot.end.split(':').map(Number);
      return { startHour, startMinute, endHour, endMinute };
    });
  }

  // For multiple interviewers, find overlapping time slots
  const commonSlots: { startHour: number; startMinute: number; endHour: number; endMinute: number }[] = [];

  // Get all time slots for the first interviewer
  const firstInterviewerSlots = scheduleByUserAndDay[interviewers[0]]?.[dayOfWeek] || [];

  firstInterviewerSlots.forEach(baseSlot => {
    const [baseStartHour, baseStartMinute] = baseSlot.start.split(':').map(Number);
    const [baseEndHour, baseEndMinute] = baseSlot.end.split(':').map(Number);

    let overlapStart = baseStartHour * 60 + baseStartMinute; // Convert to minutes
    let overlapEnd = baseEndHour * 60 + baseEndMinute;

    // Check overlap with all other interviewers
    let hasOverlap = true;

    for (let i = 1; i < interviewers.length; i++) {
      const otherInterviewerSlots = scheduleByUserAndDay[interviewers[i]]?.[dayOfWeek] || [];

      let foundOverlap = false;

      otherInterviewerSlots.forEach(otherSlot => {
        const [otherStartHour, otherStartMinute] = otherSlot.start.split(':').map(Number);
        const [otherEndHour, otherEndMinute] = otherSlot.end.split(':').map(Number);

        const otherStart = otherStartHour * 60 + otherStartMinute;
        const otherEnd = otherEndHour * 60 + otherEndMinute;

        // Check if there's an overlap
        const maxStart = Math.max(overlapStart, otherStart);
        const minEnd = Math.min(overlapEnd, otherEnd);

        if (maxStart < minEnd && (minEnd - maxStart) >= durationMinutes) {
          overlapStart = maxStart;
          overlapEnd = minEnd;
          foundOverlap = true;
        }
      });

      if (!foundOverlap) {
        hasOverlap = false;
        break;
      }
    }

    // If we found a common overlap, add it to the result
    if (hasOverlap && (overlapEnd - overlapStart) >= durationMinutes) {
      commonSlots.push({
        startHour: Math.floor(overlapStart / 60),
        startMinute: overlapStart % 60,
        endHour: Math.floor(overlapEnd / 60),
        endMinute: overlapEnd % 60
      });
    }
  });

  return commonSlots;
}
