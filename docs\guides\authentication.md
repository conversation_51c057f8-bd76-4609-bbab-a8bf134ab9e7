# 🔐 Authentication System Guide

Comprehensive guide to the BPO Training Platform's authentication and authorization system, built with Supabase Auth and Row Level Security (RLS).

## 🎯 Authentication Overview

The platform uses a **multi-layered security approach**:

1. **Supabase Authentication** - JWT-based user authentication
2. **Row Level Security (RLS)** - Database-level access control
3. **Role-Based Access Control (RBAC)** - Application-level permissions
4. **API Security** - Request validation and rate limiting

## 👥 User Roles & Permissions

### Role Hierarchy

```
Platform Admin (admin)
├── Full platform access
├── User management
├── Content management
├── System configuration
└── Analytics and reporting

BPO Admin (bpo_admin)
├── Company management
├── Job posting management
├── Candidate review
├── Team member management
└── Company analytics

Prospect (prospect)
├── Profile management
├── Training access
├── Job applications
├── Progress tracking
└── Certificate viewing
```

### Permission Matrix

| Feature | Platform Admin | BPO Admin | Prospect |
|---------|---------------|-----------|----------|
| User Management | ✅ Full | ❌ None | ❌ None |
| Training Content | ✅ Full | ❌ View Only | ✅ Assigned |
| Job Postings | ✅ Full | ✅ Own Company | ✅ View/Apply |
| Applications | ✅ All | ✅ Own Jobs | ✅ Own Apps |
| Analytics | ✅ Platform | ✅ Company | ✅ Personal |
| System Settings | ✅ Full | ❌ None | ❌ None |

## 🔑 Authentication Flow

### User Registration

```typescript
// Registration process
const registerUser = async (userData: {
  email: string
  password: string
  full_name: string
  role: 'prospect' | 'bpo_admin'
}) => {
  // 1. Create auth user in Supabase
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email: userData.email,
    password: userData.password,
    options: {
      data: {
        full_name: userData.full_name,
        role: userData.role
      }
    }
  })

  if (authError) throw authError

  // 2. Create user profile
  const { error: profileError } = await supabase
    .from('users')
    .insert({
      id: authData.user!.id,
      email: userData.email,
      full_name: userData.full_name,
      role: userData.role,
      status: 'pending_activation'
    })

  if (profileError) throw profileError

  // 3. Create role-specific profile
  if (userData.role === 'prospect') {
    await createProspectProfile(authData.user!.id)
  }

  return authData
}
```

### User Login

```typescript
// Login process with role verification
const loginUser = async (email: string, password: string) => {
  // 1. Authenticate with Supabase
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email,
    password
  })

  if (authError) throw authError

  // 2. Fetch user profile with role
  const { data: userProfile, error: profileError } = await supabase
    .from('users')
    .select('*')
    .eq('id', authData.user.id)
    .single()

  if (profileError) throw profileError

  // 3. Verify user status
  if (userProfile.status !== 'active') {
    throw new Error('Account is not active')
  }

  // 4. Update last login
  await supabase
    .from('users')
    .update({ last_login: new Date().toISOString() })
    .eq('id', authData.user.id)

  return { user: authData.user, profile: userProfile }
}
```

### Session Management

```typescript
// Session persistence and validation
const useAuth = () => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session?.user) {
        await loadUserProfile(session.user.id)
      }
      
      setLoading(false)
    }

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          await loadUserProfile(session.user.id)
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
          setProfile(null)
        }
      }
    )

    getSession()

    return () => subscription.unsubscribe()
  }, [])

  const loadUserProfile = async (userId: string) => {
    const { data: profile } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()

    setProfile(profile)
    setUser(supabase.auth.getUser())
  }

  return { user, profile, loading }
}
```

## 🛡️ Row Level Security (RLS)

### Security Functions

```sql
-- Check if user is platform admin
CREATE OR REPLACE FUNCTION is_platform_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = auth.uid() 
    AND role = 'admin'
    AND status = 'active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Check if user is BPO admin for specific company
CREATE OR REPLACE FUNCTION is_bpo_admin(bpo_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM bpo_team_members btm
    JOIN users u ON u.id = btm.user_id
    WHERE btm.user_id = auth.uid()
    AND btm.bpo_id = bpo_uuid
    AND btm.role = 'admin'
    AND u.status = 'active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Get user's BPO memberships
CREATE OR REPLACE FUNCTION get_user_bpo_memberships()
RETURNS TABLE(bpo_id UUID, role bpo_role) AS $$
BEGIN
  RETURN QUERY
  SELECT btm.bpo_id, btm.role
  FROM bpo_team_members btm
  JOIN users u ON u.id = btm.user_id
  WHERE btm.user_id = auth.uid()
  AND u.status = 'active'
  AND btm.is_placeholder = false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### RLS Policies

```sql
-- Users table policies
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Platform admins can view all users" ON users
  FOR SELECT USING (is_platform_admin());

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Training modules policies
CREATE POLICY "Published modules visible to prospects" ON training_modules
  FOR SELECT USING (
    status = 'published' AND 
    EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'prospect')
  );

CREATE POLICY "Admins can manage all modules" ON training_modules
  FOR ALL USING (is_platform_admin());

-- Job postings policies
CREATE POLICY "Published jobs visible to prospects" ON job_postings
  FOR SELECT USING (
    status = 'published' AND
    application_deadline > NOW()
  );

CREATE POLICY "BPO admins can manage company jobs" ON job_postings
  FOR ALL USING (
    bpo_id IN (
      SELECT bpo_id FROM get_user_bpo_memberships() 
      WHERE role = 'admin'
    )
  );

-- Applications policies
CREATE POLICY "Prospects can view own applications" ON applications
  FOR SELECT USING (
    prospect_id IN (
      SELECT id FROM prospects WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "BPO admins can view applications to their jobs" ON applications
  FOR SELECT USING (
    job_id IN (
      SELECT id FROM job_postings 
      WHERE bpo_id IN (
        SELECT bpo_id FROM get_user_bpo_memberships() 
        WHERE role = 'admin'
      )
    )
  );
```

## 🔒 Client-Side Authorization

### Route Protection

```typescript
// Protected route component
const ProtectedRoute = ({ 
  children, 
  requiredRole,
  fallback = <Navigate to="/login" />
}: {
  children: React.ReactNode
  requiredRole?: UserRole[]
  fallback?: React.ReactNode
}) => {
  const { user, profile, loading } = useAuth()

  if (loading) {
    return <LoadingSpinner />
  }

  if (!user || !profile) {
    return fallback
  }

  if (requiredRole && !requiredRole.includes(profile.role)) {
    return <Navigate to="/unauthorized" />
  }

  return <>{children}</>
}

// Usage in routing
<Routes>
  <Route path="/admin/*" element={
    <ProtectedRoute requiredRole={['admin']}>
      <AdminLayout />
    </ProtectedRoute>
  } />
  
  <Route path="/bpo/*" element={
    <ProtectedRoute requiredRole={['bpo_admin']}>
      <BPOLayout />
    </ProtectedRoute>
  } />
  
  <Route path="/prospect/*" element={
    <ProtectedRoute requiredRole={['prospect']}>
      <ProspectLayout />
    </ProtectedRoute>
  } />
</Routes>
```

### Permission Hooks

```typescript
// Permission checking hooks
const usePermissions = () => {
  const { profile } = useAuth()

  const can = useCallback((action: string, resource?: string) => {
    if (!profile) return false

    // Platform admin has all permissions
    if (profile.role === 'admin') return true

    // Define permission rules
    const permissions = {
      'view:users': ['admin'],
      'create:training_module': ['admin'],
      'view:training_module': ['admin', 'prospect'],
      'create:job_posting': ['admin', 'bpo_admin'],
      'view:job_posting': ['admin', 'bpo_admin', 'prospect'],
      'apply:job': ['prospect'],
      'review:application': ['admin', 'bpo_admin']
    }

    const allowedRoles = permissions[`${action}:${resource}`] || []
    return allowedRoles.includes(profile.role)
  }, [profile])

  const canManageBPO = useCallback((bpoId: string) => {
    // Check if user is admin of specific BPO
    return checkBPOMembership(bpoId, 'admin')
  }, [])

  return { can, canManageBPO }
}

// Usage in components
const JobPostingCard = ({ job }) => {
  const { can, canManageBPO } = usePermissions()

  return (
    <Card>
      <CardContent>
        <h3>{job.title}</h3>
        <p>{job.description}</p>
        
        {can('apply', 'job') && (
          <Button onClick={() => applyToJob(job.id)}>
            Apply Now
          </Button>
        )}
        
        {canManageBPO(job.bpo_id) && (
          <Button variant="outline" onClick={() => editJob(job.id)}>
            Edit Job
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
```

## 🔐 API Security

### Middleware Authentication

```typescript
// API route middleware
export const withAuth = (handler: NextApiHandler, requiredRole?: UserRole[]) => {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      // Extract JWT token
      const token = req.headers.authorization?.replace('Bearer ', '')
      
      if (!token) {
        return res.status(401).json({ error: 'No token provided' })
      }

      // Verify token with Supabase
      const { data: { user }, error } = await supabase.auth.getUser(token)
      
      if (error || !user) {
        return res.status(401).json({ error: 'Invalid token' })
      }

      // Get user profile
      const { data: profile } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single()

      if (!profile || profile.status !== 'active') {
        return res.status(401).json({ error: 'User not active' })
      }

      // Check role permissions
      if (requiredRole && !requiredRole.includes(profile.role)) {
        return res.status(403).json({ error: 'Insufficient permissions' })
      }

      // Add user to request
      req.user = user
      req.profile = profile

      return handler(req, res)
    } catch (error) {
      return res.status(500).json({ error: 'Authentication error' })
    }
  }
}

// Usage in API routes
export default withAuth(async (req, res) => {
  // Handler logic here
  res.json({ message: 'Authenticated request' })
}, ['admin', 'bpo_admin'])
```

### Request Validation

```typescript
// Input validation with Zod
import { z } from 'zod'

const createJobSchema = z.object({
  title: z.string().min(1).max(100),
  description: z.string().min(10).max(5000),
  employment_type: z.enum(['full_time', 'part_time', 'contract']),
  location_type: z.enum(['remote', 'on_site', 'hybrid']),
  salary_range: z.object({
    min: z.number().positive().optional(),
    max: z.number().positive().optional()
  }).optional()
})

export default withAuth(async (req, res) => {
  if (req.method === 'POST') {
    try {
      // Validate input
      const validatedData = createJobSchema.parse(req.body)
      
      // Create job posting
      const { data, error } = await supabase
        .from('job_postings')
        .insert({
          ...validatedData,
          bpo_id: req.profile.bpo_id,
          created_by: req.user.id
        })
        .select()
        .single()

      if (error) throw error

      res.status(201).json({ success: true, data })
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Validation error', details: error.errors })
      } else {
        res.status(500).json({ error: 'Failed to create job posting' })
      }
    }
  }
}, ['bpo_admin'])
```

## 🔄 Password Security

### Password Requirements

```typescript
const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character')
```

### Password Reset Flow

```typescript
const resetPassword = async (email: string) => {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/reset-password`
  })
  
  if (error) throw error
}

const updatePassword = async (newPassword: string) => {
  const { error } = await supabase.auth.updateUser({
    password: newPassword
  })
  
  if (error) throw error
}
```

## 🔍 Security Monitoring

### Audit Logging

```sql
-- Audit log table
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO audit_logs (
    user_id,
    action,
    resource_type,
    resource_id,
    old_values,
    new_values
  ) VALUES (
    auth.uid(),
    TG_OP,
    TG_TABLE_NAME,
    COALESCE(NEW.id, OLD.id),
    CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN to_jsonb(NEW) ELSE NULL END
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;
```

### Security Headers

```typescript
// Next.js security headers
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
]
```

---

**Next**: Learn about [Performance Optimization](performance-optimization.md) techniques.
