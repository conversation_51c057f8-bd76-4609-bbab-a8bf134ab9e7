/**
 * Data Display Components
 * Standardized components for displaying data in tables, lists, and grids
 */

"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  ChevronDown, 
  ChevronUp, 
  MoreHorizontal, 
  Search, 
  Filter,
  ArrowUpDown,
  Eye,
  Edit,
  Trash2
} from "lucide-react"

// TypeScript interfaces
export interface Column<T = any> {
  key: string
  title: string
  width?: string
  sortable?: boolean
  render?: (value: any, record: T, index: number) => React.ReactNode
  className?: string
}

export interface DataTableProps<T = any> {
  data: T[]
  columns: Column<T>[]
  loading?: boolean
  emptyMessage?: string
  onRowClick?: (record: T, index: number) => void
  onSort?: (key: string, direction: 'asc' | 'desc') => void
  sortKey?: string
  sortDirection?: 'asc' | 'desc'
  className?: string
  variant?: 'default' | 'compact' | 'bordered'
}

export interface DataListProps<T = any> {
  data: T[]
  renderItem: (item: T, index: number) => React.ReactNode
  loading?: boolean
  emptyMessage?: string
  className?: string
  variant?: 'default' | 'compact' | 'cards'
  dividers?: boolean
}

export interface StatsCardProps {
  title: string
  value: string | number
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  trend?: {
    value: number
    label: string
    direction: 'up' | 'down' | 'neutral'
  }
  className?: string
  variant?: 'default' | 'compact' | 'highlighted'
}

export interface UserListItemProps {
  user: {
    id: string
    name: string
    email: string
    avatar?: string
    role?: string
    status?: 'online' | 'offline' | 'away'
    lastSeen?: string
  }
  actions?: React.ReactNode
  onClick?: () => void
  className?: string
}

/**
 * Data Table Component
 */
export const DataTable = React.forwardRef<
  HTMLTableElement,
  DataTableProps
>(({
  data,
  columns,
  loading = false,
  emptyMessage = "No data available",
  onRowClick,
  onSort,
  sortKey,
  sortDirection,
  className,
  variant = 'default',
  ...props
}, ref) => {
  const handleSort = (key: string) => {
    if (!onSort) return
    
    const newDirection = sortKey === key && sortDirection === 'asc' ? 'desc' : 'asc'
    onSort(key, newDirection)
  }
  
  if (loading) {
    return (
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="h-12 bg-muted rounded animate-pulse" />
        ))}
      </div>
    )
  }
  
  if (data.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <p>{emptyMessage}</p>
      </div>
    )
  }
  
  return (
    <div className={cn("overflow-x-auto", className)}>
      <table
        ref={ref}
        className={cn(
          "w-full",
          variant === 'bordered' && "border border-border rounded-lg",
          variant === 'compact' && "text-sm"
        )}
        {...props}
      >
        <thead>
          <tr className={cn(
            "border-b border-border",
            variant === 'bordered' && "bg-muted/50"
          )}>
            {columns.map((column) => (
              <th
                key={column.key}
                className={cn(
                  "text-left font-medium text-muted-foreground",
                  variant === 'compact' ? "px-3 py-2" : "px-4 py-3",
                  column.width && `w-${column.width}`,
                  column.sortable && "cursor-pointer hover:text-foreground",
                  column.className
                )}
                onClick={() => column.sortable && handleSort(column.key)}
                style={column.width ? { width: column.width } : undefined}
              >
                <div className="flex items-center gap-2">
                  {column.title}
                  {column.sortable && (
                    <div className="flex flex-col">
                      {sortKey === column.key ? (
                        sortDirection === 'asc' ? (
                          <ChevronUp className="h-3 w-3" />
                        ) : (
                          <ChevronDown className="h-3 w-3" />
                        )
                      ) : (
                        <ArrowUpDown className="h-3 w-3 opacity-50" />
                      )}
                    </div>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((record, index) => (
            <tr
              key={index}
              className={cn(
                "border-b border-border last:border-b-0",
                onRowClick && "cursor-pointer hover:bg-muted/50",
                variant === 'compact' && "text-sm"
              )}
              onClick={() => onRowClick?.(record, index)}
            >
              {columns.map((column) => (
                <td
                  key={column.key}
                  className={cn(
                    variant === 'compact' ? "px-3 py-2" : "px-4 py-3",
                    column.className
                  )}
                >
                  {column.render 
                    ? column.render(record[column.key], record, index)
                    : record[column.key]
                  }
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
})

DataTable.displayName = "DataTable"

/**
 * Data List Component
 */
export const DataList = React.forwardRef<
  HTMLDivElement,
  DataListProps
>(({
  data,
  renderItem,
  loading = false,
  emptyMessage = "No items found",
  className,
  variant = 'default',
  dividers = false,
  ...props
}, ref) => {
  if (loading) {
    return (
      <div ref={ref} className={cn("space-y-3", className)} {...props}>
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="h-16 bg-muted rounded animate-pulse" />
        ))}
      </div>
    )
  }
  
  if (data.length === 0) {
    return (
      <div ref={ref} className={cn("text-center py-8 text-muted-foreground", className)} {...props}>
        <p>{emptyMessage}</p>
      </div>
    )
  }
  
  const gapClasses = {
    default: "space-y-4",
    compact: "space-y-2",
    cards: "space-y-6"
  }
  
  if (dividers) {
    return (
      <div ref={ref} className={cn("space-y-0", className)} {...props}>
        {data.map((item, index) => (
          <React.Fragment key={index}>
            {renderItem(item, index)}
            {index < data.length - 1 && <Separator className="my-4" />}
          </React.Fragment>
        ))}
      </div>
    )
  }
  
  return (
    <div ref={ref} className={cn(gapClasses[variant], className)} {...props}>
      {data.map((item, index) => renderItem(item, index))}
    </div>
  )
})

DataList.displayName = "DataList"

/**
 * Stats Card Component
 */
export const StatsCard = React.forwardRef<
  HTMLDivElement,
  StatsCardProps
>(({
  title,
  value,
  description,
  icon: Icon,
  trend,
  className,
  variant = 'default',
  ...props
}, ref) => {
  return (
    <Card
      ref={ref}
      className={cn(
        "border-none shadow-sm",
        variant === 'highlighted' && "bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20",
        className
      )}
      {...props}
    >
      <CardContent className={cn(
        "p-6",
        variant === 'compact' && "p-4"
      )}>
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <p className={cn(
              "text-muted-foreground font-medium",
              variant === 'compact' ? "text-sm" : "text-base"
            )}>
              {title}
            </p>
            <p className={cn(
              "font-bold text-foreground",
              variant === 'compact' ? "text-xl" : "text-2xl"
            )}>
              {value}
            </p>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">
                {description}
              </p>
            )}
          </div>
          {Icon && (
            <div className={cn(
              "flex-shrink-0 p-2 rounded-lg bg-muted",
              variant === 'highlighted' && "bg-blue-100 dark:bg-blue-900/20"
            )}>
              <Icon className={cn(
                "text-muted-foreground",
                variant === 'compact' ? "h-4 w-4" : "h-5 w-5",
                variant === 'highlighted' && "text-blue-600 dark:text-blue-400"
              )} />
            </div>
          )}
        </div>
        {trend && (
          <div className="flex items-center gap-1 mt-2">
            <Badge
              variant={trend.direction === 'up' ? 'default' : trend.direction === 'down' ? 'destructive' : 'secondary'}
              className="text-xs"
            >
              {trend.direction === 'up' ? '↗' : trend.direction === 'down' ? '↘' : '→'} {trend.value}%
            </Badge>
            <span className="text-xs text-muted-foreground">{trend.label}</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
})

StatsCard.displayName = "StatsCard"

/**
 * User List Item Component
 */
export const UserListItem = React.forwardRef<
  HTMLDivElement,
  UserListItemProps
>(({
  user,
  actions,
  onClick,
  className,
  ...props
}, ref) => {
  const statusColors = {
    online: 'bg-green-500',
    offline: 'bg-gray-400',
    away: 'bg-yellow-500'
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center justify-between p-4 rounded-lg border bg-card hover:bg-muted/50 transition-colors",
        onClick && "cursor-pointer",
        className
      )}
      onClick={onClick}
      {...props}
    >
      <div className="flex items-center gap-3 min-w-0 flex-1">
        <div className="relative">
          <Avatar className="h-10 w-10">
            {user.avatar && <AvatarImage src={user.avatar} alt={user.name} />}
            <AvatarFallback>
              {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          {user.status && (
            <div className={cn(
              "absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full border-2 border-background",
              statusColors[user.status]
            )} />
          )}
        </div>
        <div className="min-w-0 flex-1">
          <p className="font-medium truncate">{user.name}</p>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span className="truncate">{user.email}</span>
            {user.role && (
              <>
                <span>•</span>
                <Badge variant="outline" className="text-xs">
                  {user.role}
                </Badge>
              </>
            )}
          </div>
          {user.lastSeen && (
            <p className="text-xs text-muted-foreground mt-0.5">
              Last seen {user.lastSeen}
            </p>
          )}
        </div>
      </div>
      {actions && (
        <div className="flex items-center gap-1 flex-shrink-0">
          {actions}
        </div>
      )}
    </div>
  )
})

UserListItem.displayName = "UserListItem"
