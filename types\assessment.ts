import { Database } from './database.types';

export type Assessment = Database['public']['Tables']['assessments']['Row'];
export type AssessmentInsert = Database['public']['Tables']['assessments']['Insert'];
export type AssessmentUpdate = Database['public']['Tables']['assessments']['Update'];

export type AssessmentCompletion = Database['public']['Tables']['assessment_completions']['Row'];
export type AssessmentCompletionInsert = Database['public']['Tables']['assessment_completions']['Insert'];
export type AssessmentCompletionUpdate = Database['public']['Tables']['assessment_completions']['Update'];

export type AssessmentQuestion = Database['public']['Tables']['assessment_questions']['Row'];
export type AssessmentQuestionInsert = Database['public']['Tables']['assessment_questions']['Insert'];
export type AssessmentQuestionUpdate = Database['public']['Tables']['assessment_questions']['Update'];

export type AssessmentWithStatus = Assessment & {
  status?: 'not_started' | 'in_progress' | 'completed';
  score?: number | null;
  completion_time?: number | null;
  completed_at?: string | null;
};

export type AssessmentCategory = 'Critical Thinking' | 'Soft Skills' | 'Temperament' | 'Technical';
export type AssessmentStatus = 'started' | 'completed' | 'abandoned';

export type QuestionType = 'multiple_choice' | 'text_input' | 'typing_test';

export interface AssessmentStats {
  total: number;
  active: number;
  inactive: number;
  technical: number;
  softSkills: number;
  totalCompletions: number;
  avgScore: number;
} 