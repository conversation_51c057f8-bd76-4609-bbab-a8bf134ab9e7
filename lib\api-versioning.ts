/**
 * API Versioning System
 * Handles API version management, deprecation, and backward compatibility
 */

import { NextRequest, NextResponse } from 'next/server';
import { createError, ErrorType } from '@/lib/utils';
import { handleApiError } from '@/lib/api-error-handler';

// API version configuration
export const API_VERSIONS = {
  CURRENT: 'v1',
  SUPPORTED: ['v1'],
  DEPRECATED: [] as string[],
  SUNSET: [] as string[]
} as const;

// Version-specific configurations
export const VERSION_CONFIG = {
  v1: {
    introduced: '2025-01-01',
    deprecated: null,
    sunset: null,
    features: [
      'authentication',
      'user-management',
      'training-modules',
      'progress-tracking',
      'file-uploads',
      'admin-dashboard'
    ]
  }
} as const;

/**
 * API version detection and validation middleware
 */
export function withApiVersioning(
  handler: (request: NextRequest, version: string) => Promise<NextResponse>
) {
  return async function (request: NextRequest): Promise<NextResponse> {
    try {
      // Extract version from URL path or header
      const version = extractApiVersion(request);
      
      // Validate version
      const validationResult = validateApiVersion(version);
      if (!validationResult.isValid) {
        return handleApiError(validationResult.error!, 'API Versioning Middleware');
      }
      
      // Add version headers to response
      const response = await handler(request, version);
      addVersionHeaders(response, version);
      
      return response;
      
    } catch (error) {
      return handleApiError(error, 'API Versioning Middleware Error');
    }
  };
}

/**
 * Extract API version from request
 */
function extractApiVersion(request: NextRequest): string {
  // Method 1: Check URL path (/api/v1/...)
  const pathMatch = request.nextUrl.pathname.match(/^\/api\/(v\d+)\//);
  if (pathMatch) {
    return pathMatch[1];
  }
  
  // Method 2: Check Accept header (application/vnd.bpo-training.v1+json)
  const acceptHeader = request.headers.get('accept');
  if (acceptHeader) {
    const versionMatch = acceptHeader.match(/application\/vnd\.bpo-training\.(v\d+)\+json/);
    if (versionMatch) {
      return versionMatch[1];
    }
  }
  
  // Method 3: Check custom header
  const versionHeader = request.headers.get('x-api-version');
  if (versionHeader) {
    return versionHeader;
  }
  
  // Default to current version
  return API_VERSIONS.CURRENT;
}

/**
 * Validate API version
 */
function validateApiVersion(version: string): { 
  isValid: boolean; 
  error?: Error;
  warnings?: string[];
} {
  const warnings: string[] = [];
  
  // Check if version is supported
  if (!API_VERSIONS.SUPPORTED.includes(version as any)) {
    return {
      isValid: false,
      error: createError(
        ErrorType.VALIDATION,
        `Unsupported API version: ${version}`,
        `API version ${version} is not supported. Supported versions: ${API_VERSIONS.SUPPORTED.join(', ')}`,
        {
          requestedVersion: version,
          supportedVersions: API_VERSIONS.SUPPORTED,
          currentVersion: API_VERSIONS.CURRENT
        }
      )
    };
  }
  
  // Check if version is deprecated
  if (API_VERSIONS.DEPRECATED.includes(version as any)) {
    warnings.push(`API version ${version} is deprecated. Please migrate to ${API_VERSIONS.CURRENT}.`);
  }
  
  // Check if version is sunset
  if (API_VERSIONS.SUNSET.includes(version as any)) {
    const config = VERSION_CONFIG[version as keyof typeof VERSION_CONFIG];
    warnings.push(`API version ${version} will be sunset on ${config?.sunset}. Please migrate immediately.`);
  }
  
  return {
    isValid: true,
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Add version-related headers to response
 */
function addVersionHeaders(response: NextResponse, version: string): void {
  response.headers.set('X-API-Version', version);
  response.headers.set('X-API-Current-Version', API_VERSIONS.CURRENT);
  response.headers.set('X-API-Supported-Versions', API_VERSIONS.SUPPORTED.join(', '));
  
  // Add deprecation warnings
  if (API_VERSIONS.DEPRECATED.includes(version as any)) {
    response.headers.set('Warning', `299 - "API version ${version} is deprecated"`);
    response.headers.set('Sunset', VERSION_CONFIG[version as keyof typeof VERSION_CONFIG]?.sunset || '');
  }
}

/**
 * Version-specific feature flags
 */
export function isFeatureAvailable(version: string, feature: string): boolean {
  const config = VERSION_CONFIG[version as keyof typeof VERSION_CONFIG];
  return config?.features.includes(feature) || false;
}

/**
 * Create versioned API response
 */
export function createVersionedResponse(
  data: any,
  version: string,
  options: {
    deprecationNotice?: string;
    migrationGuide?: string;
  } = {}
): any {
  const response = {
    success: true,
    data,
    meta: {
      version,
      timestamp: new Date().toISOString(),
      ...(options.deprecationNotice && { deprecationNotice: options.deprecationNotice }),
      ...(options.migrationGuide && { migrationGuide: options.migrationGuide })
    }
  };
  
  return response;
}

/**
 * Handle version-specific data transformations
 */
export function transformDataForVersion(data: any, version: string): any {
  switch (version) {
    case 'v1':
      return transformForV1(data);
    default:
      return data;
  }
}

/**
 * Transform data for v1 API
 */
function transformForV1(data: any): any {
  // V1 specific transformations
  if (Array.isArray(data)) {
    return data.map(item => transformItemForV1(item));
  }
  
  return transformItemForV1(data);
}

function transformItemForV1(item: any): any {
  if (!item || typeof item !== 'object') {
    return item;
  }
  
  // Remove internal fields
  const { 
    internal_id, 
    legacy_data, 
    debug_info,
    ...publicData 
  } = item;
  
  // Transform date fields to ISO strings
  Object.keys(publicData).forEach(key => {
    if (publicData[key] instanceof Date) {
      publicData[key] = publicData[key].toISOString();
    }
  });
  
  return publicData;
}

/**
 * API deprecation utilities
 */
export class ApiDeprecation {
  static logDeprecationUsage(version: string, endpoint: string, userAgent?: string): void {
    console.warn(`[API_DEPRECATION] Version ${version} used for ${endpoint}`, {
      version,
      endpoint,
      userAgent,
      timestamp: new Date().toISOString()
    });
  }
  
  static createDeprecationNotice(
    currentVersion: string,
    targetVersion: string,
    sunsetDate?: string
  ): string {
    let notice = `API version ${currentVersion} is deprecated. Please migrate to ${targetVersion}.`;
    
    if (sunsetDate) {
      notice += ` This version will be sunset on ${sunsetDate}.`;
    }
    
    return notice;
  }
  
  static getMigrationGuide(fromVersion: string, toVersion: string): string {
    return `https://docs.bpotraining.com/api/migration/${fromVersion}-to-${toVersion}`;
  }
}

/**
 * Version compatibility checker
 */
export function checkVersionCompatibility(
  requestedVersion: string,
  requiredFeatures: string[]
): {
  compatible: boolean;
  missingFeatures: string[];
  recommendations: string[];
} {
  const config = VERSION_CONFIG[requestedVersion as keyof typeof VERSION_CONFIG];
  
  if (!config) {
    return {
      compatible: false,
      missingFeatures: requiredFeatures,
      recommendations: [`Upgrade to version ${API_VERSIONS.CURRENT}`]
    };
  }
  
  const missingFeatures = requiredFeatures.filter(
    feature => !config.features.includes(feature)
  );
  
  const recommendations: string[] = [];
  
  if (missingFeatures.length > 0) {
    recommendations.push(`Upgrade to version ${API_VERSIONS.CURRENT} for full feature support`);
  }
  
  if (API_VERSIONS.DEPRECATED.includes(requestedVersion as any)) {
    recommendations.push(`Version ${requestedVersion} is deprecated, please migrate`);
  }
  
  return {
    compatible: missingFeatures.length === 0,
    missingFeatures,
    recommendations
  };
}

/**
 * Create API documentation metadata
 */
export function getApiDocumentation(version: string) {
  const config = VERSION_CONFIG[version as keyof typeof VERSION_CONFIG];
  
  return {
    version,
    introduced: config?.introduced,
    deprecated: config?.deprecated,
    sunset: config?.sunset,
    features: config?.features || [],
    status: API_VERSIONS.DEPRECATED.includes(version as any) 
      ? 'deprecated' 
      : API_VERSIONS.SUNSET.includes(version as any)
      ? 'sunset'
      : 'active',
    documentation: `https://docs.bpotraining.com/api/${version}`,
    changelog: `https://docs.bpotraining.com/api/${version}/changelog`
  };
}
