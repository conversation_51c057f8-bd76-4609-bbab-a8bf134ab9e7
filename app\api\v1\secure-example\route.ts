/**
 * Secure API Route Example
 * Demonstrates comprehensive API security implementation
 */

import { NextRequest, NextResponse } from 'next/server';
import { withApiErrorHandler } from '@/lib/api-error-handler';
import { getAuthenticatedUser } from '@/lib/auth';
import { withApiSecurity, RATE_LIMITS } from '@/lib/api-security';
import { withApiVersioning, createVersionedResponse } from '@/lib/api-versioning';
import { withValidation, COMMON_SCHEMAS } from '@/lib/api-validation';
import { securityAudit, SecurityEventType } from '@/lib/security-audit';
import { createAdminClient } from '@/lib/supabase-server';

/**
 * GET /api/v1/secure-example
 * Secure endpoint demonstrating all security features
 */
export const GET = withApiSecurity(
  withApiVersioning(
    withApiErrorHandler(async (request: NextRequest, version: string) => {
      // Authenticate user
      const authResult = await getAuthenticatedUser();
      if (!authResult.user) {
        securityAudit.logApiSecurityEvent(
          SecurityEventType.UNAUTHORIZED_ACCESS,
          request,
          'Attempted access to secure endpoint without authentication'
        );
        throw new Error('Authentication required');
      }

      const user = authResult.user;
      
      // Log successful access
      securityAudit.logEvent(
        SecurityEventType.SENSITIVE_DATA_ACCESS,
        `User ${user.email} accessed secure example endpoint`,
        { endpoint: '/api/v1/secure-example', version },
        { request, userId: user.id, userEmail: user.email, userRole: user.role }
      );

      // Fetch some example data
      const supabase = createAdminClient();
      const { data: modules, error } = await supabase
        .from('training_modules')
        .select('id, title, description, status, created_at')
        .eq('status', 'published')
        .limit(10);

      if (error) {
        throw error;
      }

      // Return versioned response
      return NextResponse.json(
        createVersionedResponse(
          {
            message: 'Secure endpoint accessed successfully',
            user: {
              id: user.id,
              email: user.email,
              role: user.role
            },
            modules: modules || [],
            security: {
              authenticated: true,
              version,
              timestamp: new Date().toISOString()
            }
          },
          version
        )
      );
    })
  ),
  {
    rateLimit: RATE_LIMITS.DEFAULT,
    maxRequestSize: 1024 * 1024, // 1MB
    skipInputSanitization: false
  }
);

/**
 * POST /api/v1/secure-example
 * Secure endpoint with comprehensive validation
 */
export const POST = withApiSecurity(
  withApiVersioning(
    withValidation(
      {
        title: { type: 'string', required: true, minLength: 3, maxLength: 100, sanitize: true },
        description: { type: 'string', required: true, minLength: 10, maxLength: 500, sanitize: true },
        category: { type: 'string', required: true, enum: ['technical', 'soft-skills', 'industry'] },
        duration: { type: 'number', required: true, min: 1, max: 480 },
        tags: { 
          type: 'array', 
          items: { type: 'string', maxLength: 30, sanitize: true }
        },
        metadata: {
          type: 'object',
          properties: {
            difficulty: { type: 'string', enum: ['beginner', 'intermediate', 'advanced'] },
            prerequisites: { type: 'array', items: { type: 'string', maxLength: 100 } }
          }
        }
      },
      { validateBody: true, allowExtraFields: false, logValidationErrors: true }
    )(
      withApiErrorHandler(async (request: NextRequest, version: string) => {
        // Authenticate user and require admin access
        const authResult = await getAuthenticatedUser();
        if (!authResult.user) {
          securityAudit.logApiSecurityEvent(
            SecurityEventType.UNAUTHORIZED_ACCESS,
            request,
            'Attempted POST to secure endpoint without authentication'
          );
          throw new Error('Authentication required');
        }

        if (authResult.user.role !== 'admin') {
          securityAudit.logEvent(
            SecurityEventType.PRIVILEGE_ESCALATION,
            `User ${authResult.user.email} attempted admin action without privileges`,
            { 
              endpoint: '/api/v1/secure-example',
              method: 'POST',
              userRole: authResult.user.role,
              requiredRole: 'admin'
            },
            { 
              request, 
              userId: authResult.user.id, 
              userEmail: authResult.user.email, 
              userRole: authResult.user.role 
            }
          );
          throw new Error('Admin access required');
        }

        const user = authResult.user;
        
        // Parse validated request body
        const body = await request.json();
        
        // Log admin action
        securityAudit.logEvent(
          SecurityEventType.ADMIN_ACCESS,
          `Admin ${user.email} created new training module`,
          { 
            action: 'create_training_module',
            moduleTitle: body.title,
            category: body.category
          },
          { request, userId: user.id, userEmail: user.email, userRole: user.role }
        );

        // Create the training module
        const supabase = createAdminClient();
        const { data: newModule, error } = await supabase
          .from('training_modules')
          .insert({
            title: body.title,
            description: body.description,
            duration_minutes: body.duration,
            status: 'draft',
            created_by: user.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) {
          throw error;
        }

        // Return versioned response
        return NextResponse.json(
          createVersionedResponse(
            {
              message: 'Training module created successfully',
              module: newModule,
              createdBy: {
                id: user.id,
                email: user.email
              }
            },
            version
          ),
          { status: 201 }
        );
      })
    )
  ),
  {
    rateLimit: RATE_LIMITS.ADMIN,
    maxRequestSize: 2 * 1024 * 1024, // 2MB for admin operations
    skipInputSanitization: false
  }
);

/**
 * PUT /api/v1/secure-example/[id]
 * Secure update endpoint with parameter validation
 */
export const PUT = withApiSecurity(
  withApiVersioning(
    withValidation(
      {
        title: { type: 'string', minLength: 3, maxLength: 100, sanitize: true },
        description: { type: 'string', minLength: 10, maxLength: 500, sanitize: true },
        status: { type: 'string', enum: ['draft', 'published', 'archived'] },
        duration: { type: 'number', min: 1, max: 480 }
      },
      { validateBody: true, allowExtraFields: false }
    )(
      withApiErrorHandler(async (request: NextRequest, version: string) => {
        // Extract ID from URL
        const url = new URL(request.url);
        const pathParts = url.pathname.split('/');
        const moduleId = pathParts[pathParts.length - 1];
        
        // Validate UUID format
        const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidPattern.test(moduleId)) {
          securityAudit.logApiSecurityEvent(
            SecurityEventType.MALFORMED_REQUEST,
            request,
            'Invalid UUID format in URL parameter',
            { moduleId }
          );
          throw new Error('Invalid module ID format');
        }

        // Authenticate user
        const authResult = await getAuthenticatedUser();
        if (!authResult.user) {
          throw new Error('Authentication required');
        }

        const user = authResult.user;
        const body = await request.json();
        
        // Log update action
        securityAudit.logEvent(
          SecurityEventType.SENSITIVE_DATA_ACCESS,
          `User ${user.email} updated training module ${moduleId}`,
          { 
            action: 'update_training_module',
            moduleId,
            updates: Object.keys(body)
          },
          { request, userId: user.id, userEmail: user.email, userRole: user.role }
        );

        // Update the module
        const supabase = createAdminClient();
        const { data: updatedModule, error } = await supabase
          .from('training_modules')
          .update({
            ...body,
            updated_at: new Date().toISOString()
          })
          .eq('id', moduleId)
          .select()
          .single();

        if (error) {
          throw error;
        }

        return NextResponse.json(
          createVersionedResponse(
            {
              message: 'Training module updated successfully',
              module: updatedModule
            },
            version
          )
        );
      })
    )
  ),
  {
    rateLimit: RATE_LIMITS.DEFAULT,
    maxRequestSize: 1024 * 1024
  }
);

/**
 * DELETE /api/v1/secure-example/[id]
 * Secure delete endpoint with admin-only access
 */
export const DELETE = withApiSecurity(
  withApiVersioning(
    withApiErrorHandler(async (request: NextRequest, version: string) => {
      // Extract ID from URL
      const url = new URL(request.url);
      const pathParts = url.pathname.split('/');
      const moduleId = pathParts[pathParts.length - 1];
      
      // Validate UUID
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidPattern.test(moduleId)) {
        throw new Error('Invalid module ID format');
      }

      // Authenticate and require admin
      const authResult = await getAuthenticatedUser();
      if (!authResult.user || authResult.user.role !== 'admin') {
        securityAudit.logEvent(
          SecurityEventType.UNAUTHORIZED_ACCESS,
          `Unauthorized delete attempt on module ${moduleId}`,
          { 
            moduleId,
            userRole: authResult.user?.role || 'unauthenticated'
          },
          { 
            request, 
            userId: authResult.user?.id, 
            userEmail: authResult.user?.email 
          }
        );
        throw new Error('Admin access required');
      }

      const user = authResult.user;
      
      // Log critical admin action
      securityAudit.logEvent(
        SecurityEventType.ADMIN_ACCESS,
        `Admin ${user.email} deleted training module ${moduleId}`,
        { 
          action: 'delete_training_module',
          moduleId,
          severity: 'high'
        },
        { request, userId: user.id, userEmail: user.email, userRole: user.role }
      );

      // Delete the module
      const supabase = createAdminClient();
      const { error } = await supabase
        .from('training_modules')
        .delete()
        .eq('id', moduleId);

      if (error) {
        throw error;
      }

      return NextResponse.json(
        createVersionedResponse(
          {
            message: 'Training module deleted successfully',
            moduleId
          },
          version
        )
      );
    })
  ),
  {
    rateLimit: { windowMs: 60 * 1000, maxRequests: 5 }, // Very restrictive for delete operations
    maxRequestSize: 1024 // Minimal size for delete requests
  }
);
