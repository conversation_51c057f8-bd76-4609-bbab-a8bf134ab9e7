#!/usr/bin/env node

/**
 * Script to create required storage buckets in Supabase
 * Run with: node database/scripts/create-storage-buckets.js
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');

// Try to load from .env.local directly
let supabaseUrl = '';
let supabaseServiceKey = '';
const envLocalPath = path.resolve(process.cwd(), '.env.local');

if (fs.existsSync(envLocalPath)) {
  console.log('Loading environment variables from .env.local');
  const envLocalContent = fs.readFileSync(envLocalPath, 'utf8');
  
  // Extract values directly using regex patterns
  const urlMatch = envLocalContent.match(/NEXT_PUBLIC_SUPABASE_URL=([^\n]+)/);
  if (urlMatch && urlMatch[1]) {
    supabaseUrl = urlMatch[1].trim();
  }
  
  // Find service role key which might span multiple lines
  const serviceKeyMatch = envLocalContent.match(/SUPABASE_SERVICE_ROLE_KEY=([\s\S]+?)(\n\s*\n|\n[A-Z#]|$)/);
  if (serviceKeyMatch && serviceKeyMatch[1]) {
    // Clean up any line breaks that might be in the key
    supabaseServiceKey = serviceKeyMatch[1].replace(/\s+/g, '').trim();
  }
} else {
  // Fallback to dotenv
  console.log('No .env.local found, trying dotenv');
  dotenv.config();
  
  supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
}

console.log('Supabase URL:', supabaseUrl ? `${supabaseUrl.slice(0, 20)}...` : 'Not defined');
console.log('Service Role Key:', supabaseServiceKey ? `${supabaseServiceKey.slice(0, 10)}...` : 'Not defined');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Missing Supabase URL or service role key in environment variables');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Buckets to check/create
const requiredBuckets = [
  {
    name: 'training-media',
    public: true,
    description: 'Storage for training module media (slides, images, etc.)'
  },
  {
    name: 'user-uploads',
    public: true,
    description: 'Storage for user-uploaded files'
  }
];

async function createBuckets() {
  console.log('🚀 Checking and creating Supabase storage buckets...');
  
  // List existing buckets
  const { data: existingBuckets, error: listError } = await supabase.storage.listBuckets();
  
  if (listError) {
    console.error('❌ Error listing buckets:', listError);
    process.exit(1);
  }
  
  const existingBucketNames = existingBuckets.map(bucket => bucket.name);
  console.log('📁 Existing buckets:', existingBucketNames.join(', ') || 'None');
  
  // Create missing buckets
  for (const bucket of requiredBuckets) {
    if (!existingBucketNames.includes(bucket.name)) {
      console.log(`📦 Creating bucket: ${bucket.name}`);
      
      const { data, error } = await supabase.storage.createBucket(bucket.name, {
        public: bucket.public,
        fileSizeLimit: 10485760, // 10MB
        allowedMimeTypes: [
          'image/jpeg', 
          'image/png', 
          'image/gif', 
          'image/webp',
          'application/pdf',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation' // PPTX
        ]
      });
      
      if (error) {
        console.error(`❌ Error creating bucket ${bucket.name}:`, error);
      } else {
        console.log(`✅ Successfully created bucket: ${bucket.name}`);
      }
    } else {
      console.log(`✅ Bucket already exists: ${bucket.name}`);
    }
  }
  
  console.log('\n🎉 Storage bucket setup complete!');
}

createBuckets().catch(err => {
  console.error('💥 Unexpected error:', err);
  process.exit(1);
});
