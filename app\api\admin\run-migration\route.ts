import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function POST(request: NextRequest) {
  try {
    // Create admin client
    const adminClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    )

    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'database', 'migrations', '10_fix_interviews_table.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')

    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    const results = []
    
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          const { data, error } = await adminClient.rpc('exec_sql', { 
            sql_query: statement + ';' 
          })
          
          if (error) {
            console.error('Migration statement error:', error)
            results.push({ statement: statement.substring(0, 100) + '...', error: error.message })
          } else {
            results.push({ statement: statement.substring(0, 100) + '...', success: true })
          }
        } catch (err: any) {
          console.error('Migration execution error:', err)
          results.push({ statement: statement.substring(0, 100) + '...', error: err.message })
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Migration executed',
      results
    })

  } catch (error: any) {
    console.error('Migration error:', error)
    return NextResponse.json(
      { error: 'Failed to run migration: ' + error.message },
      { status: 500 }
    )
  }
}
