'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';

// Inner component that uses useSearchParams
function InvitationHandlerContent() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [processed, setProcessed] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [metadata, setMetadata] = useState<any>(null);

  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createClientComponentClient();

  // Check if this is an invitation flow
  const isInvitation = searchParams.get('invited') === 'true';
  const bpoId = searchParams.get('bpo');

  // Get the current user
  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getUser();
      setUser(data.user);
      
      // Also get user metadata to check for placeholder info
      if (data.user) {
        setMetadata(data.user.user_metadata || {});
      }
    };
    
    getUser();
  }, [supabase]);

  useEffect(() => {
    const processInvitation = async () => {
      // Only run if this is an invitation and we have a logged-in user
      if (!isInvitation || !bpoId || !user || processed || loading) {
        return;
      }

      try {
        setLoading(true);
        setError(null);

        console.log('Processing invitation for user:', user.id);

        // Get the session for the access token
        const { data: sessionData } = await supabase.auth.getSession();
        const accessToken = sessionData?.session?.access_token;

        if (!accessToken) {
          throw new Error('No access token available');
        }

        // Call our API endpoint to process the invitation
        const response = await fetch('/api/auth/invitation-callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            accessToken,
            userId: user.id,
            metadata: {
              ...metadata,
              bpo_id: bpoId,
              role: metadata.role || 'recruiter', // Default to recruiter if not specified
              placeholder_user_id: metadata.placeholder_user_id // Pass along placeholder ID if it exists
            },
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to process invitation');
        }

        console.log('Invitation processed successfully');
        setProcessed(true);
        
        // Redirect to dashboard after processing
        setTimeout(() => {
          router.push('/dashboard');
        }, 1500);

      } catch (err: any) {
        console.error('Error processing invitation:', err);
        setError(err.message || 'Failed to process invitation');
      } finally {
        setLoading(false);
      }
    };

    processInvitation();
  }, [user, isInvitation, bpoId, processed, router, supabase, loading, metadata]);

  // Only render visible content if we're handling an invitation
  if (!isInvitation || !bpoId) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-card border rounded-lg shadow-lg p-6 max-w-md w-full">
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-center">Setting Up Your Account</h2>
          
          {loading && (
            <div className="flex flex-col items-center justify-center py-4">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
              <p className="text-muted-foreground text-sm text-center">
                Processing your invitation...
              </p>
            </div>
          )}
          
          {error && (
            <Alert variant="destructive">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {processed && (
            <Alert className="bg-green-50 text-green-800 border-green-200">
              <AlertTitle>Success!</AlertTitle>
              <AlertDescription>Your account has been set up successfully. Redirecting to dashboard...</AlertDescription>
            </Alert>
          )}
          
          {!loading && !processed && error && (
            <Button 
              className="w-full" 
              onClick={() => router.push('/dashboard')}
            >
              Continue to Dashboard
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

// Export with Suspense boundary
export function InvitationHandler() {
  return (
    <Suspense fallback={<div className="p-4">Loading invitation handler...</div>}>
      <InvitationHandlerContent />
    </Suspense>
  );
} 