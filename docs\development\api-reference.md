# 🔌 API Reference

Complete documentation for the BPO Training Platform REST API endpoints, including authentication, request/response formats, and examples.

## 🌐 Base URL

```
Development: http://localhost:3000/api
Production: https://your-domain.com/api
```

## 🔐 Authentication

All API endpoints (except public ones) require authentication using JWT tokens.

### Authentication Header
```http
Authorization: Bearer <jwt_token>
```

### Getting a Token
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "full_name": "<PERSON>",
      "role": "prospect"
    },
    "session": {
      "access_token": "jwt_token_here",
      "refresh_token": "refresh_token_here",
      "expires_at": "2024-12-31T23:59:59Z"
    }
  }
}
```

## 📚 API Endpoints

### Authentication Endpoints

#### `POST /api/auth/login`
Authenticate user and return session tokens.

**Request Body:**
```json
{
  "email": "string (required)",
  "password": "string (required)"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": "User object",
    "session": "Session object"
  }
}
```

#### `POST /api/auth/register`
Register a new user account.

**Request Body:**
```json
{
  "email": "string (required)",
  "password": "string (required, min 8 chars)",
  "full_name": "string (required)",
  "role": "prospect | bpo_admin (default: prospect)"
}
```

#### `POST /api/auth/logout`
Logout user and invalidate session.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

#### `POST /api/auth/refresh`
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refresh_token": "string (required)"
}
```

### User Management

#### `GET /api/users/profile`
Get current user's profile information.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "role": "prospect",
    "status": "active",
    "avatar_url": "https://...",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### `PUT /api/users/profile`
Update current user's profile.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "full_name": "string (optional)",
  "avatar_url": "string (optional)"
}
```

#### `GET /api/users` (Admin only)
Get list of all users with pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `role`: Filter by role
- `status`: Filter by status
- `search`: Search by name or email

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "uuid",
        "email": "<EMAIL>",
        "full_name": "John Doe",
        "role": "prospect",
        "status": "active",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "pages": 8
    }
  }
}
```

### Training System

#### `GET /api/training/modules`
Get list of training modules.

**Query Parameters:**
- `status`: Filter by status (published, draft)
- `search`: Search by title or description
- `page`: Page number
- `limit`: Items per page

**Response:**
```json
{
  "success": true,
  "data": {
    "modules": [
      {
        "id": "uuid",
        "title": "Customer Service Basics",
        "description": "Learn fundamental customer service skills",
        "cover_image_url": "https://...",
        "duration_minutes": 120,
        "status": "published",
        "lessons_count": 8,
        "activities_count": 15,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 25,
      "pages": 2
    }
  }
}
```

#### `GET /api/training/modules/{id}`
Get detailed information about a specific training module.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "Customer Service Basics",
    "description": "Learn fundamental customer service skills",
    "cover_image_url": "https://...",
    "duration_minutes": 120,
    "status": "published",
    "learning_objectives": [
      "Understand customer service principles",
      "Handle difficult customers effectively"
    ],
    "lessons": [
      {
        "id": "uuid",
        "title": "Introduction to Customer Service",
        "description": "Overview of customer service",
        "order_index": 1,
        "duration_minutes": 15,
        "lesson_type": "video",
        "activities": [
          {
            "id": "uuid",
            "title": "Customer Service Quiz",
            "type": "quiz",
            "order_index": 1
          }
        ]
      }
    ]
  }
}
```

#### `POST /api/training/modules` (Admin only)
Create a new training module.

**Request Body:**
```json
{
  "title": "string (required)",
  "description": "string (optional)",
  "cover_image_url": "string (optional)",
  "duration_minutes": "number (optional)",
  "learning_objectives": "array (optional)",
  "prerequisites": "array (optional)"
}
```

#### `GET /api/training/progress`
Get current user's training progress.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "overall_progress": {
      "total_modules": 10,
      "completed_modules": 3,
      "in_progress_modules": 2,
      "completion_percentage": 30
    },
    "modules": [
      {
        "module_id": "uuid",
        "module_title": "Customer Service Basics",
        "completion_percentage": 75,
        "completed_activities": 12,
        "total_activities": 16,
        "last_activity_date": "2024-01-15T10:30:00Z",
        "status": "in_progress"
      }
    ]
  }
}
```

#### `POST /api/training/progress`
Update progress for a specific activity.

**Request Body:**
```json
{
  "activity_id": "uuid (required)",
  "status": "completed | in_progress (required)",
  "score": "number (0-100, optional)",
  "time_spent_seconds": "number (optional)"
}
```

### Job Board

#### `GET /api/jobs/postings`
Get list of job postings.

**Query Parameters:**
- `status`: Filter by status (published, draft, closed)
- `employment_type`: Filter by employment type
- `location_type`: Filter by location type
- `experience_level`: Filter by experience level
- `search`: Search by title or description
- `bpo_id`: Filter by BPO company
- `page`: Page number
- `limit`: Items per page

**Response:**
```json
{
  "success": true,
  "data": {
    "jobs": [
      {
        "id": "uuid",
        "title": "Customer Service Representative",
        "description": "Handle customer inquiries and support",
        "employment_type": "full_time",
        "location_type": "remote",
        "experience_level": "entry_level",
        "salary_range": {
          "min": 35000,
          "max": 45000,
          "currency": "USD"
        },
        "status": "published",
        "application_deadline": "2024-12-31T23:59:59Z",
        "bpo": {
          "id": "uuid",
          "name": "TechSupport Pro",
          "logo_url": "https://..."
        },
        "applications_count": 25,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "pages": 3
    }
  }
}
```

#### `GET /api/jobs/postings/{id}`
Get detailed information about a specific job posting.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "Customer Service Representative",
    "description": "Handle customer inquiries and support",
    "requirements": [
      "High school diploma or equivalent",
      "Excellent communication skills",
      "Computer literacy"
    ],
    "responsibilities": [
      "Answer customer calls and emails",
      "Resolve customer issues",
      "Maintain customer records"
    ],
    "employment_type": "full_time",
    "location_type": "remote",
    "experience_level": "entry_level",
    "salary_range": {
      "min": 35000,
      "max": 45000,
      "currency": "USD"
    },
    "required_training_modules": ["uuid1", "uuid2"],
    "bpo": {
      "id": "uuid",
      "name": "TechSupport Pro",
      "description": "Leading customer support company",
      "logo_url": "https://...",
      "website_url": "https://..."
    },
    "applications_count": 25,
    "user_has_applied": false,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### `POST /api/jobs/postings` (BPO Admin only)
Create a new job posting.

**Request Body:**
```json
{
  "title": "string (required)",
  "description": "string (required)",
  "requirements": "array (optional)",
  "responsibilities": "array (optional)",
  "employment_type": "full_time | part_time | contract (required)",
  "location_type": "remote | on_site | hybrid (required)",
  "experience_level": "entry_level | mid_level | senior_level (required)",
  "salary_range": {
    "min": "number (optional)",
    "max": "number (optional)",
    "currency": "string (optional)"
  },
  "application_deadline": "string (ISO date, optional)",
  "required_training_modules": "array (optional)"
}
```

#### `GET /api/jobs/applications`
Get job applications (for prospects: their applications, for BPO admins: applications to their jobs).

**Query Parameters:**
- `status`: Filter by application status
- `job_id`: Filter by specific job (BPO admins only)
- `page`: Page number
- `limit`: Items per page

**Response:**
```json
{
  "success": true,
  "data": {
    "applications": [
      {
        "id": "uuid",
        "status": "submitted",
        "cover_letter": "I am interested in this position...",
        "submitted_at": "2024-01-15T10:30:00Z",
        "job": {
          "id": "uuid",
          "title": "Customer Service Representative",
          "bpo_name": "TechSupport Pro"
        },
        "prospect": {
          "id": "uuid",
          "full_name": "Jane Smith",
          "email": "<EMAIL>"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 15,
      "pages": 1
    }
  }
}
```

#### `POST /api/jobs/applications`
Submit a job application.

**Request Body:**
```json
{
  "job_id": "uuid (required)",
  "cover_letter": "string (optional)",
  "additional_notes": "string (optional)",
  "expected_salary_min": "number (optional)",
  "expected_salary_max": "number (optional)",
  "availability_start_date": "string (ISO date, optional)"
}
```

### Admin Analytics

#### `GET /api/admin/analytics/overview` (Admin only)
Get platform overview analytics.

**Response:**
```json
{
  "success": true,
  "data": {
    "users": {
      "total": 1250,
      "active": 1100,
      "new_this_month": 85
    },
    "training": {
      "total_modules": 25,
      "total_completions": 3500,
      "average_completion_rate": 78.5
    },
    "jobs": {
      "active_postings": 45,
      "total_applications": 890,
      "hire_rate": 12.5
    },
    "bpos": {
      "total": 35,
      "active": 28,
      "new_this_month": 3
    }
  }
}
```

## 📝 Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data here
  },
  "message": "Optional success message"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      // Additional error details
    }
  }
}
```

## 🚨 Error Codes

| Code | Description |
|------|-------------|
| `UNAUTHORIZED` | Invalid or missing authentication |
| `FORBIDDEN` | Insufficient permissions |
| `NOT_FOUND` | Resource not found |
| `VALIDATION_ERROR` | Invalid request data |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `INTERNAL_ERROR` | Server error |

## 🔄 Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Authentication endpoints**: 5 requests per minute
- **General endpoints**: 100 requests per 15 minutes
- **Upload endpoints**: 10 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

---

**Next**: Explore the [Component Library](component-library.md) for UI component documentation.
