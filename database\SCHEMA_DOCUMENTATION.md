# BPO Training Platform - Database Schema Documentation

## 📊 **OVERVIEW**

This document provides comprehensive documentation for the BPO Training Platform database schema, including tables, relationships, indexes, and optimization strategies.

**Database Type**: PostgreSQL (via Supabase)  
**Schema Version**: 10.0  
**Last Updated**: 2025-06-06  

---

## 🗂️ **TABLE STRUCTURE**

### **Core User Tables**

#### **users**
Primary user authentication and profile table.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique user identifier |
| email | VARCHAR(255) | UNIQUE, NOT NULL | User email address |
| encrypted_password | VARCHAR(255) | NOT NULL | Encrypted password |
| full_name | VARCHAR(255) | NOT NULL | User's full name |
| role | user_role | NOT NULL | User role enum |
| status | user_status | DEFAULT 'pending_activation' | Account status |
| is_password_change_required | BOOLEAN | DEFAULT TRUE | Password change flag |
| last_login | TIMESTAMPTZ | | Last login timestamp |
| avatar_url | TEXT | | Profile avatar URL |
| is_placeholder | BOOLEAN | DEFAULT FALSE | Placeholder account flag |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

**Indexes**: email, role, status, role+status, created_at, updated_at  
**Foreign Keys**: None  
**Triggers**: update_timestamp()  

#### **prospects**
Extended profile information for job seekers.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique prospect identifier |
| user_id | UUID | UNIQUE, FK → users.id | Reference to user account |
| contact_info | JSONB | | Contact information object |
| education | JSONB | | Education history array |
| experience | JSONB | | Work experience array |
| skills | JSONB | | Skills and proficiency array |
| intro_video_url | TEXT | | Introduction video URL |
| resume_url | TEXT | | Resume document URL |
| profile_image | TEXT | | Profile image URL |
| profile_visibility | BOOLEAN | DEFAULT TRUE | Profile visibility flag |
| training_status | training_status | DEFAULT 'not_started' | Training progress status |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

**Indexes**: user_id, training_status, profile_visibility, training_status+visibility  
**Foreign Keys**: user_id → users.id  
**Triggers**: update_timestamp()  

#### **bpos**
Business Process Outsourcing company profiles.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique BPO identifier |
| name | VARCHAR(255) | NOT NULL | Company name |
| description | TEXT | | Company description |
| logo_url | TEXT | | Company logo URL |
| industry | VARCHAR(100) | | Industry sector |
| size_range | VARCHAR(50) | | Employee count range |
| founded_year | INTEGER | | Year founded |
| website_url | TEXT | | Company website |
| location | JSONB | | Location object |
| contact_email | VARCHAR(255) | | Contact email |
| phone | VARCHAR(50) | | Contact phone |
| created_by | UUID | FK → users.id | Creator user ID |
| reviews | JSONB | DEFAULT '[]' | Company reviews array |
| workplace_stats | JSONB | DEFAULT '{}' | Workplace statistics |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

**Indexes**: created_by, created_at  
**Foreign Keys**: created_by → users.id  
**Triggers**: update_timestamp()  

#### **bpo_teams**
Team membership and roles within BPO companies.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique membership identifier |
| bpo_id | UUID | FK → bpos.id | BPO company reference |
| user_id | UUID | FK → users.id | Team member reference |
| role | bpo_team_role | NOT NULL | Team role enum |
| permissions | JSONB | DEFAULT '{}' | Role permissions |
| invited_at | TIMESTAMPTZ | | Invitation timestamp |
| invited_by | UUID | FK → users.id | Inviter reference |
| accepted_at | TIMESTAMPTZ | | Acceptance timestamp |
| is_placeholder | BOOLEAN | DEFAULT FALSE | Placeholder flag |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

**Indexes**: bpo_id, user_id, role, bpo_id+user_id, bpo_id+role  
**Foreign Keys**: bpo_id → bpos.id, user_id → users.id, invited_by → users.id  
**Unique Constraints**: user_id+bpo_id  
**Triggers**: update_timestamp()  

---

### **Training System Tables**

#### **training_modules**
Training course modules.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique module identifier |
| title | VARCHAR(255) | NOT NULL | Module title |
| description | TEXT | | Module description |
| cover_image_url | TEXT | | Cover image URL |
| duration_minutes | INTEGER | CHECK > 0 | Estimated duration |
| required_order | INTEGER | | Sequence order |
| status | content_status | DEFAULT 'draft' | Publication status |
| requires_assessment | BOOLEAN | DEFAULT FALSE | Assessment requirement |
| created_by | UUID | FK → users.id | Creator reference |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

**Indexes**: status, created_by, required_order, status+required_order, created_at, updated_at  
**Foreign Keys**: created_by → users.id  
**Check Constraints**: duration_minutes > 0  
**Triggers**: update_timestamp()  

#### **lessons**
Individual lessons within training modules.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique lesson identifier |
| module_id | UUID | FK → training_modules.id | Parent module |
| title | VARCHAR(255) | NOT NULL | Lesson title |
| description | TEXT | | Lesson content |
| order_index | INTEGER | CHECK > 0 | Lesson sequence |
| duration_minutes | INTEGER | CHECK > 0 | Estimated duration |
| lesson_type | VARCHAR(50) | | Lesson type |
| video_url | TEXT | | Video content URL |
| media_url | TEXT | | Additional media URL |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

**Indexes**: module_id, order_index, lesson_type, module_id+order_index  
**Foreign Keys**: module_id → training_modules.id  
**Unique Constraints**: module_id+order_index  
**Check Constraints**: order_index > 0, duration_minutes > 0  
**Triggers**: update_timestamp()  

#### **activities**
Interactive activities within lessons.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique activity identifier |
| lesson_id | UUID | FK → lessons.id | Parent lesson |
| title | VARCHAR(255) | NOT NULL | Activity title |
| description | TEXT | | Activity description |
| type | activity_type | NOT NULL | Activity type enum |
| content | JSONB | DEFAULT '{}' | Activity content |
| content_url | TEXT | | External content URL |
| passing_criteria | JSONB | DEFAULT '{}' | Completion criteria |
| order_index | INTEGER | CHECK > 0 | Activity sequence |
| duration_minutes | INTEGER | | Estimated duration |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

**Indexes**: lesson_id, type, order_index, lesson_id+order_index  
**Foreign Keys**: lesson_id → lessons.id  
**Unique Constraints**: lesson_id+order_index  
**Check Constraints**: order_index > 0  
**Triggers**: update_timestamp()  

#### **progress_records**
User progress tracking for activities.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique progress identifier |
| prospect_id | UUID | FK → prospects.id | Learner reference |
| activity_id | UUID | FK → activities.id | Activity reference |
| status | progress_status | DEFAULT 'not_started' | Progress status |
| score | INTEGER | CHECK 0-100 | Achievement score |
| attempts | INTEGER | CHECK >= 0 | Number of attempts |
| completed_at | TIMESTAMPTZ | | Completion timestamp |
| time_spent_seconds | INTEGER | CHECK >= 0 | Time spent |
| is_module_assessment | BOOLEAN | DEFAULT FALSE | Assessment flag |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

**Indexes**: prospect_id, activity_id, status, prospect_id+status, prospect_id+activity_id  
**Foreign Keys**: prospect_id → prospects.id, activity_id → activities.id  
**Unique Constraints**: prospect_id+activity_id  
**Check Constraints**: score 0-100, attempts >= 0, time_spent_seconds >= 0  
**Triggers**: update_timestamp()  

---

### **Assessment System Tables**

#### **assessments**
Standalone assessment definitions.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique assessment identifier |
| title | VARCHAR(255) | NOT NULL | Assessment title |
| description | TEXT | | Assessment description |
| category | VARCHAR(100) | NOT NULL | Assessment category |
| duration | VARCHAR(50) | | Time limit |
| instructions | TEXT | | Instructions text |
| total_questions | INTEGER | CHECK > 0 | Question count |
| passing_score | INTEGER | CHECK 0-100 | Minimum passing score |
| what_it_checks | TEXT | | Assessment purpose |
| what_to_expect | TEXT | | Expectation description |
| is_active | BOOLEAN | DEFAULT TRUE | Active status |
| completions | INTEGER | DEFAULT 0 | Completion count |
| avg_score | DECIMAL | DEFAULT 0 | Average score |
| order_index | INTEGER | | Display order |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

**Indexes**: category, is_active, order_index, is_active+category  
**Check Constraints**: total_questions > 0, passing_score 0-100  
**Triggers**: update_timestamp()  

---

## 🔗 **RELATIONSHIPS**

### **Primary Relationships**
- users → prospects (1:1)
- users → bpo_teams (1:N)
- bpos → bpo_teams (1:N)
- training_modules → lessons (1:N)
- lessons → activities (1:N)
- prospects → progress_records (1:N)
- activities → progress_records (1:N)

### **Secondary Relationships**
- bpos → job_postings (1:N)
- job_postings → applications (1:N)
- applications → interviews (1:N)
- prospects → certificates (1:N)
- users → ai_conversations (1:N)

---

## 📈 **PERFORMANCE OPTIMIZATIONS**

### **Indexes**
- **Single Column**: All foreign keys, status fields, timestamps
- **Composite**: Multi-column queries (role+status, module+order)
- **Partial**: Filtered indexes for common queries (active users, published content)

### **Materialized Views**
- `mv_prospect_training_progress`: Aggregated training progress
- Refresh function: `refresh_training_progress_cache()`

### **Optimized Functions**
- `get_prospect_training_progress(UUID)`: Efficient progress queries
- `get_bpo_team_members(UUID)`: Team member lookup
- `get_job_applications_with_candidates(UUID)`: Application details

---

## 🔒 **SECURITY**

### **Row Level Security (RLS)**
All tables have RLS enabled with policies for:
- Platform admins (full access)
- Users (own data access)
- BPO admins (BPO-scoped access)
- Public (limited read access)

### **Security Functions**
- `is_platform_admin(UUID)`: Admin check
- `is_bpo_admin(UUID, UUID)`: BPO admin check
- `get_user_bpo_memberships(UUID)`: User BPO access

---

## 🛠️ **MAINTENANCE**

### **Regular Tasks**
- Refresh materialized views daily
- Analyze table statistics weekly
- Monitor slow queries
- Update indexes based on query patterns

### **Monitoring Views**
- `v_slow_queries`: Performance monitoring
- `analyze_table_stats()`: Table size analysis
