# Database Structure

This folder contains all database-related files for the BPO Training Platform.

## 🗂️ **CONSOLIDATED STRUCTURE**

Previously scattered across 5+ folders (`sql/`, `migrations/`, `scripts/`, `supabase/`, `db/`), all database files are now organized in one place:

### `/migrations/`
Contains all database migration files in chronological order:
- `01_initial_schema.sql` - Complete database schema
- `02_validation_triggers.sql` - Validation functions
- `03_fix_bpo_teams_rls.sql` - RLS security fixes

### `/functions/`
Contains database functions and stored procedures:
- `security_functions.sql` - Authentication helper functions
- Business logic functions
- Utility functions

### `/policies/`
Contains Row Level Security (RLS) policies:
- `rls_policies.sql` - Complete RLS policy definitions
- Authentication policies
- Authorization rules

### `/scripts/`
Contains setup and utility scripts:
- `apply-migrations.js` - Automated migration runner
- `create-storage-buckets.js` - Storage bucket setup
- `setup-database.sql` - Complete database initialization

## 🚀 **USAGE**

### Quick Setup
```bash
# Run all migrations automatically
npm run migrate

# Set up storage buckets
npm run setup:storage

# Complete database setup
npm run db:setup
```

### Manual Setup
1. Execute files in `/migrations/` directory in order
2. Apply functions from `/functions/` directory
3. Set up policies from `/policies/` directory

### Supabase CLI
```bash
supabase db push
```

## ⚠️ **IMPORTANT NOTES**

- **BACKUP FIRST**: Always backup your database before running migrations
- **TEST ENVIRONMENT**: Test migrations in development environment first
- **SEQUENTIAL ORDER**: Follow the numbered migration sequence
- **DOCUMENTATION**: Document any manual steps required
- **OLD FOLDERS**: The old `sql/`, `migrations/`, `scripts/`, `supabase/` folders can be safely removed

## 🔧 **MIGRATION FROM OLD STRUCTURE**

If you have references to old paths, update them:
- `scripts/apply-migrations.js` → `database/scripts/apply-migrations.js`
- `sql/comprehensive_rls_fix.sql` → `database/functions/security_functions.sql`
- `migrations/*.sql` → `database/migrations/*.sql`
