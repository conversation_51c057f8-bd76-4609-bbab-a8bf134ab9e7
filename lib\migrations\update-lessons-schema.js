/**
 * JavaScript migration helper to ensure lessons table has the required columns
 * This can be imported and called from API routes to ensure schema compatibility
 */

import { supabase } from '@/lib/supabase';

/**
 * Checks if the lessons table has the required columns for new lesson types
 * and adds them if they're missing
 */
export async function ensureLessonsTableSchema() {
  try {
    console.log('Checking lessons table schema...');
    
    // First check if our helper function exists
    const { data: functionExists, error: functionError } = await supabase.rpc(
      'pg_get_columns',
      { table_name: 'lessons' }
    );
    
    // If the function doesn't exist, we need to manually check for columns
    if (functionError) {
      console.log('Helper function not available, checking columns manually...');
      
      // Add lesson_type column if it doesn't exist
      await supabase.rpc('execute_sql', {
        sql_query: `
          DO $$
          BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                          WHERE table_name = 'lessons' AND column_name = 'lesson_type') THEN
                ALTER TABLE lessons ADD COLUMN lesson_type TEXT DEFAULT 'video';
                RAISE NOTICE 'Added lesson_type column to lessons table';
            END IF;
          END $$;
        `
      });
      
      // Add video_url column if it doesn't exist
      await supabase.rpc('execute_sql', {
        sql_query: `
          DO $$
          BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                          WHERE table_name = 'lessons' AND column_name = 'video_url') THEN
                ALTER TABLE lessons ADD COLUMN video_url TEXT DEFAULT NULL;
                RAISE NOTICE 'Added video_url column to lessons table';
            END IF;
          END $$;
        `
      });
      
      // Add media_url column if it doesn't exist
      await supabase.rpc('execute_sql', {
        sql_query: `
          DO $$
          BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                          WHERE table_name = 'lessons' AND column_name = 'media_url') THEN
                ALTER TABLE lessons ADD COLUMN media_url TEXT DEFAULT NULL;
                RAISE NOTICE 'Added media_url column to lessons table';
            END IF;
          END $$;
        `
      });
      
      console.log('Manual schema migration completed');
      
    } else {
      // Check if all required columns exist
      const columns = functionExists;
      const columnNames = columns.map(col => col.column_name);
      
      const missingColumns = [];
      if (!columnNames.includes('lesson_type')) missingColumns.push('lesson_type');
      if (!columnNames.includes('video_url')) missingColumns.push('video_url');
      if (!columnNames.includes('media_url')) missingColumns.push('media_url');
      
      if (missingColumns.length > 0) {
        console.log(`Missing columns detected: ${missingColumns.join(', ')}`);
        
        // Execute the migration SQL
        const { error } = await supabase.rpc('execute_sql', {
          sql_query: `
            ALTER TABLE lessons 
            ${!columnNames.includes('lesson_type') ? "ADD COLUMN IF NOT EXISTS lesson_type TEXT DEFAULT 'video'," : ""}
            ${!columnNames.includes('video_url') ? "ADD COLUMN IF NOT EXISTS video_url TEXT DEFAULT NULL," : ""}
            ${!columnNames.includes('media_url') ? "ADD COLUMN IF NOT EXISTS media_url TEXT DEFAULT NULL" : ""}
          `.replace(/,\s*$/, '') // Remove trailing comma if present
        });
        
        if (error) {
          console.error('Error applying migration:', error);
          throw error;
        }
        
        console.log('Schema migration completed');
      } else {
        console.log('All required columns already exist');
      }
    }
    
    return { success: true };
  } catch (error) {
    console.error('Migration error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error during migration' 
    };
  }
} 