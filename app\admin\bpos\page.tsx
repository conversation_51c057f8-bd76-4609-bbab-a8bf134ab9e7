'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { 
  Sheet, 
  SheetContent, 
  SheetHeader, 
  SheetTitle, 
  SheetDescription, 
  SheetFooter 
} from '@/components/ui/sheet';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Building2, 
  Plus, 
  Search, 
  Users, 
  Mail, 
  Briefcase,
  ArrowUpDown,
  MoreHorizontal,
  Calendar,
  Edit,
  Trash
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Toaster } from '@/components/ui/toaster';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from '@/components/ui/badge';

// Types
type BPO = {
  id: string;
  name: string;
  description: string | null;
  industry: string | null;
  created_at: string;
  contact_email: string | null;
  admin?: string;
  adminId?: string;
};

type User = {
  id: string;
  email: string;
  full_name: string;
  role: string;
};

export default function BPOManagement() {
  const { toast } = useToast();
  const [bpos, setBPOs] = useState<BPO[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentBPO, setCurrentBPO] = useState<BPO | null>(null);
  
  const [formData, setFormData] = useState({
    id: '',
    name: '',
    description: '',
    industry: '',
    contact_email: '',
    admin_user_id: '',
  });

  // Fetch BPOs and users on component mount
  useEffect(() => {
    fetchBPOs();
    fetchUsers();
  }, []);

  // Fetch BPOs with admin information
  async function fetchBPOs() {
    try {
      setLoading(true);
      
      // Fetch BPOs
      const { data: bposData, error: bposError } = await supabase
        .from('bpos')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (bposError) throw bposError;
      
      // For each BPO, fetch its admin
      const bposWithAdmins = await Promise.all((bposData || []).map(async (bpo) => {
        const { data: teamData, error: teamError } = await supabase
          .from('bpo_teams')
          .select('user_id')
          .eq('bpo_id', bpo.id)
          .eq('role', 'admin')
          .limit(1);
        
        if (teamError) throw teamError;
        
        let adminName = "No admin assigned";
        let adminId = null;
        
        if (teamData && teamData.length > 0) {
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('full_name')
            .eq('id', teamData[0].user_id)
            .single();
          
          if (!userError && userData) {
            adminName = userData.full_name;
            adminId = teamData[0].user_id;
          }
        }
        
        return {
          ...bpo,
          admin: adminName,
          adminId: adminId
        };
      }));
      
      setBPOs(bposWithAdmins);
    } catch (error: any) {
      console.error('Error fetching BPOs:', error);
      toast({
        title: "Error",
        description: "Failed to fetch BPO companies",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }

  // Fetch all users who could be BPO admins
  async function fetchUsers() {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, email, full_name, role')
        .eq('role', 'bpo_admin')
        .order('full_name');
      
      if (error) throw error;
      
      setUsers(data || []);
    } catch (error: any) {
      console.error('Error fetching users:', error);
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      });
    }
  }

  // Handle opening the form sheet for creating a new BPO
  const handleAddNew = () => {
    setFormData({
      id: '',
      name: '',
      description: '',
      industry: '',
      contact_email: '',
      admin_user_id: '',
    });
    setIsEditing(false);
    setCurrentBPO(null);
    setIsSheetOpen(true);
  };

  // Handle opening the form sheet for editing an existing BPO
  const handleEdit = (bpo: BPO) => {
    setFormData({
      id: bpo.id,
      name: bpo.name,
      description: bpo.description || '',
      industry: bpo.industry || '',
      contact_email: bpo.contact_email || '',
      admin_user_id: bpo.adminId || '',
    });
    setIsEditing(true);
    setCurrentBPO(bpo);
    setIsSheetOpen(true);
  };

  // Handle form submission (create or update)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name) {
      toast({
        title: "Validation Error",
        description: "BPO name is required",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      
      // Get current user's ID
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('You must be logged in to manage BPOs');
      }
      
      if (isEditing) {
        // Update existing BPO
        const { error: bpoError } = await supabase
          .from('bpos')
          .update({
            name: formData.name,
            description: formData.description || null,
            industry: formData.industry || null,
            contact_email: formData.contact_email || null,
          })
          .eq('id', formData.id);
        
        if (bpoError) throw bpoError;
        
        // Update BPO admin if changed
        if (formData.admin_user_id && currentBPO?.adminId !== formData.admin_user_id) {
          // First check if there's an existing admin
          const { data: existingAdmin, error: checkError } = await supabase
            .from('bpo_teams')
            .select('user_id')
            .eq('bpo_id', formData.id)
            .eq('role', 'admin');
          
          if (checkError) throw checkError;
          
          if (existingAdmin && existingAdmin.length > 0) {
            // Update existing admin record
            const { error: updateError } = await supabase
              .from('bpo_teams')
              .update({ user_id: formData.admin_user_id })
              .eq('bpo_id', formData.id)
              .eq('role', 'admin');
            
            if (updateError) throw updateError;
          } else {
            // Create new admin record
            const { error: createError } = await supabase
              .from('bpo_teams')
              .insert([
                {
                  bpo_id: formData.id,
                  user_id: formData.admin_user_id,
                  role: 'admin',
                  permissions: {},
                  invited_at: new Date().toISOString(),
                  accepted_at: new Date().toISOString(),
                },
              ]);
            
            if (createError) throw createError;
          }
        }
        
        toast({
          title: "Success",
          description: `${formData.name} updated successfully`,
        });
      } else {
        // Create new BPO
        const { data: bpoData, error: bpoError } = await supabase
          .from('bpos')
          .insert([
            {
              name: formData.name,
              description: formData.description || null,
              industry: formData.industry || null,
              contact_email: formData.contact_email || null,
              created_by: session.user.id,
              location: {},
              workplace_stats: {},
              reviews: [],
            },
          ])
          .select()
          .single();
        
        if (bpoError) throw bpoError;
        
        // Create BPO admin if specified
        if (formData.admin_user_id) {
          const { error: teamError } = await supabase
            .from('bpo_teams')
            .insert([
              {
                bpo_id: bpoData.id,
                user_id: formData.admin_user_id,
                role: 'admin',
                permissions: {},
                invited_at: new Date().toISOString(),
                accepted_at: new Date().toISOString(),
              },
            ]);
          
          if (teamError) throw teamError;
        }
        
        toast({
          title: "Success",
          description: `${formData.name} created successfully`,
        });
      }
      
      // Close sheet and refresh data
      setIsSheetOpen(false);
      await fetchBPOs();
    } catch (error: any) {
      console.error('Error saving BPO:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to save BPO",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle BPO deletion
  const handleDelete = async (bpoId: string, bpoName: string) => {
    if (!confirm(`Are you sure you want to delete ${bpoName}? This action cannot be undone.`)) {
      return;
    }
    
    try {
      setLoading(true);
      
      // First delete related team members
      const { error: teamError } = await supabase
        .from('bpo_teams')
        .delete()
        .eq('bpo_id', bpoId);
      
      if (teamError) throw teamError;
      
      // Then delete the BPO
      const { error: bpoError } = await supabase
        .from('bpos')
        .delete()
        .eq('id', bpoId);
      
      if (bpoError) throw bpoError;
      
      toast({
        title: "Success",
        description: `${bpoName} deleted successfully`,
      });
      
      // Refresh the BPO list
      await fetchBPOs();
    } catch (error: any) {
      console.error('Error deleting BPO:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete BPO",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter BPOs based on search query
  const filteredBPOs = bpos.filter(bpo => 
    bpo.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (bpo.industry && bpo.industry.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (bpo.contact_email && bpo.contact_email.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (bpo.admin && bpo.admin.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">BPO Companies</h1>
        <Button onClick={handleAddNew} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          <span>Add New BPO</span>
        </Button>
      </div>
      
      <div className="rounded-lg border shadow-sm">
        <div className="p-4 flex items-center justify-between border-b">
          <div className="flex items-center gap-2 w-72">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search BPOs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-9"
            />
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-muted text-muted-foreground">
              {filteredBPOs.length} Companies
            </Badge>
          </div>
        </div>
        
        <div className="relative overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[300px]">
                  <div className="flex items-center gap-1">
                    <span>Company Name</span>
                    <ArrowUpDown className="h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead>Industry</TableHead>
                <TableHead>Contact Email</TableHead>
                <TableHead>Admin</TableHead>
                <TableHead>
                  <div className="flex items-center gap-1">
                    <span>Created On</span>
                    <ArrowUpDown className="h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center h-24 text-muted-foreground">
                    Loading BPO companies...
                  </TableCell>
                </TableRow>
              ) : filteredBPOs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center h-24 text-muted-foreground">
                    {searchQuery ? 'No BPOs matching your search' : 'No BPO companies found'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredBPOs.map((bpo) => (
                  <TableRow key={bpo.id} className="cursor-pointer hover:bg-muted/50" onClick={() => handleEdit(bpo)}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <div className="h-8 w-8 rounded-md bg-primary/10 flex items-center justify-center text-primary">
                          <Building2 className="h-4 w-4" />
                        </div>
                        <span>{bpo.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {bpo.industry ? (
                        <div className="flex items-center gap-2">
                          <Briefcase className="h-4 w-4 text-muted-foreground" />
                          <span>{bpo.industry}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Not specified</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {bpo.contact_email ? (
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span>{bpo.contact_email}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Not specified</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>{bpo.admin}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>{formatDate(bpo.created_at)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(bpo);
                          }}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(bpo.id, bpo.name);
                            }}
                          >
                            <Trash className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
      
      {/* Slide-out Form Sheet */}
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetContent className="sm:max-w-md md:max-w-lg">
          <SheetHeader>
            <SheetTitle>{isEditing ? 'Edit BPO' : 'Add New BPO'}</SheetTitle>
            <SheetDescription>
              {isEditing 
                ? 'Update the details for this BPO company.' 
                : 'Fill in the details to create a new BPO company.'}
            </SheetDescription>
          </SheetHeader>
          
          <form onSubmit={handleSubmit} className="space-y-6 py-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="name" className="text-sm font-medium">
                  BPO Name
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter BPO company name"
                  className="mt-1.5"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="industry" className="text-sm font-medium">
                  Industry
                </Label>
                <Input
                  id="industry"
                  value={formData.industry}
                  onChange={(e) => setFormData({ ...formData, industry: e.target.value })}
                  placeholder="E.g., Customer Service, IT Support"
                  className="mt-1.5"
                />
              </div>
              
              <div>
                <Label htmlFor="contact_email" className="text-sm font-medium">
                  Contact Email
                </Label>
                <Input
                  id="contact_email"
                  type="email"
                  value={formData.contact_email}
                  onChange={(e) => setFormData({ ...formData, contact_email: e.target.value })}
                  placeholder="<EMAIL>"
                  className="mt-1.5"
                />
              </div>
              
              <div>
                <Label htmlFor="admin_user" className="text-sm font-medium">
                  BPO Admin
                </Label>
                <Select
                  value={formData.admin_user_id}
                  onValueChange={(value) => setFormData({ ...formData, admin_user_id: value })}
                >
                  <SelectTrigger id="admin_user" className="mt-1.5">
                    <SelectValue placeholder="Select BPO Admin" />
                  </SelectTrigger>
                  <SelectContent>
                    {users.map(user => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.full_name} ({user.email})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="description" className="text-sm font-medium">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Brief description of the BPO company"
                  className="mt-1.5"
                  rows={4}
                />
              </div>
            </div>
            
            <SheetFooter>
              <Button type="submit" disabled={loading}>
                {loading 
                  ? (isEditing ? 'Updating...' : 'Creating...') 
                  : (isEditing ? 'Update BPO' : 'Create BPO')}
              </Button>
            </SheetFooter>
          </form>
        </SheetContent>
      </Sheet>
      
      <Toaster />
    </div>
  );
} 