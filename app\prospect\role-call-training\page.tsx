'use client'

import React from 'react'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Phone,
  PhoneCall,
  Headphones,
  MessageSquare,
  Users,
  ArrowRight,
  Star,
  Clock,
  Target
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface CallType {
  id: string
  title: string
  description: string
  icon: React.ElementType
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced'
  duration: string
  scenarios: number
  colorScheme: {
    gradient: string
    button: string
    badge: string
    border: string
  }
}

export default function RoleCallTrainingPage() {
  const callTypes: CallType[] = [
    {
      id: 'cold-call',
      title: 'Cold Call for New Business',
      description: 'Learn to make effective cold calls to potential clients and introduce your business services.',
      icon: Phone,
      difficulty: 'Beginner',
      duration: '15-20 min',
      scenarios: 5,
      colorScheme: {
        gradient: "from-blue-500/10 to-indigo-500/10 dark:from-blue-500/5 dark:to-indigo-500/5",
        button: "bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700",
        badge: "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
        border: "border-blue-200 dark:border-blue-800"
      }
    },
    {
      id: 'follow-up',
      title: 'Follow-up Calls',
      description: 'Master the art of following up with prospects and maintaining professional relationships.',
      icon: PhoneCall,
      difficulty: 'Intermediate',
      duration: '10-15 min',
      scenarios: 4,
      colorScheme: {
        gradient: "from-green-500/10 to-emerald-500/10 dark:from-green-500/5 dark:to-emerald-500/5",
        button: "bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700",
        badge: "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300",
        border: "border-green-200 dark:border-green-800"
      }
    },
    {
      id: 'customer-service',
      title: 'Customer Service Calls',
      description: 'Handle customer inquiries, complaints, and support requests with professionalism.',
      icon: Headphones,
      difficulty: 'Intermediate',
      duration: '20-25 min',
      scenarios: 6,
      colorScheme: {
        gradient: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/5 dark:to-pink-500/5",
        button: "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700",
        badge: "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300",
        border: "border-purple-200 dark:border-purple-800"
      }
    },
    {
      id: 'sales-closing',
      title: 'Sales Closing Calls',
      description: 'Learn advanced techniques for closing deals and handling objections effectively.',
      icon: Target,
      difficulty: 'Advanced',
      duration: '25-30 min',
      scenarios: 7,
      colorScheme: {
        gradient: "from-orange-500/10 to-red-500/10 dark:from-orange-500/5 dark:to-red-500/5",
        button: "bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700",
        badge: "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300",
        border: "border-orange-200 dark:border-orange-800"
      }
    },
    {
      id: 'team-communication',
      title: 'Team Communication',
      description: 'Practice internal communication, team meetings, and collaborative discussions.',
      icon: Users,
      difficulty: 'Beginner',
      duration: '10-15 min',
      scenarios: 3,
      colorScheme: {
        gradient: "from-sky-500/10 to-cyan-500/10 dark:from-sky-500/5 dark:to-cyan-500/5",
        button: "bg-gradient-to-r from-sky-600 to-cyan-600 hover:from-sky-700 hover:to-cyan-700",
        badge: "bg-sky-100 text-sky-700 dark:bg-sky-900/30 dark:text-sky-300",
        border: "border-sky-200 dark:border-sky-800"
      }
    },
    {
      id: 'complaint-handling',
      title: 'Complaint Handling',
      description: 'Develop skills to handle difficult customers and resolve complaints professionally.',
      icon: MessageSquare,
      difficulty: 'Advanced',
      duration: '20-25 min',
      scenarios: 5,
      colorScheme: {
        gradient: "from-rose-500/10 to-pink-500/10 dark:from-rose-500/5 dark:to-pink-500/5",
        button: "bg-gradient-to-r from-rose-600 to-pink-600 hover:from-rose-700 hover:to-pink-700",
        badge: "bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300",
        border: "border-rose-200 dark:border-rose-800"
      }
    }
  ]

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
      case 'Advanced':
        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300'
    }
  }

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight mb-4">Role-Call Training</h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Practice your communication skills with AI-powered call simulations. Choose from different call types to improve your professional phone skills.
          </p>
        </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Phone className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-2xl font-bold">6</p>
                <p className="text-sm text-muted-foreground">Call Types</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Star className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-2xl font-bold">30</p>
                <p className="text-sm text-muted-foreground">Scenarios</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <Clock className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-2xl font-bold">2-4h</p>
                <p className="text-sm text-muted-foreground">Total Training</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Call Types Grid */}
      <div>
        <h2 className="text-2xl font-semibold mb-6">Choose Your Training Type</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {callTypes.map((callType) => {
            const IconComponent = callType.icon
            return (
              <Card
                key={callType.id}
                className={cn(
                  "group hover:shadow-lg transition-all duration-200 cursor-pointer border-2",
                  callType.colorScheme.border
                )}
              >
                {/* Header with gradient background */}
                <div className={cn(
                  "relative bg-gradient-to-r p-6",
                  callType.colorScheme.gradient
                )}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-white dark:bg-gray-900 rounded-lg shadow-sm">
                      <IconComponent className="h-6 w-6 text-gray-700 dark:text-gray-300" />
                    </div>
                    <Badge className={cn("text-xs", getDifficultyColor(callType.difficulty))}>
                      {callType.difficulty}
                    </Badge>
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{callType.title}</h3>
                </div>

                <CardContent className="p-6">
                  <p className="text-muted-foreground mb-4 line-clamp-3">
                    {callType.description}
                  </p>

                  <div className="flex items-center justify-between text-sm text-muted-foreground mb-6">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{callType.duration}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Target className="h-4 w-4" />
                      <span>{callType.scenarios} scenarios</span>
                    </div>
                  </div>

                  <Button
                    className={cn(
                      "w-full text-white font-medium group-hover:scale-105 transition-transform",
                      callType.colorScheme.button
                    )}
                    asChild
                  >
                    <Link href={`/prospect/role-call-training/${callType.id}`}>
                      Start Training
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {/* Getting Started Tips */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200 dark:border-blue-800">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <MessageSquare className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Getting Started Tips</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Start with beginner-level call types to build confidence</li>
                <li>• Practice in a quiet environment with good audio quality</li>
                <li>• Review the script before starting each simulation</li>
                <li>• Take notes on areas for improvement after each session</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}
