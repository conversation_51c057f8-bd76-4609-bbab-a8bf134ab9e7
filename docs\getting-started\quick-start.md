# ⚡ Quick Start Guide

Get the BPO Training Platform up and running in 5 minutes! This guide assumes you have already completed the [Installation](installation.md) process.

## 🎯 What You'll Accomplish

By the end of this guide, you'll have:
- ✅ A running BPO Training Platform
- ✅ Test user accounts set up
- ✅ Sample training modules loaded
- ✅ Understanding of the main features

## 🚀 5-Minute Setup

### Step 1: Verify Installation (30 seconds)

```bash
# Ensure you're in the project directory
cd bpo-training-platform

# Check if dependencies are installed
npm list --depth=0

# Verify environment variables
cat .env.local | grep SUPABASE
```

### Step 2: Start the Development Server (30 seconds)

```bash
# Start the application
npm run dev

# Wait for this message:
# ✓ Ready in 2.1s
# ○ Local:        http://localhost:3000
```

### Step 3: Access the Application (1 minute)

1. **Open your browser** and navigate to [http://localhost:3000](http://localhost:3000)
2. **Verify the homepage loads** - You should see the BPO Training Platform landing page
3. **Check the navigation** - Ensure all menu items are visible

### Step 4: Test User Authentication (2 minutes)

#### Login as Platform Admin
```
Email: <EMAIL>
Password: admin123
```

**What you'll see:**
- Admin dashboard with platform overview
- User management capabilities
- Training module management
- System analytics

#### Login as BPO Company Admin
```
Email: <EMAIL>
Password: bpo123
```

**What you'll see:**
- BPO dashboard with company metrics
- Job posting management
- Candidate applications
- Team member management

#### Login as Prospect (Job Seeker)
```
Email: <EMAIL>
Password: prospect123
```

**What you'll see:**
- Training modules and progress tracking
- Job board with available positions
- Profile management
- Application history

### Step 5: Explore Key Features (1.5 minutes)

#### Training System
1. **Navigate to Training** (as prospect)
2. **Start a training module** - Click on any available module
3. **Complete a lesson** - Go through the interactive content
4. **Check progress** - View your completion percentage

#### Job Board
1. **Browse job postings** - View available BPO positions
2. **Apply for a job** - Submit an application
3. **Track applications** - Check application status

#### Admin Features
1. **User management** - View and manage users (as admin)
2. **Content management** - Add/edit training modules
3. **Analytics** - View platform usage statistics

## 🎮 Interactive Demo

### Create Your First Training Module (Admin)

1. **Login as admin** using the credentials above
2. **Navigate to Admin → Training Modules**
3. **Click "Create New Module"**
4. **Fill in the details:**
   ```
   Title: Customer Service Basics
   Description: Learn fundamental customer service skills
   Duration: 30 minutes
   ```
5. **Add lessons and activities**
6. **Publish the module**

### Complete Your First Training (Prospect)

1. **Login as prospect**
2. **Go to Training section**
3. **Select "Customer Service Basics"**
4. **Complete the first lesson**
5. **Take the quiz**
6. **View your progress**

### Post Your First Job (BPO Admin)

1. **Login as BPO admin**
2. **Navigate to Job Postings**
3. **Click "Create New Job"**
4. **Fill in job details:**
   ```
   Title: Customer Service Representative
   Type: Full-time
   Location: Remote
   Salary: $35,000 - $45,000
   ```
5. **Publish the job posting**

## 🔍 Verification Checklist

After completing the quick start, verify these features work:

### ✅ Authentication & Authorization
- [ ] Can login with different user roles
- [ ] Role-based access control works
- [ ] Logout functionality works
- [ ] Password reset works (optional)

### ✅ Training System
- [ ] Training modules display correctly
- [ ] Can start and complete lessons
- [ ] Progress tracking updates
- [ ] Quizzes and assessments work

### ✅ Job Board
- [ ] Job postings display
- [ ] Can apply for jobs
- [ ] Application tracking works
- [ ] Search and filtering work

### ✅ Admin Features
- [ ] User management interface
- [ ] Content management tools
- [ ] Analytics dashboard
- [ ] System settings

### ✅ Performance & UI
- [ ] Pages load quickly (< 3 seconds)
- [ ] Navigation is smooth
- [ ] Mobile responsive design
- [ ] No console errors

## 🐛 Common Quick Start Issues

### Issue: "Cannot connect to database"
**Solution:**
```bash
# Check your environment variables
echo $NEXT_PUBLIC_SUPABASE_URL
echo $NEXT_PUBLIC_SUPABASE_ANON_KEY

# Verify Supabase project is active
# Go to supabase.com and check project status
```

### Issue: "Authentication failed"
**Solution:**
1. Verify test users exist in Supabase Auth
2. Check if email confirmation is required
3. Ensure RLS policies are correctly set up

### Issue: "Training modules not loading"
**Solution:**
```bash
# Run database seeding
npm run db:seed

# Or manually add sample data through admin interface
```

### Issue: "Page not found (404)"
**Solution:**
1. Ensure development server is running
2. Check if you're using the correct URL
3. Verify Next.js routing is working

## 🎯 Next Steps

Now that you have the platform running, explore these areas:

### 🏗️ **Development**
- **[Architecture Overview](../development/architecture.md)** - Understand the system design
- **[Component Library](../development/component-library.md)** - Explore reusable UI components
- **[API Reference](../development/api-reference.md)** - Learn about available endpoints

### 🔐 **Security & Performance**
- **[Authentication Guide](../guides/authentication.md)** - Deep dive into auth system
- **[Performance Optimization](../guides/performance-optimization.md)** - Optimize your setup
- **[Security Best Practices](../guides/security-best-practices.md)** - Secure your application

### 🚀 **Deployment**
- **[Production Setup](../deployment/production-setup.md)** - Deploy to production
- **[Environment Variables](../deployment/environment-variables.md)** - Configure for different environments

### 🤝 **Contributing**
- **[Development Workflow](../contributing/development-workflow.md)** - How to contribute
- **[Code Style Guide](../contributing/code-style.md)** - Follow coding standards

## 💡 Pro Tips

### Development Workflow
```bash
# Use these commands for efficient development
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # Check code quality
npm run type-check   # Verify TypeScript
npm run test         # Run tests
```

### Browser DevTools
- **Open DevTools** (F12) to monitor network requests
- **Check Console** for any JavaScript errors
- **Use React DevTools** extension for component debugging
- **Monitor Performance** tab for optimization opportunities

### Database Management
- **Use Supabase Dashboard** for real-time data viewing
- **SQL Editor** for running custom queries
- **Table Editor** for quick data modifications
- **Auth Users** section for user management

## 🆘 Getting Help

If you encounter issues during quick start:

1. **Check the logs** in your terminal for error messages
2. **Review the [Troubleshooting Guide](../deployment/troubleshooting.md)**
3. **Search existing issues** on GitHub
4. **Ask for help** in GitHub Discussions
5. **Join our community** Discord server (if available)

## 🎉 Congratulations!

You've successfully set up the BPO Training Platform! You now have:

- ✅ A fully functional training and recruitment platform
- ✅ Multi-role authentication system
- ✅ Interactive training modules
- ✅ Job board functionality
- ✅ Admin management tools

**Ready to dive deeper?** Check out the [Architecture Overview](../development/architecture.md) to understand how everything works together.

---

**Happy coding!** 🚀
