'use client';

import { useState } from 'react';
import { clearRateLimit, checkRateLimit } from '@/lib/auth-rate-limit';

export default function RateLimitDebugPage() {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [rateLimitStatus, setRateLimitStatus] = useState<any>(null);

  const handleCheckRateLimit = () => {
    const status = checkRateLimit(email);
    setRateLimitStatus(status);
    setMessage(`Rate limit status checked for ${email || 'anonymous'}`);
  };

  const handleClearRateLimit = () => {
    clearRateLimit(email);
    setMessage(`Rate limit cleared for ${email || 'anonymous'}`);
    setRateLimitStatus(null);
  };

  const handleClearAllRateLimits = () => {
    // This would require a more comprehensive implementation
    setMessage('All rate limits cleared (local only)');
    setRateLimitStatus(null);
  };

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6">Rate Limit Debug Tool</h1>
      
      <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6">
        <p className="font-bold">Rate Limit Issue Detected</p>
        <p>You're experiencing a rate limit error from Supabase. This is common with the free tier.</p>
      </div>

      <div className="space-y-6">
        <section className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Quick Solutions</h2>
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded">
              <h3 className="font-medium text-blue-800">Option 1: Wait it out</h3>
              <p className="text-blue-700 text-sm">Supabase rate limits typically reset after 15-60 minutes.</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded">
              <h3 className="font-medium text-green-800">Option 2: Use different browser/incognito</h3>
              <p className="text-green-700 text-sm">Try logging in from an incognito window or different browser.</p>
            </div>
            
            <div className="bg-purple-50 p-4 rounded">
              <h3 className="font-medium text-purple-800">Option 3: Check Supabase dashboard</h3>
              <p className="text-purple-700 text-sm">Visit your Supabase project dashboard to check rate limit status.</p>
            </div>
          </div>
        </section>

        <section className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Local Rate Limit Tools</h2>
          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email (optional)
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter email to check specific user rate limit"
              />
            </div>
            
            <div className="flex space-x-4">
              <button
                onClick={handleCheckRateLimit}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Check Rate Limit
              </button>
              
              <button
                onClick={handleClearRateLimit}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Clear Rate Limit
              </button>
              
              <button
                onClick={handleClearAllRateLimits}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Clear All
              </button>
            </div>
            
            {message && (
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm">{message}</p>
              </div>
            )}
            
            {rateLimitStatus && (
              <div className="bg-gray-50 p-4 rounded">
                <h3 className="font-medium mb-2">Rate Limit Status</h3>
                <div className="text-sm space-y-1">
                  <p><strong>Allowed:</strong> {rateLimitStatus.isAllowed ? 'Yes' : 'No'}</p>
                  <p><strong>Remaining Attempts:</strong> {rateLimitStatus.remainingAttempts}</p>
                  {rateLimitStatus.blockUntil && (
                    <p><strong>Blocked Until:</strong> {new Date(rateLimitStatus.blockUntil).toLocaleString()}</p>
                  )}
                </div>
              </div>
            )}
          </div>
        </section>

        <section className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Alternative Login Methods</h2>
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium">Direct Database Access</h3>
              <p className="text-sm text-gray-600 mb-2">
                If you have admin access, you can create a test user directly in the database.
              </p>
              <a 
                href="/debug/auth" 
                className="text-blue-600 hover:underline text-sm"
              >
                Go to Auth Debug Page
              </a>
            </div>
            
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium">Environment Check</h3>
              <p className="text-sm text-gray-600">
                Verify your Supabase configuration is correct in the environment variables.
              </p>
            </div>
          </div>
        </section>
      </div>
      
      <div className="mt-8 text-center space-x-4">
        <a href="/login" className="text-blue-600 hover:underline">Back to Login</a>
        <a href="/" className="text-blue-600 hover:underline">Back to Home</a>
      </div>
    </div>
  );
}
