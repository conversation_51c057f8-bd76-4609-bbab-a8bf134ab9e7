import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser, createAuthErrorResponse } from '@/lib/auth';
import { createAdminClient } from '@/lib/supabase';
import { Database } from '@/types/database.types';

export async function GET(req: NextRequest) {
  try {
    // Use standardized authentication
    const authResult = await getAuthenticatedUser();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const user = authResult.user;
    const adminClient = createAdminClient();

    // Test database access with new secure authentication
    const { data: prospectData, error: prospectError } = await adminClient
      .from('prospects')
      .select('id, user_id, training_status')
      .eq('user_id', user.id)
      .single();

    // Try to fetch some training modules to test database access
    const { data: modules, error: modulesError } = await adminClient
      .from('training_modules')
      .select('id, title, status')
      .limit(3);

    // Try to fetch some assessments to test database access
    const { data: assessments, error: assessmentsError } = await adminClient
      .from('assessments')
      .select('id, title, category')
      .limit(3);

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        isAdmin: user.isAdmin,
        isBPOAdmin: user.isBPOAdmin,
        bpoMemberships: user.bpoMemberships
      },
      database_access: {
        prospect: {
          data: prospectData,
          error: prospectError?.message
        },
        training_modules: {
          data: modules,
          error: modulesError?.message
        },
        assessments: {
          data: assessments,
          error: assessmentsError?.message
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error('Debug API Error:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 