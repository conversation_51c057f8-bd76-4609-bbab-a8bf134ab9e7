import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { Database } from '@/types/supabase';

// Create admin client with service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

export async function POST(req: NextRequest) {
  try {
    // Require platform admin access for this critical operation
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { action, targetUserId } = await req.json();

    // Validate the action
    if (!action || !['logout_all_users', 'logout_single_user'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "logout_all_users" or "logout_single_user"' },
        { status: 400 }
      );
    }

    // For single user logout, validate targetUserId
    if (action === 'logout_single_user' && !targetUserId) {
      return NextResponse.json(
        { error: 'Target user ID is required for single user logout' },
        { status: 400 }
      );
    }

    let loggedOutUsers = 0;
    let errors: string[] = [];

    if (action === 'logout_all_users') {
      // Get all users from the database
      const { data: users, error: usersError } = await adminClient
        .from('users')
        .select('id, email, full_name')
        .neq('id', authResult.user.id); // Don't logout the admin performing the action

      if (usersError) {
        return NextResponse.json(
          { error: 'Failed to fetch users: ' + usersError.message },
          { status: 500 }
        );
      }

      if (!users || users.length === 0) {
        return NextResponse.json({
          success: true,
          message: 'No users to logout',
          loggedOutUsers: 0,
          errors: []
        });
      }

      // Logout each user by invalidating their sessions
      for (const user of users) {
        try {
          // Use admin client to sign out the user
          const { error } = await adminClient.auth.admin.signOut(user.id);
          
          if (error) {
            errors.push(`Failed to logout ${user.email}: ${error.message}`);
          } else {
            loggedOutUsers++;
          }
        } catch (error: any) {
          errors.push(`Failed to logout ${user.email}: ${error.message}`);
        }
      }

      // Log this critical admin action
      console.log(`[ADMIN ACTION] ${authResult.user.email} performed hard logout of all users. Logged out: ${loggedOutUsers}, Errors: ${errors.length}`);

    } else if (action === 'logout_single_user') {
      // Logout a specific user
      try {
        // Get user info for logging
        const { data: targetUser, error: userError } = await adminClient
          .from('users')
          .select('email, full_name')
          .eq('id', targetUserId)
          .single();

        if (userError || !targetUser) {
          return NextResponse.json(
            { error: 'Target user not found' },
            { status: 404 }
          );
        }

        // Don't allow admin to logout themselves
        if (targetUserId === authResult.user.id) {
          return NextResponse.json(
            { error: 'Cannot logout yourself using this endpoint' },
            { status: 400 }
          );
        }

        // Sign out the target user
        const { error } = await adminClient.auth.admin.signOut(targetUserId);
        
        if (error) {
          errors.push(`Failed to logout ${targetUser.email}: ${error.message}`);
        } else {
          loggedOutUsers = 1;
        }

        // Log this admin action
        console.log(`[ADMIN ACTION] ${authResult.user.email} performed hard logout of user ${targetUser.email}`);

      } catch (error: any) {
        errors.push(`Failed to logout user: ${error.message}`);
      }
    }

    // Return results
    const response = {
      success: loggedOutUsers > 0 || errors.length === 0,
      message: action === 'logout_all_users' 
        ? `Hard logout completed. ${loggedOutUsers} users logged out.`
        : loggedOutUsers > 0 
          ? 'User logged out successfully'
          : 'Failed to logout user',
      loggedOutUsers,
      errors: errors.length > 0 ? errors : undefined,
      timestamp: new Date().toISOString(),
      performedBy: authResult.user.email
    };

    return NextResponse.json(response);

  } catch (error: any) {
    console.error('Hard logout error:', error);
    return NextResponse.json(
      { error: 'Internal server error: ' + error.message },
      { status: 500 }
    );
  }
}

// GET endpoint to check current active sessions (for monitoring)
export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Get all users and their last sign in times
    const { data: users, error } = await adminClient
      .from('users')
      .select('id, email, full_name, last_sign_in_at, created_at')
      .order('last_sign_in_at', { ascending: false, nullsFirst: false });

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch user session data: ' + error.message },
        { status: 500 }
      );
    }

    // Filter to show only recently active users (last 24 hours)
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentlyActiveUsers = users?.filter(user => 
      user.last_sign_in_at && new Date(user.last_sign_in_at) > twentyFourHoursAgo
    ) || [];

    return NextResponse.json({
      totalUsers: users?.length || 0,
      recentlyActiveUsers: recentlyActiveUsers.length,
      users: recentlyActiveUsers.map(user => ({
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        last_sign_in_at: user.last_sign_in_at
      }))
    });

  } catch (error: any) {
    console.error('Session monitoring error:', error);
    return NextResponse.json(
      { error: 'Internal server error: ' + error.message },
      { status: 500 }
    );
  }
}
