/**
 * Component Type Definitions
 * Comprehensive TypeScript types for all UI components
 */

import { ReactNode, ComponentType } from 'react'

// =============================================================================
// COMMON TYPES
// =============================================================================

export type Size = 'sm' | 'md' | 'lg'
export type Variant = 'default' | 'compact' | 'detailed'
export type Status = 'active' | 'inactive' | 'pending' | 'completed' | 'failed'
export type Priority = 'low' | 'medium' | 'high' | 'urgent'
export type UserRole = 'admin' | 'bpo_admin' | 'prospect'
export type UserStatus = 'online' | 'offline' | 'away' | 'busy'

// =============================================================================
// USER TYPES
// =============================================================================

export interface User {
  id: string
  email: string
  full_name: string
  role: UserRole
  status: 'active' | 'inactive' | 'pending_activation'
  avatar_url?: string
  last_login?: string
  created_at: string
  updated_at: string
}

export interface Prospect {
  id: string
  user_id: string
  contact_info?: ContactInfo
  education?: Education[]
  experience?: Experience[]
  skills?: Skill[]
  intro_video_url?: string
  resume_url?: string
  profile_image?: string
  profile_visibility: boolean
  training_status: 'not_started' | 'in_progress' | 'completed'
  created_at: string
  updated_at: string
}

export interface ContactInfo {
  phone?: string
  address?: string
  city?: string
  country?: string
  linkedin?: string
  portfolio?: string
}

export interface Education {
  institution: string
  degree: string
  field_of_study?: string
  start_date?: string
  end_date?: string
  description?: string
}

export interface Experience {
  company: string
  position: string
  start_date?: string
  end_date?: string
  current?: boolean
  description?: string
  skills_used?: string[]
}

export interface Skill {
  name: string
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  category?: string
}

// =============================================================================
// TRAINING TYPES
// =============================================================================

export interface TrainingModule {
  id: string
  title: string
  description?: string
  cover_image_url?: string
  duration_minutes?: number
  required_order?: number
  status: 'draft' | 'published' | 'archived'
  requires_assessment: boolean
  created_by: string
  created_at: string
  updated_at: string
  progressPercentage?: number
}

export interface Lesson {
  id: string
  module_id: string
  title: string
  description?: string
  order_index: number
  duration_minutes?: number
  lesson_type?: string
  video_url?: string
  media_url?: string
  created_at: string
  updated_at: string
}

export interface Activity {
  id: string
  lesson_id: string
  title: string
  description?: string
  type: 'video' | 'reading' | 'quiz' | 'assignment' | 'discussion'
  content: Record<string, any>
  content_url?: string
  passing_criteria?: Record<string, any>
  order_index: number
  duration_minutes?: number
  created_at: string
  updated_at: string
}

export interface ProgressRecord {
  id: string
  prospect_id: string
  activity_id: string
  status: 'not_started' | 'in_progress' | 'completed' | 'failed'
  score?: number
  attempts: number
  completed_at?: string
  time_spent_seconds: number
  is_module_assessment: boolean
  created_at: string
  updated_at: string
}

// =============================================================================
// BPO TYPES
// =============================================================================

export interface BPO {
  id: string
  name: string
  description?: string
  logo_url?: string
  industry?: string
  size_range?: string
  founded_year?: number
  website_url?: string
  location?: Location
  contact_email?: string
  phone?: string
  created_by: string
  reviews?: Review[]
  workplace_stats?: Record<string, any>
  created_at: string
  updated_at: string
}

export interface Location {
  address?: string
  city?: string
  state?: string
  country?: string
  postal_code?: string
  coordinates?: {
    lat: number
    lng: number
  }
}

export interface Review {
  id: string
  rating: number
  comment?: string
  reviewer_name?: string
  created_at: string
}

export interface BPOTeamMember {
  id: string
  bpo_id: string
  user_id: string
  role: 'admin' | 'manager' | 'recruiter' | 'member'
  permissions: Record<string, boolean>
  invited_at?: string
  invited_by?: string
  accepted_at?: string
  is_placeholder: boolean
  created_at: string
  updated_at: string
}

// =============================================================================
// JOB TYPES
// =============================================================================

export interface JobPosting {
  id: string
  bpo_id: string
  title: string
  description: string
  requirements?: string[]
  responsibilities?: string[]
  salary_range?: SalaryRange
  employment_type: 'full_time' | 'part_time' | 'contract' | 'internship'
  location_type: 'remote' | 'on_site' | 'hybrid'
  experience_level: 'entry' | 'mid' | 'senior' | 'executive'
  status: 'draft' | 'published' | 'closed' | 'archived'
  application_deadline?: string
  created_by: string
  created_at: string
  updated_at: string
}

export interface SalaryRange {
  min: number
  max: number
  currency: string
  period: 'hourly' | 'monthly' | 'yearly'
}

export interface Application {
  id: string
  job_id: string
  prospect_id: string
  status: 'submitted' | 'reviewing' | 'shortlisted' | 'rejected' | 'accepted'
  cover_letter?: string
  additional_notes?: string
  resume_url?: string
  portfolio_url?: string
  expected_salary_min?: number
  expected_salary_max?: number
  availability_start_date?: string
  is_remote_preferred: boolean
  submitted_at: string
  reviewed_at?: string
  reviewed_by?: string
  rejection_reason?: string
  interview_scheduled_at?: string
  created_at: string
  updated_at: string
}

// =============================================================================
// COMPONENT PROP TYPES
// =============================================================================

export interface BaseComponentProps {
  className?: string
  children?: ReactNode
}

export interface IconComponentProps {
  className?: string
  size?: Size
}

export interface LoadingProps {
  loading?: boolean
  loadingText?: string
  loadingComponent?: ComponentType
}

export interface ErrorProps {
  error?: string | Error | null
  onRetry?: () => void
  errorComponent?: ComponentType<{ error: string; onRetry?: () => void }>
}

export interface PaginationProps {
  currentPage: number
  totalPages: number
  pageSize: number
  totalItems: number
  onPageChange: (page: number) => void
  onPageSizeChange?: (size: number) => void
}

export interface SortingProps {
  sortKey?: string
  sortDirection?: 'asc' | 'desc'
  onSort?: (key: string, direction: 'asc' | 'desc') => void
}

export interface FilteringProps {
  filters?: Record<string, any>
  onFilterChange?: (filters: Record<string, any>) => void
}

export interface SearchProps {
  searchQuery?: string
  onSearchChange?: (query: string) => void
  placeholder?: string
}

// =============================================================================
// FORM TYPES
// =============================================================================

export interface FormFieldConfig {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file'
  placeholder?: string
  description?: string
  required?: boolean
  disabled?: boolean
  options?: Array<{ value: string; label: string }>
  validation?: {
    min?: number
    max?: number
    minLength?: number
    maxLength?: number
    pattern?: RegExp
    custom?: (value: any) => string | true
  }
}

export interface FormState {
  values: Record<string, any>
  errors: Record<string, string>
  touched: Record<string, boolean>
  isSubmitting: boolean
  isValid: boolean
}

export interface FormActions {
  setValue: (name: string, value: any) => void
  setError: (name: string, error: string) => void
  clearError: (name: string) => void
  setTouched: (name: string, touched: boolean) => void
  reset: () => void
  submit: () => Promise<void>
}

// =============================================================================
// DATA DISPLAY TYPES
// =============================================================================

export interface TableColumn<T = any> {
  key: string
  title: string
  width?: string
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, record: T, index: number) => ReactNode
  className?: string
}

export interface TableProps<T = any> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  error?: string
  emptyMessage?: string
  pagination?: PaginationProps
  sorting?: SortingProps
  filtering?: FilteringProps
  selection?: {
    selectedRows: string[]
    onSelectionChange: (selectedRows: string[]) => void
    getRowId: (record: T) => string
  }
  onRowClick?: (record: T, index: number) => void
  className?: string
}

export interface ListProps<T = any> {
  data: T[]
  renderItem: (item: T, index: number) => ReactNode
  loading?: boolean
  error?: string
  emptyMessage?: string
  pagination?: PaginationProps
  className?: string
}

// =============================================================================
// LAYOUT TYPES
// =============================================================================

export interface LayoutProps extends BaseComponentProps {
  header?: ReactNode
  sidebar?: ReactNode
  footer?: ReactNode
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

export interface NavigationItem {
  id: string
  label: string
  href?: string
  icon?: ComponentType<IconComponentProps>
  badge?: string | number
  children?: NavigationItem[]
  disabled?: boolean
  external?: boolean
}

export interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

// =============================================================================
// NOTIFICATION TYPES
// =============================================================================

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  persistent?: boolean
  actions?: Array<{
    label: string
    action: () => void
    variant?: 'default' | 'destructive'
  }>
  created_at: string
}

// =============================================================================
// API RESPONSE TYPES
// =============================================================================

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  meta?: {
    pagination?: {
      page: number
      pageSize: number
      total: number
      totalPages: number
    }
    sorting?: {
      key: string
      direction: 'asc' | 'desc'
    }
    filters?: Record<string, any>
  }
}

export interface ApiError {
  message: string
  code?: string
  details?: Record<string, any>
  timestamp: string
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// =============================================================================
// COMPONENT COMPOSITION TYPES
// =============================================================================

export interface WithLoadingProps {
  loading?: boolean
  loadingComponent?: ComponentType
}

export interface WithErrorProps {
  error?: string | Error | null
  errorComponent?: ComponentType<{ error: string }>
}

export interface WithPermissionsProps {
  requiredPermissions?: string[]
  fallbackComponent?: ComponentType
}

export interface WithAnalyticsProps {
  trackingId?: string
  eventName?: string
  eventData?: Record<string, any>
}
