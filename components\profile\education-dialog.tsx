"use client";

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Plus, Edit, Trash2 } from "lucide-react"

const currentYear = new Date().getFullYear()
const years = Array.from({ length: 60 }, (_, i) => currentYear - i)

const educationSchema = z.object({
  degree: z.string().min(2, {
    message: "Degree must be at least 2 characters.",
  }),
  institution: z.string().min(2, {
    message: "Institution name must be at least 2 characters.",
  }),
  field_of_study: z.string().optional(),
  start_year: z.string(),
  end_year: z.string().optional(),
  current_education: z.boolean().default(false),
  description: z.string().optional(),
  location: z.string().optional(),
})

type EducationValues = z.infer<typeof educationSchema>

interface EducationDialogProps {
  triggerComponent?: React.ReactNode
  education?: any
  educationIndex?: number
  action: "add" | "update" | "delete"
  onSuccess?: () => void
}

export function EducationDialog({
  triggerComponent,
  education,
  educationIndex,
  action = "add",
  onSuccess
}: EducationDialogProps) {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  
  const isUpdate = action === "update"
  const isDelete = action === "delete"
  
  // Parse education data
  let defaultValues: EducationValues = {
    degree: "",
    institution: "",
    field_of_study: "",
    location: "",
    start_year: currentYear.toString(),
    end_year: "",
    current_education: false,
    description: "",
  }
  
  if (isUpdate && education) {
    // Check for current education - either explicit current flag or null end_year
    const isCurrentEducation = education.current === true || education.end_year === null;
    
    defaultValues = {
      degree: education.degree || "",
      institution: education.institution || "",
      field_of_study: education.field_of_study || "",
      location: education.location || "",
      start_year: education.start_year?.toString() || currentYear.toString(),
      end_year: !isCurrentEducation ? (education.end_year?.toString() || "") : "",
      current_education: isCurrentEducation,
      description: education.description || "",
    }
  }
  
  const form = useForm<EducationValues>({
    resolver: zodResolver(educationSchema),
    defaultValues,
  })
  
  // Watch current education value to conditionally show/hide end date
  const currentEducation = form.watch("current_education")
  
  async function onSubmit(data: EducationValues) {
    try {
      setIsLoading(true)
      
      // Format education data
      const educationData = {
        degree: data.degree,
        institution: data.institution,
        field_of_study: data.field_of_study,
        location: data.location,
        start_year: parseInt(data.start_year),
        end_year: data.current_education ? null : (data.end_year ? parseInt(data.end_year) : null),
        description: data.description,
        current: data.current_education
      }
      
      console.log('EducationDialog - Submitting data:', {
        action: isDelete ? "delete" : isUpdate ? "update" : "add",
        educationData,
        educationIndex: isUpdate || isDelete ? educationIndex : undefined,
      });
      
      // Function to make the API call with built-in retry
      const updateEducation = async (retryCount = 0): Promise<any> => {
        try {
          const response = await fetch("/api/profile/update-education", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              action: isDelete ? "delete" : isUpdate ? "update" : "add",
              educationData: isDelete ? null : educationData,
              educationIndex: isUpdate || isDelete ? educationIndex : undefined,
            }),
          });
          
          if (!response.ok) {
            const errorText = await response.text();
            console.error('EducationDialog - API error status:', response.status, response.statusText);
            console.error('EducationDialog - API error response:', errorText);
            
            // Retry logic - max 2 retries (3 attempts total)
            if (retryCount < 2) {
              console.log(`EducationDialog - Retrying (${retryCount + 1}/2)...`);
              return await updateEducation(retryCount + 1);
            }
            
            throw new Error(errorText || `Server error: ${response.status}`);
          }
          
          return await response.json();
        } catch (error) {
          if (retryCount < 2) {
            console.log(`EducationDialog - Retrying after error (${retryCount + 1}/2)...`);
            // Add exponential backoff for retries
            await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 500));
            return await updateEducation(retryCount + 1);
          }
          throw error;
        }
      };
      
      // Make the API call with retries
      const result = await updateEducation();
      
      console.log('EducationDialog - Success:', result);
      
      // Show success toast and close dialog
      const message = isDelete 
        ? "Education deleted successfully" 
        : isUpdate 
          ? "Education updated successfully" 
          : "Education added successfully"
          
      toast.success(message)
      setOpen(false)
      
      // Refresh data
      if (onSuccess) {
        onSuccess()
      } else {
        router.refresh()
      }
    } catch (error: any) {
      console.error('EducationDialog - Error:', error);
      toast.error(error.message || "Failed to update education. Please try again.");
    } finally {
      setIsLoading(false)
    }
  }
  
  // Handle delete education
  async function handleDelete() {
    if (isDelete) {
      onSubmit({} as EducationValues)
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {triggerComponent || (
          isDelete ? (
            <Button size="icon" variant="ghost" className="text-destructive hover:text-destructive">
              <Trash2 className="h-4 w-4" />
            </Button>
          ) : isUpdate ? (
            <Button size="icon" variant="ghost">
              <Edit className="h-4 w-4" />
            </Button>
          ) : (
            <Button className="mt-3" variant="outline" size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Education
            </Button>
          )
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px]">
        {isDelete ? (
          <>
            <DialogHeader>
              <DialogTitle>Delete Education</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this education entry?
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="mt-4">
              <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
              <Button variant="destructive" onClick={handleDelete} disabled={isLoading}>
                {isLoading ? "Deleting..." : "Delete"}
              </Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>{isUpdate ? "Edit Education" : "Add Education"}</DialogTitle>
              <DialogDescription>
                {isUpdate ? "Update your education details." : "Add a new education to your profile."}
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="degree"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Degree / Certificate</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Bachelor of Science" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="institution"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Institution</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. University of Example" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="field_of_study"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Field of Study (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Computer Science" {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. New York, USA" {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="start_year"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Year</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Year" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {years.map((year) => (
                              <SelectItem key={year} value={year.toString()}>
                                {year}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {!currentEducation && (
                    <FormField
                      control={form.control}
                      name="end_year"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>End Year</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Year" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {years.map((year) => (
                                <SelectItem key={year} value={year.toString()}>
                                  {year}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
                
                <FormField
                  control={form.control}
                  name="current_education"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>I'm currently studying here</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Relevant coursework, achievements, etc." {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Saving..." : (isUpdate ? "Update" : "Add")}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
} 