'use client'

import { useState, useEffect } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface TimePickerProps {
  value: string // 24-hour format (HH:MM)
  onChange: (value: string) => void
  className?: string
}

export function TimePicker({ value, onChange, className }: TimePickerProps) {
  const [hour12, setHour12] = useState('9')
  const [minute, setMinute] = useState('00')
  const [ampm, setAmpm] = useState('AM')

  // Convert 24-hour to 12-hour format when value changes
  useEffect(() => {
    if (value) {
      const [hours, minutes] = value.split(':')
      const hour24 = parseInt(hours)
      const ampmValue = hour24 >= 12 ? 'PM' : 'AM'
      const hour12Value = hour24 % 12 || 12
      
      setHour12(hour12Value.toString())
      setMinute(minutes)
      setAmpm(ampmValue)
    }
  }, [value])

  // Convert 12-hour to 24-hour format and call onChange
  const updateTime = (newHour12?: string, newMinute?: string, newAmpm?: string) => {
    const h = newHour12 || hour12
    const m = newMinute || minute
    const ap = newAmpm || ampm
    
    let hour24 = parseInt(h)
    if (ap === 'PM' && hour24 !== 12) {
      hour24 += 12
    } else if (ap === 'AM' && hour24 === 12) {
      hour24 = 0
    }
    
    const time24 = `${hour24.toString().padStart(2, '0')}:${m}`
    onChange(time24)
  }

  const hours = Array.from({ length: 12 }, (_, i) => (i + 1).toString())
  const minutes = ['00', '15', '30', '45']

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <Select value={hour12} onValueChange={(value) => {
        setHour12(value)
        updateTime(value, minute, ampm)
      }}>
        <SelectTrigger className="w-16">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {hours.map((h) => (
            <SelectItem key={h} value={h}>{h}</SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      <span className="text-sm">:</span>
      
      <Select value={minute} onValueChange={(value) => {
        setMinute(value)
        updateTime(hour12, value, ampm)
      }}>
        <SelectTrigger className="w-16">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {minutes.map((m) => (
            <SelectItem key={m} value={m}>{m}</SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      <Select value={ampm} onValueChange={(value) => {
        setAmpm(value)
        updateTime(hour12, minute, value)
      }}>
        <SelectTrigger className="w-16">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="AM">AM</SelectItem>
          <SelectItem value="PM">PM</SelectItem>
        </SelectContent>
      </Select>
    </div>
  )
}
