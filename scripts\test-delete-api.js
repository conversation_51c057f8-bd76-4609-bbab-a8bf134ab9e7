const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testDeleteAPI() {
  console.log('🧪 Testing Delete API functionality...\n');

  try {
    // 1. Check environment variables
    console.log('1️⃣ Environment Variables:');
    console.log('✅ NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
    console.log('✅ SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);

    // 2. Test database connection
    console.log('\n2️⃣ Testing database connection...');
    const { data: testConnection, error: connectionError } = await supabase
      .from('files')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.error('❌ Database connection failed:', connectionError);
      return;
    }
    console.log('✅ Database connection successful');

    // 3. Get a test file (if any exist)
    console.log('\n3️⃣ Looking for test files...');
    const { data: files, error: filesError } = await supabase
      .from('files')
      .select('id, prospect_id, file_url, original_filename, title')
      .limit(5);

    if (filesError) {
      console.error('❌ Failed to get files:', filesError);
      return;
    }

    console.log(`✅ Found ${files?.length || 0} files in database`);
    if (files && files.length > 0) {
      console.log('📋 Sample files:');
      files.forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.title || file.original_filename} (ID: ${file.id})`);
      });
    }

    // 4. Test file path extraction logic
    console.log('\n4️⃣ Testing file path extraction...');
    const testUrls = [
      'https://snihmpmulfxhrjjcnzau.supabase.co/storage/v1/object/public/files/test.pdf',
      'https://snihmpmulfxhrjjcnzau.supabase.co/storage/v1/object/public/files/subfolder/test.jpg',
      'invalid-url'
    ];

    testUrls.forEach(url => {
      const urlParts = url.split('/files/');
      const filePath = urlParts.length > 1 ? urlParts[1] : '';
      console.log(`  URL: ${url}`);
      console.log(`  Extracted path: "${filePath}"`);
    });

    // 5. Test storage bucket access
    console.log('\n5️⃣ Testing storage bucket access...');
    try {
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
      
      if (bucketsError) {
        console.error('❌ Failed to list buckets:', bucketsError);
      } else {
        console.log('✅ Storage access successful');
        console.log('📋 Available buckets:', buckets?.map(b => b.name).join(', '));
      }
    } catch (storageError) {
      console.error('❌ Storage access failed:', storageError);
    }

    console.log('\n🎯 Delete API Test Complete!');
    console.log('\nIf you see any ❌ errors above, those need to be fixed for delete to work.');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testDeleteAPI();
