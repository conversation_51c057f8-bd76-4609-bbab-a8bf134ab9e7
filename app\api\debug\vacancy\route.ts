import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: Request) {
  try {
    const data = await request.json();
    console.log("Received data for vacancy creation:", data);
    
    // Check for required fields to help with debugging
    const requiredFields = ['title', 'bpo_id'];
    const missingFields = requiredFields.filter(field => !data[field]);
    if (missingFields.length > 0) {
      return NextResponse.json({
        success: false,
        error: `Missing required fields: ${missingFields.join(', ')}`,
        message: "Required fields are missing",
        data: data
      }, { status: 400 });
    }
    
    // Prepare a minimal vacancy with just the essential fields if others are causing issues
    const minimalVacancy = {
      title: data.title,
      description: data.description || "",
      requirements: data.requirements || "",
      job_type: data.job_type || "full_time",
      bpo_id: data.bpo_id,
      status: data.status || "draft", // Valid values are "draft", "published", "archived"
      created_at: new Date().toISOString()
    };
    
    console.log("Trying with minimal vacancy:", minimalVacancy);
    
    // Try with minimal data first
    const { data: minimalResult, error: minimalError } = await supabase
      .from('job_postings')
      .insert(minimalVacancy)
      .select();
      
    if (minimalError) {
      console.error("Error creating minimal vacancy:", minimalError);
      
      // Try to log what fields might be causing issues
      return NextResponse.json({
        success: false,
        error: minimalError,
        message: `Minimal creation failed: ${minimalError.message}`,
        data: minimalVacancy
      }, { status: 400 });
    }
    
    console.log("Minimal vacancy created successfully:", minimalResult);
    return NextResponse.json({
      success: true,
      message: "Vacancy created successfully (minimal data)",
      data: minimalResult
    });
    
  } catch (error) {
    console.error("Server error:", error);
    return NextResponse.json({
      success: false,
      error: String(error),
      message: "Server error occurred",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
} 