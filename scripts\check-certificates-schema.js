const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkSchema() {
  console.log('🔍 Checking certificates table schema...\n');

  try {
    // Check if certificates table exists and get its structure
    const { data: certificates, error: certError } = await supabase
      .from('certificates')
      .select('*')
      .limit(1);

    if (certError) {
      console.log('❌ Certificates table error:', certError.message);
    } else {
      console.log('✅ Certificates table exists');
      if (certificates && certificates.length > 0) {
        console.log('📋 Sample certificate record columns:');
        console.log(Object.keys(certificates[0]));
      } else {
        console.log('📋 Certificates table is empty');
      }
    }

    // Check if files table exists and get its structure
    const { data: files, error: filesError } = await supabase
      .from('files')
      .select('*')
      .limit(1);

    if (filesError) {
      console.log('❌ Files table does not exist yet:', filesError.message);
    } else {
      console.log('✅ Files table already exists');
      if (files && files.length > 0) {
        console.log('📋 Sample file record columns:');
        console.log(Object.keys(files[0]));
      } else {
        console.log('📋 Files table is empty');

        // Try to get table structure by testing individual columns
        const columnsToTest = [
          'id', 'prospect_id', 'module_id', 'title', 'file_type', 'file_category',
          'file_url', 'original_filename', 'file_size', 'issued_at', 'expires_at',
          'verification_code', 'created_at', 'updated_at'
        ];

        console.log('🔍 Testing individual columns:');
        const existingColumns = [];

        for (const column of columnsToTest) {
          const { data, error } = await supabase
            .from('files')
            .select(column)
            .limit(1);

          if (error) {
            console.log(`❌ ${column}: ${error.message}`);
          } else {
            console.log(`✅ ${column}: exists`);
            existingColumns.push(column);
          }
        }

        console.log('\n📋 Existing columns:', existingColumns.join(', '));
      }
    }

    // Get count of records
    const { count: certCount } = await supabase
      .from('certificates')
      .select('*', { count: 'exact', head: true });

    console.log(`\n📊 Certificates count: ${certCount || 0}`);

  } catch (error) {
    console.error('❌ Error checking schema:', error);
  }
}

checkSchema();
