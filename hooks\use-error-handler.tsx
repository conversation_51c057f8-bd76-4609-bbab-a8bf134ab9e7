"use client";

import { useCallback, useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { 
  AppError, 
  ErrorType, 
  createError, 
  logError, 
  handleSupabaseError, 
  handleFetchError 
} from '@/lib/utils';

/**
 * Error state interface
 */
export interface ErrorState {
  error: AppError | null;
  isError: boolean;
  isLoading: boolean;
}

/**
 * Error handler hook options
 */
export interface UseErrorHandlerOptions {
  showToast?: boolean;
  logErrors?: boolean;
  context?: string;
}

/**
 * Custom hook for standardized error handling
 */
export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const { showToast = true, logErrors = true, context } = options;
  const { toast } = useToast();
  
  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isError: false,
    isLoading: false
  });

  /**
   * Handle any error with standardized processing
   */
  const handleError = useCallback((
    error: any, 
    errorContext?: string,
    customUserMessage?: string
  ) => {
    let appError: AppError;

    // Convert different error types to AppError
    if (error?.code && (error?.message || error?.details)) {
      // Supabase error
      appError = handleSupabaseError(error, errorContext || context);
    } else if (error?.status || (error instanceof TypeError && error.message.includes('fetch'))) {
      // Fetch/Network error
      appError = handleFetchError(error, errorContext || context);
    } else if (error instanceof Error) {
      // Standard JavaScript error
      appError = createError(
        ErrorType.UNKNOWN,
        error.message,
        customUserMessage,
        { originalError: error, context: errorContext || context }
      );
    } else {
      // Unknown error type
      appError = createError(
        ErrorType.UNKNOWN,
        String(error),
        customUserMessage || 'An unexpected error occurred',
        { originalError: error, context: errorContext || context }
      );
    }

    // Override user message if provided
    if (customUserMessage) {
      appError.userMessage = customUserMessage;
    }

    // Log error if enabled
    if (logErrors) {
      logError(appError, errorContext || context);
    }

    // Show toast notification if enabled
    if (showToast) {
      toast({
        title: getErrorTitle(appError.type),
        description: appError.userMessage,
        variant: 'destructive',
      });
    }

    // Update error state
    setErrorState({
      error: appError,
      isError: true,
      isLoading: false
    });

    return appError;
  }, [toast, showToast, logErrors, context]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isError: false,
      isLoading: false
    });
  }, []);

  /**
   * Set loading state
   */
  const setLoading = useCallback((loading: boolean) => {
    setErrorState(prev => ({
      ...prev,
      isLoading: loading,
      isError: loading ? false : prev.isError
    }));
  }, []);

  /**
   * Execute async operation with error handling
   */
  const executeAsync = useCallback(async <T>(
    operation: () => Promise<T>,
    errorContext?: string,
    customErrorMessage?: string
  ): Promise<T | null> => {
    try {
      setLoading(true);
      clearError();
      const result = await operation();
      setLoading(false);
      return result;
    } catch (error) {
      setLoading(false);
      handleError(error, errorContext, customErrorMessage);
      return null;
    }
  }, [handleError, setLoading, clearError]);

  /**
   * Execute async operation with retry logic
   */
  const executeWithRetry = useCallback(async <T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    retryDelay: number = 1000,
    errorContext?: string
  ): Promise<T | null> => {
    let lastError: any;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        setLoading(true);
        if (attempt === 1) clearError();
        
        const result = await operation();
        setLoading(false);
        return result;
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          setLoading(false);
          handleError(error, `${errorContext} (failed after ${maxRetries} attempts)`);
          return null;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }
    
    return null;
  }, [handleError, setLoading, clearError]);

  return {
    // State
    ...errorState,
    
    // Actions
    handleError,
    clearError,
    setLoading,
    executeAsync,
    executeWithRetry,
    
    // Utilities
    showSuccessToast: (message: string, title?: string) => {
      toast({
        title: title || 'Success',
        description: message,
        variant: 'default',
      });
    },
    
    showWarningToast: (message: string, title?: string) => {
      toast({
        title: title || 'Warning',
        description: message,
        variant: 'destructive', // You might want to add a warning variant
      });
    }
  };
}

/**
 * Get appropriate error title based on error type
 */
function getErrorTitle(errorType: ErrorType): string {
  switch (errorType) {
    case ErrorType.AUTHENTICATION:
      return 'Authentication Required';
    case ErrorType.AUTHORIZATION:
      return 'Access Denied';
    case ErrorType.VALIDATION:
      return 'Invalid Input';
    case ErrorType.DATABASE:
      return 'Database Error';
    case ErrorType.NETWORK:
      return 'Connection Error';
    case ErrorType.FILE_UPLOAD:
      return 'Upload Failed';
    case ErrorType.NOT_FOUND:
      return 'Not Found';
    case ErrorType.RATE_LIMIT:
      return 'Rate Limited';
    case ErrorType.SERVER_ERROR:
      return 'Server Error';
    default:
      return 'Error';
  }
}

/**
 * Higher-order component for error boundary functionality
 */
export function withErrorHandler<P extends object>(
  Component: React.ComponentType<P>,
  options?: UseErrorHandlerOptions
) {
  return function WrappedComponent(props: P) {
    const errorHandler = useErrorHandler(options);
    
    return (
      <Component 
        {...props} 
        errorHandler={errorHandler}
      />
    );
  };
}
