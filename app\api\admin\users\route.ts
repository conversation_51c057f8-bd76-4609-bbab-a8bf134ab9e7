import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { Database } from '@/types/database.types';

export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    // Parse request body
    const body = await req.json();
    const { email, password, role, full_name } = body;
    
    // Validate required fields
    if (!email || !password || !role || !full_name) {
      return NextResponse.json(
        { error: 'Email, password, role, and full name are required' },
        { status: 400 }
      );
    }
    
    // Validate password length
    if (password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }
    
    // Create admin client for creating users
    const adminClient = createAdminClient();
    
    // Create user in Supabase Auth with display name
    const { data, error } = await adminClient.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        full_name: full_name
      },
    });
    
    if (error || !data.user) {
      return NextResponse.json(
        { error: error?.message || 'Failed to create user in Auth' },
        { status: 500 }
      );
    }
    
    // Create user in database
    const { error: profileError } = await supabase
      .from('users')
      .insert([
        {
          id: data.user.id,
          email,
          full_name,
          encrypted_password: 'MANAGED_BY_SUPABASE_AUTH',
          role,
          status: 'active',
          is_password_change_required: true,
        },
      ]);
    
    if (profileError) {
      // If there's an error creating the profile, clean up by deleting the auth user
      await adminClient.auth.admin.deleteUser(data.user.id);
      
      return NextResponse.json(
        { error: profileError.message || 'Failed to create user profile' },
        { status: 500 }
      );
    }
    
    // If the user is a prospect, create a prospect record
    if (role === 'prospect') {
      const { error: prospectError } = await supabase
        .from('prospects')
        .insert([
          {
            user_id: data.user.id,
            contact_info: { email },
          },
        ]);
      
      if (prospectError) {
        // Log the error but don't fail the request, as the main user is created
        console.error('Error creating prospect record:', prospectError);
      }
    }
    
    return NextResponse.json({ 
      success: true, 
      user: { 
        id: data.user.id, 
        email, 
        role, 
        full_name 
      } 
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    // Get all users
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (usersError) {
      return NextResponse.json(
        { error: usersError.message || 'Failed to fetch users' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ users });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
} 