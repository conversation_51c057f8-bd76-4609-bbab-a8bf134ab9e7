'use client';

import React from 'react';
import { useAuth } from '@/hooks/use-auth';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';
import Link from 'next/link';

interface AuthGuardProps {
  children: React.ReactNode;
  fallbackUrl?: string;
}

export function AuthGuard({ children, fallbackUrl = '/auth/login' }: AuthGuardProps) {
  const { user, loading: authLoading } = useAuth();

  // Show loading state
  if (authLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-4">
          <div className="h-8 w-3/4 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  // Show error if not logged in
  if (!user) {
    return (
      <div className="w-full max-w-md mx-auto p-4">
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            You must be logged in to access this page.
          </AlertDescription>
        </Alert>
        
        <Button asChild className="w-full">
          <Link href={fallbackUrl}>
            Back to Login
          </Link>
        </Button>
      </div>
    );
  }

  // User is authenticated, render children
  return <>{children}</>;
} 