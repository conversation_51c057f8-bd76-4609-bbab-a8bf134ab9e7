"use client";

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"
import { Plus, Edit, Trash2 } from "lucide-react"

const skillSchema = z.object({
  name: z.string().min(2, {
    message: "Skill name must be at least 2 characters.",
  }),
  level: z.string(),
  proficiency: z.number().min(0).max(100),
})

type SkillValues = z.infer<typeof skillSchema>

interface SkillDialogProps {
  triggerComponent?: React.ReactNode
  skill?: any
  skillIndex?: number
  action: "add" | "update" | "delete"
  onSuccess?: () => void
}

export function SkillDialog({
  triggerComponent,
  skill,
  skillIndex,
  action = "add",
  onSuccess
}: SkillDialogProps) {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  
  const isUpdate = action === "update"
  const isDelete = action === "delete"
  
  // Parse skill data
  let defaultValues: SkillValues = {
    name: "",
    level: "Beginner",
    proficiency: 50,
  }
  
  if (isUpdate && skill) {
    defaultValues = {
      name: skill.name || "",
      level: skill.level || "Beginner",
      proficiency: skill.proficiency || 50,
    }
  }
  
  const form = useForm<SkillValues>({
    resolver: zodResolver(skillSchema),
    defaultValues,
  })
  
  async function onSubmit(values: SkillValues) {
    try {
      setIsLoading(true)
      
      // Format skill data
      const skillData = {
        name: values.name,
        level: values.level,
        proficiency: values.proficiency,
      }
      
      console.log('SkillDialog - Submitting skill data:', {
        action: isDelete ? "delete" : isUpdate ? "update" : "add",
        skillData,
        skillIndex: isUpdate || isDelete ? skillIndex : undefined,
      });
      
      // Function to make the API call with built-in retry
      const updateSkills = async (retryCount = 0): Promise<any> => {
        try {
          const response = await fetch("/api/profile/update-skills", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              action: isDelete ? "delete" : isUpdate ? "update" : "add",
              skillData: isDelete ? null : skillData,
              skillIndex: isUpdate || isDelete ? skillIndex : undefined,
            }),
          });
          
          if (!response.ok) {
            const errorText = await response.text();
            console.error('SkillDialog - API error status:', response.status, response.statusText);
            console.error('SkillDialog - API error response:', errorText);
            
            // Retry logic - max 2 retries (3 attempts total)
            if (retryCount < 2) {
              console.log(`SkillDialog - Retrying (${retryCount + 1}/2)...`);
              return await updateSkills(retryCount + 1);
            }
            
            throw new Error(errorText || `Server error: ${response.status}`);
          }
          
          return await response.json();
        } catch (error) {
          if (retryCount < 2) {
            console.log(`SkillDialog - Retrying after error (${retryCount + 1}/2)...`);
            // Add exponential backoff for retries
            await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 500));
            return await updateSkills(retryCount + 1);
          }
          throw error;
        }
      };
      
      // Make the API call with retries
      const result = await updateSkills();
      
      console.log('SkillDialog - Success:', result);
      
      if (onSuccess) {
        onSuccess();
      }
      
      setOpen(false);
    } catch (error: any) {
      console.error('SkillDialog - Error:', error);
      toast.error(error.message || "Failed to update skill. Please try again.");
    } finally {
      setIsLoading(false)
    }
  }
  
  // Handle delete skill
  async function handleDelete() {
    if (isDelete) {
      onSubmit(defaultValues)
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {triggerComponent || (
          isDelete ? (
            <Button size="icon" variant="ghost" className="text-destructive hover:text-destructive">
              <Trash2 className="h-4 w-4" />
            </Button>
          ) : isUpdate ? (
            <Button size="icon" variant="ghost">
              <Edit className="h-4 w-4" />
            </Button>
          ) : (
            <Button className="mt-3" variant="outline" size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Skill
            </Button>
          )
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[450px]">
        {isDelete ? (
          <>
            <DialogHeader>
              <DialogTitle>Delete Skill</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this skill?
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="mt-4">
              <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
              <Button variant="destructive" onClick={handleDelete} disabled={isLoading}>
                {isLoading ? "Deleting..." : "Delete"}
              </Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>{isUpdate ? "Edit Skill" : "Add Skill"}</DialogTitle>
              <DialogDescription>
                {isUpdate ? "Update your skill details." : "Add a new skill to your profile."}
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Skill Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Customer Service" {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="level"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Proficiency Level</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select level" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Beginner">Beginner</SelectItem>
                          <SelectItem value="Intermediate">Intermediate</SelectItem>
                          <SelectItem value="Advanced">Advanced</SelectItem>
                          <SelectItem value="Expert">Expert</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="proficiency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Proficiency Rating ({field.value}%)</FormLabel>
                      <FormControl>
                        <Slider
                          defaultValue={[field.value]}
                          max={100}
                          step={5}
                          onValueChange={(value) => field.onChange(value[0])}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Saving..." : (isUpdate ? "Update" : "Add")}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
} 