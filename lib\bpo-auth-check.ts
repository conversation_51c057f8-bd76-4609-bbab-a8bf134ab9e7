import { SupabaseClient } from '@supabase/supabase-js';
import { checkBPOTeamMembership } from './auth-utils';
import type { Database } from '@/types/database.types';

/**
 * Standard function to check BPO membership that can be used across all BPO pages
 * This uses the improved error handling from auth-utils.ts
 * 
 * @param supabase Supabase client
 * @returns Object containing userId, bpoId, and error information
 */
export async function checkBPOMembership(supabase: SupabaseClient<Database>) {
  try {
    // First get the current user session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return {
        authorized: false,
        userId: null,
        bpoId: null,
        error: new Error('No active session'),
        policyError: false
      };
    }
    
    const userId = session.user.id;
    
    // Use the improved membership check function
    const result = await checkBPOTeamMembership(supabase, userId);
    
    return {
      authorized: result.isMember && !!result.bpoId,
      userId,
      bpoId: result.bpoId,
      error: result.error,
      policyError: result.policyError,
      policyWarning: !result.error && result.policyError,
    };
  } catch (err) {
    console.error('Error in BPO membership check:', err);
    return {
      authorized: false,
      userId: null,
      bpoId: null,
      error: err instanceof Error ? err : new Error('Unknown error in BPO auth check'),
      policyError: false
    };
  }
} 