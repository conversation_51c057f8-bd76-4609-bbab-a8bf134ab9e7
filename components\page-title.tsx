"use client";

import { useEffect } from "react";

interface PageTitleProps {
  title: string;
}

export function PageTitle({ title }: PageTitleProps) {
  useEffect(() => {
    // Update document title
    const originalTitle = document.title;
    document.title = title;
    
    // Find and update header title if it exists
    const headerTitle = document.querySelector('header h1');
    if (headerTitle) {
      headerTitle.textContent = title;
    }
    
    // Cleanup function to restore original title when component unmounts
    return () => {
      document.title = originalTitle;
    };
  }, [title]);
  
  // This component doesn't render anything
  return null;
}
