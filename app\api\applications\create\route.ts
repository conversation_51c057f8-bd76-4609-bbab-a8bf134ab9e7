import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // Create admin client to bypass RLS for application creation
    const adminClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    )
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const { jobId } = await request.json()
    
    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      )
    }

    // Get the prospect ID for the current user
    const { data: prospect, error: prospectError } = await supabase
      .from('prospects')
      .select('id')
      .eq('user_id', session.user.id)
      .single()

    if (prospectError || !prospect) {
      return NextResponse.json(
        { error: 'Prospect profile not found' },
        { status: 404 }
      )
    }

    // Get the job posting to verify it exists
    const { data: jobPosting, error: jobError } = await supabase
      .from('job_postings')
      .select('id, title, status')
      .eq('id', jobId)
      .eq('status', 'published')
      .single()

    if (jobError || !jobPosting) {
      return NextResponse.json(
        { error: 'Job posting not found or no longer available' },
        { status: 404 }
      )
    }

    // Check if application already exists
    const { data: existingApplication, error: checkError } = await supabase
      .from('applications')
      .select('id')
      .eq('job_id', jobId)
      .eq('prospect_id', prospect.id)
      .single()

    if (existingApplication) {
      return NextResponse.json(
        { error: 'You have already applied for this position' },
        { status: 409 }
      )
    }

    // Create the application using admin client to bypass RLS
    const applicationData = {
      job_id: jobId,
      prospect_id: prospect.id,
      status: 'submitted',
      submitted_at: new Date().toISOString()
    }

    console.log('Creating application with data:', applicationData)

    const { data: application, error: applicationError } = await adminClient
      .from('applications')
      .insert(applicationData)
      .select('id, job_id, prospect_id, status, submitted_at')
      .single()

    console.log('Insert result:', { data: application, error: applicationError })

    if (applicationError) {
      console.error('Error creating application:', applicationError)
      console.error('Application error details:', JSON.stringify(applicationError, null, 2))
      return NextResponse.json(
        { error: `Failed to submit application: ${applicationError.message}` },
        { status: 500 }
      )
    }

    if (!application) {
      console.error('No application data returned from insert')
      return NextResponse.json(
        { error: 'Failed to submit application: No data returned' },
        { status: 500 }
      )
    }

    // TODO: Send notification to BPO about new application
    // This could be an email notification or in-app notification

    return NextResponse.json({
      success: true,
      applicationId: application.id,
      message: 'Application submitted successfully',
      publicProfileUrl: `/prospect/public/${prospect.id}`
    })

  } catch (error) {
    console.error('Error in application creation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
