-- =============================================================================
-- MIGRATION: Add missing RLS policies for prospects table
-- =============================================================================

-- This migration adds the missing RLS policies for the prospects table
-- which was causing the applications queries to fail

-- =============================================================================
-- PROSPECTS TABLE POLICIES
-- =============================================================================

-- Platform admins can do everything
CREATE POLICY "platform_admins_all_prospects" ON public.prospects
FOR ALL
TO authenticated
USING (public.is_platform_admin(auth.uid()))
WITH CHECK (public.is_platform_admin(auth.uid()));

-- Users can view and update their own prospect profile
CREATE POLICY "users_own_prospect_profile" ON public.prospects
FOR ALL
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- BPO admins can view prospect profiles for applications to their job postings
CREATE POLICY "bpo_admins_view_applicant_prospects" ON public.prospects
FOR SELECT
TO authenticated
USING (id IN (
  SELECT a.prospect_id FROM public.applications a
  JOIN public.job_postings jp ON jp.id = a.job_id
  WHERE public.is_bpo_admin(auth.uid(), jp.bpo_id)
));

-- =============================================================================
-- ADDITIONAL USERS TABLE POLICY
-- =============================================================================

-- BPO admins can view user data for applicants to their job postings
CREATE POLICY "bpo_admins_view_applicant_users" ON public.users
FOR SELECT
TO authenticated
USING (id IN (
  SELECT p.user_id FROM public.prospects p
  JOIN public.applications a ON a.prospect_id = p.id
  JOIN public.job_postings jp ON jp.id = a.job_id
  WHERE public.is_bpo_admin(auth.uid(), jp.bpo_id)
));

-- =============================================================================
-- VERIFICATION
-- =============================================================================

-- Verify that the policies were created successfully
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE tablename IN ('prospects', 'users') 
  AND policyname IN (
    'platform_admins_all_prospects',
    'users_own_prospect_profile', 
    'bpo_admins_view_applicant_prospects',
    'bpo_admins_view_applicant_users'
  )
ORDER BY tablename, policyname;
