'use client';

import { useEffect, useState } from 'react';

export function AppInitializer() {
  const [initStatus, setInitStatus] = useState<'loading' | 'success' | 'error'>('loading');
  
  useEffect(() => {
    // Only run in production or when explicitly allowed
    if (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_RUN_MIGRATIONS === 'true') {
      fetch('/api/setup')
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            console.log('Database schema verified');
            setInitStatus('success');
          } else {
            console.error('Database schema verification failed:', data.error);
            setInitStatus('error');
          }
        })
        .catch(err => {
          console.error('Error checking database schema:', err);
          setInitStatus('error');
        });
    } else {
      // Skip in development unless explicitly enabled
      setInitStatus('success');
    }
  }, []);
  
  // This component doesn't render anything visible
  return null;
} 