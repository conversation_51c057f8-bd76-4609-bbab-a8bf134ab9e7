'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, <PERSON>Footer, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Clock, AlertCircle, Home, ArrowLeft, ArrowRight } from 'lucide-react';
import { QuestionRenderer } from '@/components/assessment/question-renderer';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/components/ui/use-toast';
import { 
  getAssessmentById, 
  getAssessmentQuestions, 
  completeAssessment, 
  abandonAssessment 
} from '@/lib/api/assessments';
import { saveAssessmentAnswer } from '@/lib/api/assessment-answers';
import { Assessment, AssessmentQuestion } from '@/types/assessment';

interface AssessmentTakeProps {
  assessmentId: string;
}

export function AssessmentTake({ assessmentId }: AssessmentTakeProps) {
  const [assessment, setAssessment] = useState<Assessment | null>(null);
  const [questions, setQuestions] = useState<AssessmentQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [answerData, setAnswerData] = useState<Record<string, any>>({});
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [startTime] = useState(Date.now());
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const { toast } = useToast();

  // Load assessment and questions
  useEffect(() => {
    async function loadAssessment() {
      if (authLoading) {
        // Still determining auth state, don't do anything yet
        return;
      }
      
      if (!user) {
        setError('You must be logged in to take an assessment');
        setLoading(false);
        return;
      }

      try {
        const assessmentData = await getAssessmentById(assessmentId);
        if (!assessmentData) {
          throw new Error('Assessment not found');
        }
        
        setAssessment(assessmentData);
        
        // Convert duration string to seconds
        const durationMatch = assessmentData.duration.match(/(\d+)\s*min/);
        if (durationMatch) {
          const minutes = parseInt(durationMatch[1], 10);
          setTimeRemaining(minutes * 60);
        } else {
          // Default to 30 minutes if we can't parse
          setTimeRemaining(30 * 60);
        }
        
        const questionsData = await getAssessmentQuestions(assessmentId);
        setQuestions(questionsData);
      } catch (err: any) {
        console.error('Error loading assessment:', err);
        setError(err.message || 'Failed to load assessment');
      } finally {
        setLoading(false);
      }
    }
    
    loadAssessment();
  }, [assessmentId, user, authLoading]);

  // Timer
  useEffect(() => {
    if (!timeRemaining || loading) return;
    
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          handleSubmitAssessment();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    return () => clearInterval(timer);
  }, [timeRemaining, loading]);

  const handleAnswer = async (questionId: string, answer: string, additionalData?: any) => {
    // First update the local state
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
    
    if (additionalData) {
      setAnswerData(prev => ({
        ...prev,
        [questionId]: additionalData
      }));
    }
    
    // Then try to save to the database, but don't block the UI
    if (user && assessment) {
      try {
        await saveAssessmentAnswer(
          assessment.id,
          questionId,
          user.id,
          answer,
          additionalData
        );
      } catch (err: any) {
        console.error('Error saving answer:', err);
        // We still show the error but don't block the user from continuing
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'There was an issue saving your answer, but you can continue. Your progress is saved locally.',
        });
      }
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else if (currentQuestionIndex === questions.length - 1) {
      // Last question, prompt to submit assessment
      if (Object.keys(answers).length === questions.length) {
        handleSubmitAssessment();
      } else {
        toast({
          title: 'Not all questions answered',
          description: 'Please answer all questions before submitting.',
        });
      }
    }
  };

  const handlePrevQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const calculateScore = () => {
    if (questions.length === 0) return 0;
    
    let totalPoints = 0;
    let earnedPoints = 0;
    
    questions.forEach(question => {
      totalPoints += question.points;
      
      // For multiple choice questions with a correct answer
      if (
        question.question_type === 'multiple_choice' && 
        question.correct_answer && 
        answers[question.id] === question.correct_answer
      ) {
        earnedPoints += question.points;
      }
      
      // For typing tests, use accuracy as score basis
      if (
        question.question_type === 'typing_test' && 
        answerData[question.id] && 
        answerData[question.id].accuracy
      ) {
        // Convert accuracy percentage to points
        const accuracyPoints = (answerData[question.id].accuracy / 100) * question.points;
        earnedPoints += accuracyPoints;
      }
      
      // For text_input, we'll have to score manually or count it as full points for now
      if (question.question_type === 'text_input' && answers[question.id]) {
        // For now, give full points if they answered the question
        // In a real system, this would be manually reviewed or analyzed
        earnedPoints += question.points;
      }
    });
    
    // Calculate percentage
    return Math.round((earnedPoints / totalPoints) * 100);
  };

  const handleSubmitAssessment = async () => {
    if (!user || !assessment) return;
    
    setIsSubmitting(true);
    try {
      // Calculate completion time in seconds
      const completionTime = Math.floor((Date.now() - startTime) / 1000);
      
      // Calculate score based on answers
      const score = calculateScore();
      
      // Submit completion
      await completeAssessment(assessment.id, user.id, score, completionTime);
      
      // Redirect to results page - Admin route for admin users
      router.replace(`/admin/assessments/${assessmentId}/results`);
    } catch (err: any) {
      setError('Failed to submit assessment. Please try again.');
      console.error(err);
      setIsSubmitting(false);
    }
  };

  const handleAbandonAssessment = async () => {
    if (!user || !assessment) return;
    
    try {
      await abandonAssessment(assessment.id, user.id);
      // Redirect to admin assessments page
      router.replace('/admin/assessments');
    } catch (err: any) {
      console.error(err);
    }
  };

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-4">
        <Card>
          <CardHeader>
            <div className="h-8 w-3/4 bg-gray-200 rounded animate-pulse"></div>
          </CardHeader>
          <CardContent>
            <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4">
          <Button onClick={() => router.push('/admin/assessments')}>
            <Home className="mr-2 h-4 w-4" /> Back to Assessments
          </Button>
        </div>
      </div>
    );
  }
  
  const currentQuestion = questions[currentQuestionIndex];

  return (
    <div className="max-w-4xl mx-auto p-4">
      <Card>
        <CardHeader className="border-b">
          <CardTitle className="flex justify-between items-center">
            <div>{assessment?.title}</div>
            <div className="flex items-center">
              <Clock className="mr-2 h-4 w-4" />
              <span>{formatTime(timeRemaining)}</span>
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="pt-6">
          <div className="mb-4">
            <Progress value={((currentQuestionIndex + 1) / questions.length) * 100} />
            <div className="text-sm text-gray-500 mt-1">
              Question {currentQuestionIndex + 1} of {questions.length}
            </div>
          </div>
          
          {currentQuestion ? (
            <div>
              <QuestionRenderer
                question={currentQuestion}
                onAnswer={(answer, additionalData) => 
                  handleAnswer(currentQuestion.id, answer, additionalData)
                }
                value={answers[currentQuestion.id] || ''}
                additionalData={answerData[currentQuestion.id]}
              />
            </div>
          ) : (
            <div className="py-8 text-center">
              <AlertCircle className="mx-auto h-8 w-8 text-yellow-500" />
              <h3 className="mt-2 text-lg font-semibold">No questions found</h3>
              <p className="text-gray-500">This assessment doesn't have any questions.</p>
            </div>
          )}
        </CardContent>
        
        <CardFooter className="flex justify-between border-t pt-4">
          <Button 
            variant="outline" 
            onClick={handleAbandonAssessment}
            disabled={isSubmitting}
          >
            Abandon
          </Button>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handlePrevQuestion}
              disabled={currentQuestionIndex === 0 || isSubmitting}
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Previous
            </Button>
            
            {currentQuestionIndex < questions.length - 1 ? (
              <Button
                onClick={handleNextQuestion}
                disabled={isSubmitting}
              >
                Next <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmitAssessment}
                disabled={isSubmitting}
              >
                Submit
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
} 