export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          encrypted_password: string
          full_name: string
          role: 'admin' | 'bpo_admin' | 'bpo_team' | 'prospect'
          status: 'active' | 'inactive' | 'pending_activation' | 'suspended'
          is_password_change_required: boolean
          last_login: string | null
          created_at: string
          updated_at: string
          avatar_url: string | null
          is_placeholder: boolean
        }
        Insert: {
          id?: string
          email: string
          encrypted_password: string
          full_name: string
          role: 'admin' | 'bpo_admin' | 'bpo_team' | 'prospect'
          status?: 'active' | 'inactive' | 'pending_activation' | 'suspended'
          is_password_change_required?: boolean
          last_login?: string | null
          created_at?: string
          updated_at?: string
          avatar_url?: string | null
          is_placeholder?: boolean
        }
        Update: {
          id?: string
          email?: string
          encrypted_password?: string
          full_name?: string
          role?: 'admin' | 'bpo_admin' | 'bpo_team' | 'prospect'
          status?: 'active' | 'inactive' | 'pending_activation' | 'suspended'
          is_password_change_required?: boolean
          last_login?: string | null
          created_at?: string
          updated_at?: string
          avatar_url?: string | null
          is_placeholder?: boolean
        }
      }
      prospects: {
        Row: {
          id: string
          user_id: string
          contact_info: Json
          education: Json[]
          experience: Json[]
          skills: Json[]
          intro_video_url: string | null
          resume_url: string | null
          profile_visibility: boolean
          training_status: 'not_started' | 'in_progress' | 'completed'
          created_at: string
          updated_at: string
          profile_image: string | null
        }
        Insert: {
          id?: string
          user_id: string
          contact_info?: Json
          education?: Json[]
          experience?: Json[]
          skills?: Json[]
          intro_video_url?: string | null
          resume_url?: string | null
          profile_visibility?: boolean
          training_status?: 'not_started' | 'in_progress' | 'completed'
          created_at?: string
          updated_at?: string
          profile_image?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          contact_info?: Json
          education?: Json[]
          experience?: Json[]
          skills?: Json[]
          intro_video_url?: string | null
          resume_url?: string | null
          profile_visibility?: boolean
          training_status?: 'not_started' | 'in_progress' | 'completed'
          created_at?: string
          updated_at?: string
          profile_image?: string | null
        }
      }
      bpos: {
        Row: {
          id: string
          name: string
          description: string | null
          logo_url: string | null
          industry: string | null
          size_range: string | null
          founded_year: number | null
          website_url: string | null
          location: Json
          contact_email: string | null
          created_by: string
          reviews: Json[]
          workplace_stats: Json
          created_at: string
          updated_at: string
          phone: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          logo_url?: string | null
          industry?: string | null
          size_range?: string | null
          founded_year?: number | null
          website_url?: string | null
          location?: Json
          contact_email?: string | null
          created_by: string
          reviews?: Json[]
          workplace_stats?: Json
          created_at?: string
          updated_at?: string
          phone?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          logo_url?: string | null
          industry?: string | null
          size_range?: string | null
          founded_year?: number | null
          website_url?: string | null
          location?: Json
          contact_email?: string | null
          created_by?: string
          reviews?: Json[]
          workplace_stats?: Json
          created_at?: string
          updated_at?: string
          phone?: string | null
        }
      }
      bpo_teams: {
        Row: {
          id: string
          bpo_id: string
          user_id: string
          role: 'admin' | 'recruiter' | 'interviewer'
          permissions: Json
          invited_at: string
          accepted_at: string | null
          created_at: string
          updated_at: string
          is_placeholder: boolean
          invited_by: string | null
        }
        Insert: {
          id?: string
          bpo_id: string
          user_id: string
          role: 'admin' | 'recruiter' | 'interviewer'
          permissions?: Json
          invited_at?: string
          accepted_at?: string | null
          created_at?: string
          updated_at?: string
          is_placeholder?: boolean
          invited_by?: string | null
        }
        Update: {
          id?: string
          bpo_id?: string
          user_id?: string
          role?: 'admin' | 'recruiter' | 'interviewer'
          permissions?: Json
          invited_at?: string
          accepted_at?: string | null
          created_at?: string
          updated_at?: string
          is_placeholder?: boolean
          invited_by?: string | null
        }
      }
      assessments: {
        Row: {
          id: string
          title: string
          description: string
          category: 'Critical Thinking' | 'Soft Skills' | 'Temperament' | 'Technical'
          duration: string
          instructions: string
          total_questions: number
          passing_score: number
          what_it_checks: string
          what_to_expect: string
          is_active: boolean
          completions: number
          avg_score: number
          created_at: string
          updated_at: string
          order_index: number
        }
        Insert: {
          id?: string
          title: string
          description: string
          category: 'Critical Thinking' | 'Soft Skills' | 'Temperament' | 'Technical'
          duration: string
          instructions: string
          total_questions: number
          passing_score: number
          what_it_checks: string
          what_to_expect: string
          is_active?: boolean
          completions?: number
          avg_score?: number
          created_at?: string
          updated_at?: string
          order_index?: number
        }
        Update: {
          id?: string
          title?: string
          description?: string
          category?: 'Critical Thinking' | 'Soft Skills' | 'Temperament' | 'Technical'
          duration?: string
          instructions?: string
          total_questions?: number
          passing_score?: number
          what_it_checks?: string
          what_to_expect?: string
          is_active?: boolean
          completions?: number
          avg_score?: number
          created_at?: string
          updated_at?: string
          order_index?: number
        }
      }

      assessment_questions: {
        Row: {
          id: string
          assessment_id: string
          question_text: string
          question_type: 'multiple_choice' | 'text_input' | 'typing_test'
          options: Json | null
          correct_answer: string | null
          points: number
          time_limit: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          assessment_id: string
          question_text: string
          question_type: 'multiple_choice' | 'text_input' | 'typing_test'
          options?: Json | null
          correct_answer?: string | null
          points?: number
          time_limit?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          assessment_id?: string
          question_text?: string
          question_type?: 'multiple_choice' | 'text_input' | 'typing_test'
          options?: Json | null
          correct_answer?: string | null
          points?: number
          time_limit?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      training_modules: {
        Row: {
          id: string
          title: string
          description: string
          cover_image_url: string | null
          duration_minutes: number
          required_order: number
          status: 'draft' | 'published' | 'archived'
          created_by: string
          created_at: string
          updated_at: string
          requires_assessment: boolean
        }
        Insert: {
          id?: string
          title: string
          description: string
          cover_image_url?: string | null
          duration_minutes: number
          required_order: number
          status?: 'draft' | 'published' | 'archived'
          created_by: string
          created_at?: string
          updated_at?: string
          requires_assessment?: boolean
        }
        Update: {
          id?: string
          title?: string
          description?: string
          cover_image_url?: string | null
          duration_minutes?: number
          required_order?: number
          status?: 'draft' | 'published' | 'archived'
          created_by?: string
          created_at?: string
          updated_at?: string
          requires_assessment?: boolean
        }
      }
      lessons: {
        Row: {
          id: string
          module_id: string
          title: string
          description: string
          order_index: number
          duration_minutes: number
          created_at: string
          updated_at: string
          lesson_type: 'video' | 'reading' | 'interactive'
          video_url: string | null
          media_url: string | null
        }
        Insert: {
          id?: string
          module_id: string
          title: string
          description: string
          order_index: number
          duration_minutes: number
          created_at?: string
          updated_at?: string
          lesson_type: 'video' | 'reading' | 'interactive'
          video_url?: string | null
          media_url?: string | null
        }
        Update: {
          id?: string
          module_id?: string
          title?: string
          description?: string
          order_index?: number
          duration_minutes?: number
          created_at?: string
          updated_at?: string
          lesson_type?: 'video' | 'reading' | 'interactive'
          video_url?: string | null
          media_url?: string | null
        }
      }
      activities: {
        Row: {
          id: string
          lesson_id: string
          title: string
          description: string | null
          type: 'reading' | 'quiz' | 'exercise' | 'video'
          content: Json
          content_url: string | null
          passing_criteria: Json
          order_index: number
          duration_minutes: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          lesson_id: string
          title: string
          description?: string | null
          type: 'reading' | 'quiz' | 'exercise' | 'video'
          content?: Json
          content_url?: string | null
          passing_criteria?: Json
          order_index: number
          duration_minutes?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          lesson_id?: string
          title?: string
          description?: string | null
          type?: 'reading' | 'quiz' | 'exercise' | 'video'
          content?: Json
          content_url?: string | null
          passing_criteria?: Json
          order_index?: number
          duration_minutes?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      progress_records: {
        Row: {
          id: string
          prospect_id: string
          activity_id: string
          status: 'not_started' | 'in_progress' | 'completed'
          score: number | null
          attempts: number
          completed_at: string | null
          time_spent_seconds: number
          created_at: string
          updated_at: string
          is_module_assessment: boolean
        }
        Insert: {
          id?: string
          prospect_id: string
          activity_id: string
          status?: 'not_started' | 'in_progress' | 'completed'
          score?: number | null
          attempts?: number
          completed_at?: string | null
          time_spent_seconds?: number
          created_at?: string
          updated_at?: string
          is_module_assessment?: boolean
        }
        Update: {
          id?: string
          prospect_id?: string
          activity_id?: string
          status?: 'not_started' | 'in_progress' | 'completed'
          score?: number | null
          attempts?: number
          completed_at?: string | null
          time_spent_seconds?: number
          created_at?: string
          updated_at?: string
          is_module_assessment?: boolean
        }
      }
      files: {
        Row: {
          id: string
          prospect_id: string
          module_id: string | null
          assessment_id: string | null
          file_type: string
          file_category: string
          title: string
          description: string | null
          file_url: string
          original_filename: string | null
          file_size: number | null
          mime_type: string | null
          issued_at: string | null
          expires_at: string | null
          verification_code: string | null
          is_verified: boolean | null
          metadata: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          prospect_id: string
          module_id?: string | null
          assessment_id?: string | null
          file_type?: string
          file_category?: string
          title?: string
          description?: string | null
          file_url?: string
          original_filename?: string | null
          file_size?: number | null
          mime_type?: string | null
          issued_at?: string | null
          expires_at?: string | null
          verification_code?: string | null
          is_verified?: boolean | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          prospect_id?: string
          module_id?: string | null
          assessment_id?: string | null
          file_type?: string
          file_category?: string
          title?: string
          description?: string | null
          file_url?: string
          original_filename?: string | null
          file_size?: number | null
          mime_type?: string | null
          issued_at?: string | null
          expires_at?: string | null
          verification_code?: string | null
          is_verified?: boolean | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      job_postings: {
        Row: {
          id: string
          bpo_id: string
          title: string
          description: string
          requirements: string
          responsibilities: Json[]
          location: Json
          job_type: 'full_time' | 'part_time' | 'contract' | 'remote'
          salary_range: Json
          required_skills: Json[]
          required_certifications: Json[]
          application_deadline: string
          status: 'draft' | 'published' | 'closed'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          bpo_id: string
          title: string
          description: string
          requirements: string
          responsibilities?: Json[]
          location: Json
          job_type: 'full_time' | 'part_time' | 'contract' | 'remote'
          salary_range: Json
          required_skills?: Json[]
          required_certifications?: Json[]
          application_deadline: string
          status?: 'draft' | 'published' | 'closed'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          bpo_id?: string
          title?: string
          description?: string
          requirements?: string
          responsibilities?: Json[]
          location?: Json
          job_type?: 'full_time' | 'part_time' | 'contract' | 'remote'
          salary_range?: Json
          required_skills?: Json[]
          required_certifications?: Json[]
          application_deadline?: string
          status?: 'draft' | 'published' | 'closed'
          created_at?: string
          updated_at?: string
        }
      }
      applications: {
        Row: {
          id: string
          job_id: string
          prospect_id: string
          status: 'submitted' | 'reviewed' | 'accepted' | 'rejected'
          cover_letter: string | null
          resume_url: string | null
          video_url: string | null
          notes: string | null
          submitted_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          job_id: string
          prospect_id: string
          status?: 'submitted' | 'reviewed' | 'accepted' | 'rejected'
          cover_letter?: string | null
          resume_url?: string | null
          video_url?: string | null
          notes?: string | null
          submitted_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          job_id?: string
          prospect_id?: string
          status?: 'submitted' | 'reviewed' | 'accepted' | 'rejected'
          cover_letter?: string | null
          resume_url?: string | null
          video_url?: string | null
          notes?: string | null
          submitted_at?: string
          updated_at?: string
        }
      }
      interviews: {
        Row: {
          id: string
          application_id: string
          bpo_user_id: string
          scheduled_at: string
          duration_minutes: number
          location: string
          meeting_link: string | null
          notes: string | null
          status: 'scheduled' | 'completed' | 'cancelled'
          feedback: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          application_id: string
          bpo_user_id: string
          scheduled_at: string
          duration_minutes: number
          location?: string
          meeting_link?: string | null
          notes?: string | null
          status?: 'scheduled' | 'completed' | 'cancelled'
          feedback?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          application_id?: string
          bpo_user_id?: string
          scheduled_at?: string
          duration_minutes?: number
          location?: string
          meeting_link?: string | null
          notes?: string | null
          status?: 'scheduled' | 'completed' | 'cancelled'
          feedback?: Json
          created_at?: string
          updated_at?: string
        }
      }
      ai_conversations: {
        Row: {
          id: string
          prospect_id: string
          context: Json
          messages: Json[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          prospect_id: string
          context?: Json
          messages?: Json[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          prospect_id?: string
          context?: Json
          messages?: Json[]
          created_at?: string
          updated_at?: string
        }
      }
      module_assessments: {
        Row: {
          id: string
          module_id: string
          assessment_id: string | null
          is_required: boolean
          order_index: number
          passing_required: boolean
          created_at: string
          updated_at: string
          title: string
          description: string
          category: string
          duration: string
          instructions: string
          total_questions: number
          passing_score: number
          is_active: boolean
          completions: number
          avg_score: number
          is_module_quiz: boolean | null
          image_url: string | null
        }
        Insert: {
          id?: string
          module_id: string
          assessment_id?: string | null
          is_required?: boolean
          order_index: number
          passing_required?: boolean
          created_at?: string
          updated_at?: string
          title: string
          description: string
          category: string
          duration: string
          instructions: string
          total_questions: number
          passing_score: number
          is_active?: boolean
          completions?: number
          avg_score?: number
          is_module_quiz?: boolean | null
          image_url?: string | null
        }
        Update: {
          id?: string
          module_id?: string
          assessment_id?: string | null
          is_required?: boolean
          order_index?: number
          passing_required?: boolean
          created_at?: string
          updated_at?: string
          title?: string
          description?: string
          category?: string
          duration?: string
          instructions?: string
          total_questions?: number
          passing_score?: number
          is_active?: boolean
          completions?: number
          avg_score?: number
          is_module_quiz?: boolean | null
          image_url?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
  }
}