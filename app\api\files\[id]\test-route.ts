import { NextRequest, NextResponse } from 'next/server'

// Minimal test DELETE function to see if the route works at all
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('🧪 TEST: Delete API called for ID:', params.id)
    
    return NextResponse.json({
      success: true,
      message: 'Test delete endpoint working',
      fileId: params.id,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('🧪 TEST: Delete error:', error)
    return NextResponse.json({ 
      error: 'Test endpoint error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
