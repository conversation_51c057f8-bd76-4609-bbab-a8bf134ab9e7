# 🧪 Testing Guide

Comprehensive testing strategy for the BPO Training Platform, covering unit tests, integration tests, end-to-end tests, and performance testing.

## 🎯 Testing Strategy

The platform implements **comprehensive testing coverage**:

- **Unit Tests**: Component and function testing with Jest and React Testing Library
- **Integration Tests**: API and database integration testing
- **End-to-End Tests**: User workflow testing with <PERSON><PERSON>
- **Performance Tests**: Load testing and performance benchmarking
- **Security Tests**: Vulnerability and penetration testing

## 🔧 Testing Setup

### Test Configuration

```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
    '^@/components/(.*)$': '<rootDir>/components/$1',
    '^@/lib/(.*)$': '<rootDir>/lib/$1',
    '^@/hooks/(.*)$': '<rootDir>/hooks/$1'
  },
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/e2e/'
  ],
  collectCoverageFrom: [
    'components/**/*.{js,jsx,ts,tsx}',
    'lib/**/*.{js,jsx,ts,tsx}',
    'hooks/**/*.{js,jsx,ts,tsx}',
    'app/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  testTimeout: 10000
}
```

```javascript
// jest.setup.js
import '@testing-library/jest-dom'
import { TextEncoder, TextDecoder } from 'util'

// Polyfills
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/'
  })
}))

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
      getUser: jest.fn(),
      signInWithPassword: jest.fn(),
      signUp: jest.fn(),
      signOut: jest.fn()
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
      order: jest.fn().mockReturnThis()
    }))
  }
}))

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}))
```

## 🧩 Unit Testing

### Component Testing

```typescript
// components/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('applies variant classes correctly', () => {
    render(<Button variant="destructive">Delete</Button>)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('bg-destructive')
  })

  it('shows loading state', () => {
    render(<Button loading>Loading</Button>)
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('renders with icons', () => {
    const TestIcon = () => <span data-testid="test-icon">Icon</span>
    render(
      <Button leftIcon={<TestIcon />}>
        With Icon
      </Button>
    )
    expect(screen.getByTestId('test-icon')).toBeInTheDocument()
  })
})
```

### Hook Testing

```typescript
// hooks/__tests__/useAuth.test.tsx
import { renderHook, act } from '@testing-library/react'
import { useAuth } from '@/hooks/use-auth'
import { supabase } from '@/lib/supabase'

// Mock Supabase
jest.mock('@/lib/supabase')
const mockSupabase = supabase as jest.Mocked<typeof supabase>

describe('useAuth Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('initializes with loading state', () => {
    const { result } = renderHook(() => useAuth())
    
    expect(result.current.loading).toBe(true)
    expect(result.current.user).toBe(null)
    expect(result.current.profile).toBe(null)
  })

  it('loads user session on mount', async () => {
    const mockUser = { id: '123', email: '<EMAIL>' }
    const mockProfile = { id: '123', full_name: 'Test User', role: 'prospect' }

    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: { user: mockUser } },
      error: null
    })

    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: mockProfile, error: null })
    } as any)

    const { result, waitForNextUpdate } = renderHook(() => useAuth())
    
    await waitForNextUpdate()

    expect(result.current.loading).toBe(false)
    expect(result.current.user).toEqual(mockUser)
    expect(result.current.profile).toEqual(mockProfile)
  })

  it('handles login correctly', async () => {
    const mockUser = { id: '123', email: '<EMAIL>' }
    
    mockSupabase.auth.signInWithPassword.mockResolvedValue({
      data: { user: mockUser, session: { access_token: 'token' } },
      error: null
    })

    const { result } = renderHook(() => useAuth())

    await act(async () => {
      await result.current.login('<EMAIL>', 'password')
    })

    expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password'
    })
  })

  it('handles logout correctly', async () => {
    mockSupabase.auth.signOut.mockResolvedValue({ error: null })

    const { result } = renderHook(() => useAuth())

    await act(async () => {
      await result.current.logout()
    })

    expect(mockSupabase.auth.signOut).toHaveBeenCalled()
  })
})
```

### Utility Function Testing

```typescript
// lib/__tests__/cache-manager.test.ts
import { CacheManager } from '@/lib/cache-manager'

describe('CacheManager', () => {
  let cache: CacheManager<string>

  beforeEach(() => {
    cache = new CacheManager({
      maxSize: 3,
      ttl: 1000,
      strategy: 'cache-first'
    })
  })

  it('stores and retrieves values', async () => {
    cache.set('key1', 'value1')
    const value = await cache.get('key1')
    expect(value).toBe('value1')
  })

  it('respects TTL expiration', async () => {
    cache.set('key1', 'value1')
    
    // Fast-forward time
    jest.advanceTimersByTime(1500)
    
    const value = await cache.get('key1')
    expect(value).toBe(null)
  })

  it('evicts least recently used items', () => {
    cache.set('key1', 'value1')
    cache.set('key2', 'value2')
    cache.set('key3', 'value3')
    
    // Access key1 to make it recently used
    cache.get('key1')
    
    // Add key4, should evict key2 (least recently used)
    cache.set('key4', 'value4')
    
    expect(cache.get('key1')).toBe('value1')
    expect(cache.get('key2')).toBe(null)
    expect(cache.get('key3')).toBe('value3')
    expect(cache.get('key4')).toBe('value4')
  })

  it('implements stale-while-revalidate strategy', async () => {
    const fetcher = jest.fn().mockResolvedValue('fresh-value')
    const staleCache = new CacheManager({
      maxSize: 10,
      ttl: 100,
      strategy: 'stale-while-revalidate'
    })

    // Set initial value
    staleCache.set('key1', 'stale-value')
    
    // Fast-forward past TTL
    jest.advanceTimersByTime(200)
    
    // Should return stale value immediately and revalidate in background
    const value = await staleCache.get('key1', fetcher)
    expect(value).toBe('stale-value')
    expect(fetcher).toHaveBeenCalled()
  })
})
```

## 🔗 Integration Testing

### API Route Testing

```typescript
// app/api/__tests__/auth/login.test.ts
import { createMocks } from 'node-mocks-http'
import handler from '@/app/api/auth/login/route'
import { supabase } from '@/lib/supabase'

jest.mock('@/lib/supabase')
const mockSupabase = supabase as jest.Mocked<typeof supabase>

describe('/api/auth/login', () => {
  it('successfully logs in user with valid credentials', async () => {
    const mockUser = { id: '123', email: '<EMAIL>' }
    const mockSession = { access_token: 'token', refresh_token: 'refresh' }

    mockSupabase.auth.signInWithPassword.mockResolvedValue({
      data: { user: mockUser, session: mockSession },
      error: null
    })

    const { req, res } = createMocks({
      method: 'POST',
      body: {
        email: '<EMAIL>',
        password: 'password123'
      }
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(200)
    const data = JSON.parse(res._getData())
    expect(data.success).toBe(true)
    expect(data.data.user).toEqual(mockUser)
  })

  it('returns error for invalid credentials', async () => {
    mockSupabase.auth.signInWithPassword.mockResolvedValue({
      data: { user: null, session: null },
      error: { message: 'Invalid credentials' }
    })

    const { req, res } = createMocks({
      method: 'POST',
      body: {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(401)
    const data = JSON.parse(res._getData())
    expect(data.success).toBe(false)
    expect(data.error.message).toBe('Invalid credentials')
  })

  it('validates request body', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        email: 'invalid-email',
        password: '123' // Too short
      }
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(400)
    const data = JSON.parse(res._getData())
    expect(data.success).toBe(false)
    expect(data.error).toBe('Validation error')
  })
})
```

### Database Integration Testing

```typescript
// lib/__tests__/database-integration.test.ts
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database.types'

// Use test database
const supabaseTest = createClient<Database>(
  process.env.SUPABASE_TEST_URL!,
  process.env.SUPABASE_TEST_ANON_KEY!
)

describe('Database Integration', () => {
  beforeEach(async () => {
    // Clean up test data
    await supabaseTest.from('users').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  })

  it('creates user with proper RLS policies', async () => {
    // Create test user
    const { data: authData } = await supabaseTest.auth.signUp({
      email: '<EMAIL>',
      password: 'password123'
    })

    expect(authData.user).toBeTruthy()

    // Create user profile
    const { data: userData, error } = await supabaseTest
      .from('users')
      .insert({
        id: authData.user!.id,
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'prospect'
      })
      .select()
      .single()

    expect(error).toBeNull()
    expect(userData.email).toBe('<EMAIL>')
  })

  it('enforces RLS policies correctly', async () => {
    // Try to access data without authentication
    const { data, error } = await supabaseTest
      .from('users')
      .select('*')

    // Should be empty due to RLS
    expect(data).toEqual([])
  })

  it('handles training progress correctly', async () => {
    // Create test data
    const { data: module } = await supabaseTest
      .from('training_modules')
      .insert({
        title: 'Test Module',
        description: 'Test Description',
        status: 'published'
      })
      .select()
      .single()

    expect(module).toBeTruthy()

    // Test progress tracking
    const { data: progress } = await supabaseTest
      .from('progress')
      .insert({
        prospect_id: 'test-prospect-id',
        activity_id: 'test-activity-id',
        status: 'completed',
        score: 85
      })
      .select()
      .single()

    expect(progress.score).toBe(85)
    expect(progress.status).toBe('completed')
  })
})
```

## 🎭 End-to-End Testing

### Playwright Configuration

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] }
    }
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI
  }
})
```

### E2E Test Examples

```typescript
// e2e/auth.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test('user can register and login', async ({ page }) => {
    // Navigate to registration page
    await page.goto('/register')

    // Fill registration form
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.fill('[data-testid="full-name-input"]', 'Test User')
    await page.selectOption('[data-testid="role-select"]', 'prospect')

    // Submit form
    await page.click('[data-testid="register-button"]')

    // Should redirect to dashboard
    await expect(page).toHaveURL('/prospect/dashboard')
    await expect(page.locator('[data-testid="welcome-message"]')).toContainText('Welcome, Test User')
  })

  test('user can logout', async ({ page }) => {
    // Login first
    await page.goto('/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')

    // Logout
    await page.click('[data-testid="user-menu"]')
    await page.click('[data-testid="logout-button"]')

    // Should redirect to login page
    await expect(page).toHaveURL('/login')
  })

  test('shows error for invalid credentials', async ({ page }) => {
    await page.goto('/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'wrongpassword')
    await page.click('[data-testid="login-button"]')

    await expect(page.locator('[data-testid="error-message"]')).toContainText('Invalid credentials')
  })
})
```

```typescript
// e2e/training.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Training System', () => {
  test.beforeEach(async ({ page }) => {
    // Login as prospect
    await page.goto('/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
  })

  test('prospect can view training modules', async ({ page }) => {
    await page.goto('/prospect/training')

    // Should see training modules
    await expect(page.locator('[data-testid="training-module"]')).toHaveCount.greaterThan(0)
    
    // Check module details
    const firstModule = page.locator('[data-testid="training-module"]').first()
    await expect(firstModule.locator('[data-testid="module-title"]')).toBeVisible()
    await expect(firstModule.locator('[data-testid="module-description"]')).toBeVisible()
    await expect(firstModule.locator('[data-testid="module-duration"]')).toBeVisible()
  })

  test('prospect can start training module', async ({ page }) => {
    await page.goto('/prospect/training')

    // Click on first module
    await page.click('[data-testid="training-module"]:first-child [data-testid="start-button"]')

    // Should navigate to module page
    await expect(page).toHaveURL(/\/prospect\/training\/module\//)
    await expect(page.locator('[data-testid="lesson-content"]')).toBeVisible()
  })

  test('prospect can complete lesson and track progress', async ({ page }) => {
    await page.goto('/prospect/training/module/test-module-id')

    // Complete lesson
    await page.click('[data-testid="complete-lesson-button"]')

    // Progress should update
    await expect(page.locator('[data-testid="progress-indicator"]')).toContainText('25%')

    // Should be able to proceed to next lesson
    await expect(page.locator('[data-testid="next-lesson-button"]')).toBeEnabled()
  })
})
```

## ⚡ Performance Testing

### Load Testing with Artillery

```yaml
# artillery.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 120
      arrivalRate: 50
      name: "Ramp up load"
    - duration: 300
      arrivalRate: 100
      name: "Sustained load"
  payload:
    path: "./test-data.csv"
    fields:
      - "email"
      - "password"

scenarios:
  - name: "Login and browse training"
    weight: 70
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.data.session.access_token"
              as: "token"
      - get:
          url: "/api/training/modules"
          headers:
            Authorization: "Bearer {{ token }}"
      - get:
          url: "/api/training/progress"
          headers:
            Authorization: "Bearer {{ token }}"

  - name: "Browse job postings"
    weight: 30
    flow:
      - get:
          url: "/api/jobs/postings"
      - get:
          url: "/api/jobs/postings/{{ $randomString() }}"
```

### Performance Monitoring Tests

```typescript
// e2e/performance.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Performance Tests', () => {
  test('page load performance', async ({ page }) => {
    // Start performance monitoring
    await page.goto('/prospect/dashboard')

    // Measure Web Vitals
    const webVitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals = {}
        
        // Measure LCP
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          vitals.lcp = lastEntry.startTime
        }).observe({ entryTypes: ['largest-contentful-paint'] })

        // Measure FID
        new PerformanceObserver((list) => {
          const firstInput = list.getEntries()[0]
          vitals.fid = firstInput.processingStart - firstInput.startTime
        }).observe({ entryTypes: ['first-input'] })

        // Measure CLS
        let clsValue = 0
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          }
          vitals.cls = clsValue
        }).observe({ entryTypes: ['layout-shift'] })

        setTimeout(() => resolve(vitals), 5000)
      })
    })

    // Assert performance thresholds
    expect(webVitals.lcp).toBeLessThan(2500) // LCP < 2.5s
    expect(webVitals.fid).toBeLessThan(100)  // FID < 100ms
    expect(webVitals.cls).toBeLessThan(0.1)  // CLS < 0.1
  })

  test('API response times', async ({ page }) => {
    await page.goto('/prospect/training')

    // Monitor network requests
    const responses = []
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        responses.push({
          url: response.url(),
          status: response.status(),
          timing: response.timing()
        })
      }
    })

    // Trigger API calls
    await page.reload()
    await page.waitForLoadState('networkidle')

    // Assert API response times
    for (const response of responses) {
      expect(response.timing.responseEnd - response.timing.requestStart).toBeLessThan(1000)
    }
  })
})
```

## 🔒 Security Testing

### Security Test Suite

```typescript
// e2e/security.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Security Tests', () => {
  test('prevents XSS attacks', async ({ page }) => {
    await page.goto('/login')

    // Try XSS in input fields
    const xssPayload = '<script>alert("XSS")</script>'
    await page.fill('[data-testid="email-input"]', xssPayload)
    await page.fill('[data-testid="password-input"]', 'password')
    await page.click('[data-testid="login-button"]')

    // Should not execute script
    page.on('dialog', dialog => {
      test.fail('XSS vulnerability detected')
    })

    // Input should be sanitized
    const emailValue = await page.inputValue('[data-testid="email-input"]')
    expect(emailValue).not.toContain('<script>')
  })

  test('enforces authentication on protected routes', async ({ page }) => {
    // Try to access protected route without authentication
    await page.goto('/admin/dashboard')

    // Should redirect to login
    await expect(page).toHaveURL('/login')
  })

  test('prevents CSRF attacks', async ({ page, request }) => {
    // Try to make request without proper CSRF token
    const response = await request.post('/api/users/profile', {
      data: { full_name: 'Hacked Name' }
    })

    expect(response.status()).toBe(403)
  })

  test('rate limiting works correctly', async ({ page, request }) => {
    // Make multiple rapid requests
    const promises = Array(10).fill(null).map(() =>
      request.post('/api/auth/login', {
        data: { email: '<EMAIL>', password: 'wrong' }
      })
    )

    const responses = await Promise.all(promises)
    const rateLimitedResponses = responses.filter(r => r.status() === 429)

    expect(rateLimitedResponses.length).toBeGreaterThan(0)
  })
})
```

## 📊 Test Reporting

### Coverage Reports

```javascript
// scripts/test-coverage.js
const { execSync } = require('child_process')

// Run tests with coverage
execSync('jest --coverage --coverageReporters=text-lcov --coverageReporters=html', {
  stdio: 'inherit'
})

// Generate coverage badge
execSync('npx coverage-badges-cli --output coverage/badges', {
  stdio: 'inherit'
})

console.log('Coverage report generated at coverage/lcov-report/index.html')
```

### Test Automation

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run test:unit
      - run: npm run test:coverage
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npx playwright install
      - run: npm run test:e2e
      
      - uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/
```

---

**Next**: Learn about [Production Setup](../deployment/production-setup.md) for deployment strategies.
