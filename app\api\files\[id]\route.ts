import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { Database } from '@/types/database.types'

// UPDATE file
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('🔄 Update file API called for ID:', params.id)
    
    const supabase = createRouteHandlerClient<Database>({ cookies })

    // Check authentication
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user data
    const { data: userData } = await supabase
      .from('users')
      .select('id, role')
      .eq('id', user.id)
      .single()

    if (!userData || userData.role !== 'prospect') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get prospect data
    const { data: prospectData } = await supabase
      .from('prospects')
      .select('id')
      .eq('user_id', userData.id)
      .single()

    if (!prospectData) {
      return NextResponse.json({ error: 'Prospect not found' }, { status: 404 })
    }

    // Parse request body
    const { title } = await request.json()

    if (!title || !title.trim()) {
      return NextResponse.json({ error: 'Title is required' }, { status: 400 })
    }

    // Use service role for database operations
    const supabaseServiceRole = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Verify file belongs to this prospect
    const { data: existingFile, error: fileCheckError } = await supabaseServiceRole
      .from('files')
      .select('id, prospect_id')
      .eq('id', params.id)
      .eq('prospect_id', prospectData.id)
      .single()

    if (fileCheckError || !existingFile) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }

    // Update the file
    const { data: updatedFile, error: updateError } = await supabaseServiceRole
      .from('files')
      .update({
        title: title.trim(),
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .select()
      .single()

    if (updateError) {
      console.error('Update error:', updateError)
      return NextResponse.json({ 
        error: 'Failed to update file',
        details: updateError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      file: updatedFile
    })

  } catch (error) {
    console.error('Update file error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// DELETE file
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Add immediate logging to see if we even get here
  console.log('🗑️ DELETE API ENTRY POINT - File ID:', params?.id)
  console.log('🗑️ Request method:', request.method)
  console.log('🗑️ Request URL:', request.url)

  try {
    console.log('🗑️ Delete file API called for ID:', params.id)

    // Check environment variables first
    console.log('🔍 Checking environment variables...')
    const hasUrl = !!process.env.NEXT_PUBLIC_SUPABASE_URL
    const hasServiceKey = !!process.env.SUPABASE_SERVICE_ROLE_KEY

    console.log('🔍 Environment check:', { hasUrl, hasServiceKey })

    if (!hasUrl || !hasServiceKey) {
      console.error('❌ Missing environment variables:', { hasUrl, hasServiceKey })
      return NextResponse.json({
        error: 'Server configuration error',
        details: 'Missing required environment variables'
      }, { status: 500 })
    }

    console.log('✅ Environment variables OK')

    const supabase = createRouteHandlerClient<Database>({ cookies })

    // Check authentication
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      console.error('Authentication error:', userError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user data
    console.log('🔍 Getting user data for user ID:', user.id)
    const { data: userData, error: userDataError } = await supabase
      .from('users')
      .select('id, role')
      .eq('id', user.id)
      .single()

    if (userDataError) {
      console.error('User data error:', userDataError)
      return NextResponse.json({
        error: 'Failed to get user data',
        details: userDataError.message
      }, { status: 500 })
    }

    if (!userData || userData.role !== 'prospect') {
      console.error('User authorization failed:', { userData, role: userData?.role })
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get prospect data
    console.log('🔍 Getting prospect data for user ID:', userData.id)
    const { data: prospectData, error: prospectError } = await supabase
      .from('prospects')
      .select('id')
      .eq('user_id', userData.id)
      .single()

    if (prospectError) {
      console.error('Prospect data error:', prospectError)
      return NextResponse.json({
        error: 'Failed to get prospect data',
        details: prospectError.message
      }, { status: 500 })
    }

    if (!prospectData) {
      console.error('No prospect found for user ID:', userData.id)
      return NextResponse.json({ error: 'Prospect not found' }, { status: 404 })
    }

    // Use service role for database and storage operations
    console.log('🔧 Creating service role client...')
    const supabaseServiceRole = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Get file data before deletion
    console.log('🔍 Looking for file to delete:', { fileId: params.id, prospectId: prospectData.id })
    const { data: fileToDelete, error: fileCheckError } = await supabaseServiceRole
      .from('files')
      .select('id, prospect_id, file_url, original_filename')
      .eq('id', params.id)
      .eq('prospect_id', prospectData.id)
      .single()

    if (fileCheckError) {
      console.error('File check error:', fileCheckError)
      return NextResponse.json({
        error: 'Failed to find file',
        details: fileCheckError.message
      }, { status: 500 })
    }

    if (!fileToDelete) {
      console.error('File not found:', { fileId: params.id, prospectId: prospectData.id })
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }

    console.log('✅ File found for deletion:', fileToDelete)

    // Extract file path from URL for storage deletion
    let filePath = ''
    if (fileToDelete.file_url) {
      const urlParts = fileToDelete.file_url.split('/files/')
      if (urlParts.length > 1) {
        filePath = urlParts[1]
      }
    }

    // Delete from database first
    const { error: deleteError } = await supabaseServiceRole
      .from('files')
      .delete()
      .eq('id', params.id)

    if (deleteError) {
      console.error('Database delete error:', deleteError)
      return NextResponse.json({ 
        error: 'Failed to delete file record',
        details: deleteError.message
      }, { status: 500 })
    }

    // Delete from storage if file path exists
    if (filePath) {
      try {
        const { error: storageDeleteError } = await supabaseServiceRole.storage
          .from('files')
          .remove([filePath])

        if (storageDeleteError) {
          console.error('Storage delete error:', storageDeleteError)
          // Don't fail the request if storage deletion fails
          // The database record is already deleted
        }
      } catch (storageError) {
        console.error('Storage deletion failed:', storageError)
        // Continue - database record is deleted
      }
    }

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully'
    })

  } catch (error) {
    console.error('Delete file error:', error)
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}
