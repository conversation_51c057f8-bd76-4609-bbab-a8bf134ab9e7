import { NextRequest } from 'next/server';
import { requirePlatformAdmin } from '@/lib/auth';
import { createAdminClient } from '@/lib/supabase-server';
import {
  createApiSuccessResponse,
  handleApiError,
  withApiError<PERSON>and<PERSON>
} from '@/lib/api-error-handler';

export async function GET(req: NextRequest) {
  return withApiErrorHandler(async () => {
    // Authenticate user and require admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      throw new Error('Platform admin access required');
    }

    // Create admin client that bypasses RLS
    const adminClient = createAdminClient();

    // Get total users count
    const { count: totalUsers, error: usersError } = await adminClient
      .from('users')
      .select('*', { count: 'exact', head: true });

    if (usersError) {
      throw usersError; // Will be handled by handleSupabaseApiError
    }

    // Get total prospects count
    const { count: totalProspects, error: prospectsError } = await adminClient
      .from('prospects')
      .select('*', { count: 'exact', head: true });

    if (prospectsError) {
      throw prospectsError; // Will be handled by handleSupabaseApiError
    }

    // Get total BPOs count
    const { count: totalBPOs, error: bposError } = await adminClient
      .from('bpos')
      .select('*', { count: 'exact', head: true });

    if (bposError) {
      throw bposError; // Will be handled by handleSupabaseApiError
    }

    return {
      stats: {
        totalUsers: totalUsers || 0,
        totalProspects: totalProspects || 0,
        totalBPOs: totalBPOs || 0
      }
    };
  }, 'GET /api/admin/stats')();
}