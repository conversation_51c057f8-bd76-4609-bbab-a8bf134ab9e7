# 🔒 Security Best Practices

Comprehensive security guide for the BPO Training Platform, covering authentication, data protection, API security, and compliance requirements.

## 🛡️ Security Overview

The platform implements **defense-in-depth security**:

- **Authentication & Authorization**: Multi-factor auth with role-based access
- **Data Protection**: Encryption at rest and in transit
- **API Security**: Rate limiting, validation, and audit logging
- **Infrastructure Security**: HTTPS, security headers, and monitoring

## 🔐 Authentication Security

### Password Security

```typescript
// Strong password requirements
const passwordRequirements = {
  minLength: 8,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  preventCommonPasswords: true,
  preventUserInfoInPassword: true
}

// Password validation
const validatePassword = (password: string, userInfo?: UserInfo): ValidationResult => {
  const errors: string[] = []

  // Length check
  if (password.length < passwordRequirements.minLength) {
    errors.push(`Password must be at least ${passwordRequirements.minLength} characters`)
  }

  if (password.length > passwordRequirements.maxLength) {
    errors.push(`Password must be no more than ${passwordRequirements.maxLength} characters`)
  }

  // Character requirements
  if (passwordRequirements.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }

  if (passwordRequirements.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }

  if (passwordRequirements.requireNumbers && !/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number')
  }

  if (passwordRequirements.requireSpecialChars && !/[^A-Za-z0-9]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }

  // Common password check
  if (passwordRequirements.preventCommonPasswords && isCommonPassword(password)) {
    errors.push('Password is too common, please choose a stronger password')
  }

  // User info check
  if (passwordRequirements.preventUserInfoInPassword && userInfo) {
    const userInfoLower = [
      userInfo.email?.toLowerCase(),
      userInfo.full_name?.toLowerCase(),
      userInfo.email?.split('@')[0]?.toLowerCase()
    ].filter(Boolean)

    const passwordLower = password.toLowerCase()
    
    for (const info of userInfoLower) {
      if (info && passwordLower.includes(info)) {
        errors.push('Password cannot contain your personal information')
        break
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength: calculatePasswordStrength(password)
  }
}

// Password strength calculation
const calculatePasswordStrength = (password: string): PasswordStrength => {
  let score = 0
  
  // Length bonus
  score += Math.min(password.length * 2, 20)
  
  // Character variety bonus
  if (/[a-z]/.test(password)) score += 5
  if (/[A-Z]/.test(password)) score += 5
  if (/[0-9]/.test(password)) score += 5
  if (/[^A-Za-z0-9]/.test(password)) score += 10
  
  // Pattern penalties
  if (/(.)\1{2,}/.test(password)) score -= 10 // Repeated characters
  if (/123|abc|qwe/i.test(password)) score -= 10 // Sequential patterns
  
  if (score < 30) return 'weak'
  if (score < 60) return 'medium'
  if (score < 90) return 'strong'
  return 'very-strong'
}
```

### Multi-Factor Authentication

```typescript
// MFA implementation
const setupMFA = async (userId: string, method: 'totp' | 'sms' | 'email') => {
  switch (method) {
    case 'totp':
      return setupTOTP(userId)
    case 'sms':
      return setupSMS(userId)
    case 'email':
      return setupEmailMFA(userId)
  }
}

// TOTP setup
const setupTOTP = async (userId: string) => {
  const secret = generateTOTPSecret()
  const qrCode = generateQRCode(secret, userId)
  
  // Store secret temporarily (user must verify)
  await supabase
    .from('mfa_setup')
    .insert({
      user_id: userId,
      method: 'totp',
      secret: encrypt(secret),
      verified: false,
      expires_at: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
    })

  return {
    secret,
    qrCode,
    backupCodes: generateBackupCodes()
  }
}

// MFA verification
const verifyMFA = async (userId: string, code: string, method: string) => {
  const { data: mfaData } = await supabase
    .from('user_mfa')
    .select('*')
    .eq('user_id', userId)
    .eq('method', method)
    .eq('active', true)
    .single()

  if (!mfaData) {
    throw new Error('MFA not configured')
  }

  let isValid = false

  switch (method) {
    case 'totp':
      isValid = verifyTOTPCode(decrypt(mfaData.secret), code)
      break
    case 'sms':
      isValid = await verifySMSCode(userId, code)
      break
    case 'email':
      isValid = await verifyEmailCode(userId, code)
      break
  }

  if (isValid) {
    // Log successful MFA verification
    await logSecurityEvent(userId, 'mfa_verification_success', { method })
  } else {
    // Log failed attempt
    await logSecurityEvent(userId, 'mfa_verification_failed', { method })
    
    // Check for brute force attempts
    await checkBruteForceAttempts(userId, 'mfa_verification')
  }

  return isValid
}
```

### Session Security

```typescript
// Secure session management
const sessionConfig = {
  maxAge: 24 * 60 * 60, // 24 hours
  refreshThreshold: 15 * 60, // Refresh if expires in 15 minutes
  maxConcurrentSessions: 3,
  requireReauthForSensitive: true,
  logoutOnSuspiciousActivity: true
}

// Session validation middleware
const validateSession = async (req: NextApiRequest, res: NextApiResponse, next: NextFunction) => {
  const token = req.headers.authorization?.replace('Bearer ', '')
  
  if (!token) {
    return res.status(401).json({ error: 'No token provided' })
  }

  try {
    // Verify JWT token
    const { data: { user }, error } = await supabase.auth.getUser(token)
    
    if (error || !user) {
      return res.status(401).json({ error: 'Invalid token' })
    }

    // Check session in database
    const { data: session } = await supabase
      .from('user_sessions')
      .select('*')
      .eq('user_id', user.id)
      .eq('token_hash', hashToken(token))
      .eq('active', true)
      .single()

    if (!session) {
      return res.status(401).json({ error: 'Session not found' })
    }

    // Check session expiry
    if (new Date(session.expires_at) < new Date()) {
      await invalidateSession(session.id)
      return res.status(401).json({ error: 'Session expired' })
    }

    // Check for suspicious activity
    const suspiciousActivity = await detectSuspiciousActivity(user.id, req)
    
    if (suspiciousActivity.risk === 'high') {
      await invalidateAllUserSessions(user.id)
      await logSecurityEvent(user.id, 'suspicious_activity_detected', suspiciousActivity)
      return res.status(401).json({ error: 'Session terminated due to suspicious activity' })
    }

    // Update session activity
    await updateSessionActivity(session.id, req)

    req.user = user
    req.session = session
    next()
  } catch (error) {
    return res.status(500).json({ error: 'Session validation error' })
  }
}

// Suspicious activity detection
const detectSuspiciousActivity = async (userId: string, req: NextApiRequest) => {
  const checks = [
    checkIPAddress(userId, req.ip),
    checkUserAgent(userId, req.headers['user-agent']),
    checkRequestFrequency(userId),
    checkGeolocation(userId, req.ip),
    checkDeviceFingerprint(userId, req.headers)
  ]

  const results = await Promise.all(checks)
  const riskScore = results.reduce((total, result) => total + result.score, 0)

  return {
    risk: riskScore > 80 ? 'high' : riskScore > 50 ? 'medium' : 'low',
    score: riskScore,
    factors: results.filter(r => r.score > 0)
  }
}
```

## 🔒 Data Protection

### Encryption

```typescript
// Data encryption utilities
const encryption = {
  // Encrypt sensitive data
  encrypt: (data: string, key?: string): string => {
    const encryptionKey = key || process.env.ENCRYPTION_KEY
    const cipher = crypto.createCipher('aes-256-gcm', encryptionKey)
    let encrypted = cipher.update(data, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    const authTag = cipher.getAuthTag()
    return encrypted + ':' + authTag.toString('hex')
  },

  // Decrypt sensitive data
  decrypt: (encryptedData: string, key?: string): string => {
    const encryptionKey = key || process.env.ENCRYPTION_KEY
    const [encrypted, authTag] = encryptedData.split(':')
    const decipher = crypto.createDecipher('aes-256-gcm', encryptionKey)
    decipher.setAuthTag(Buffer.from(authTag, 'hex'))
    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    return decrypted
  },

  // Hash passwords
  hashPassword: async (password: string): Promise<string> => {
    const saltRounds = 12
    return bcrypt.hash(password, saltRounds)
  },

  // Verify passwords
  verifyPassword: async (password: string, hash: string): Promise<boolean> => {
    return bcrypt.compare(password, hash)
  },

  // Generate secure tokens
  generateSecureToken: (length = 32): string => {
    return crypto.randomBytes(length).toString('hex')
  }
}

// Database encryption functions
const databaseEncryption = {
  // Encrypt before storing
  encryptField: (value: string, fieldName: string): string => {
    const fieldKey = crypto.createHash('sha256')
      .update(process.env.FIELD_ENCRYPTION_KEY + fieldName)
      .digest('hex')
    return encryption.encrypt(value, fieldKey)
  },

  // Decrypt after retrieving
  decryptField: (encryptedValue: string, fieldName: string): string => {
    const fieldKey = crypto.createHash('sha256')
      .update(process.env.FIELD_ENCRYPTION_KEY + fieldName)
      .digest('hex')
    return encryption.decrypt(encryptedValue, fieldKey)
  }
}
```

### Data Sanitization

```typescript
// Input sanitization
const sanitization = {
  // Sanitize HTML input
  sanitizeHTML: (input: string): string => {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
      ALLOWED_ATTR: []
    })
  },

  // Sanitize SQL input (use with parameterized queries)
  sanitizeSQL: (input: string): string => {
    return input.replace(/['"\\;]/g, '')
  },

  // Sanitize file names
  sanitizeFileName: (fileName: string): string => {
    return fileName
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .substring(0, 255)
  },

  // Validate email
  validateEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email) && email.length <= 254
  },

  // Validate phone number
  validatePhone: (phone: string): boolean => {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
    return phoneRegex.test(phone)
  },

  // Remove PII from logs
  removePII: (data: any): any => {
    const piiFields = ['email', 'phone', 'ssn', 'credit_card', 'password']
    const sanitized = { ...data }
    
    for (const field of piiFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]'
      }
    }
    
    return sanitized
  }
}
```

## 🌐 API Security

### Rate Limiting

```typescript
// Advanced rate limiting
const rateLimiter = {
  // Different limits for different endpoints
  limits: {
    auth: { requests: 5, window: 60 * 1000 }, // 5 requests per minute
    api: { requests: 100, window: 15 * 60 * 1000 }, // 100 requests per 15 minutes
    upload: { requests: 10, window: 60 * 1000 }, // 10 uploads per minute
    search: { requests: 50, window: 60 * 1000 } // 50 searches per minute
  },

  // Check rate limit
  checkLimit: async (key: string, limit: RateLimit): Promise<RateLimitResult> => {
    const redis = getRedisClient()
    const current = await redis.incr(key)
    
    if (current === 1) {
      await redis.expire(key, Math.ceil(limit.window / 1000))
    }
    
    const ttl = await redis.ttl(key)
    
    return {
      allowed: current <= limit.requests,
      remaining: Math.max(0, limit.requests - current),
      resetTime: Date.now() + (ttl * 1000),
      total: limit.requests
    }
  },

  // Rate limiting middleware
  middleware: (limitType: keyof typeof rateLimiter.limits) => {
    return async (req: NextApiRequest, res: NextApiResponse, next: NextFunction) => {
      const limit = rateLimiter.limits[limitType]
      const identifier = req.ip || req.headers['x-forwarded-for'] || 'unknown'
      const key = `rate_limit:${limitType}:${identifier}`
      
      const result = await rateLimiter.checkLimit(key, limit)
      
      // Set rate limit headers
      res.setHeader('X-RateLimit-Limit', limit.requests)
      res.setHeader('X-RateLimit-Remaining', result.remaining)
      res.setHeader('X-RateLimit-Reset', result.resetTime)
      
      if (!result.allowed) {
        return res.status(429).json({
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
        })
      }
      
      next()
    }
  }
}
```

### Input Validation

```typescript
// Comprehensive input validation
const validation = {
  // API request validation schemas
  schemas: {
    createUser: z.object({
      email: z.string().email().max(254),
      password: z.string().min(8).max(128),
      full_name: z.string().min(1).max(100),
      role: z.enum(['prospect', 'bpo_admin'])
    }),
    
    createJob: z.object({
      title: z.string().min(1).max(100),
      description: z.string().min(10).max(5000),
      employment_type: z.enum(['full_time', 'part_time', 'contract']),
      location_type: z.enum(['remote', 'on_site', 'hybrid']),
      salary_range: z.object({
        min: z.number().positive().optional(),
        max: z.number().positive().optional()
      }).optional()
    }),
    
    updateProfile: z.object({
      full_name: z.string().min(1).max(100).optional(),
      avatar_url: z.string().url().optional(),
      contact_info: z.object({
        phone: z.string().optional(),
        address: z.string().max(500).optional()
      }).optional()
    })
  },

  // Validate request body
  validateBody: (schema: z.ZodSchema) => {
    return (req: NextApiRequest, res: NextApiResponse, next: NextFunction) => {
      try {
        const validatedData = schema.parse(req.body)
        req.validatedBody = validatedData
        next()
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({
            error: 'Validation error',
            details: error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          })
        }
        return res.status(400).json({ error: 'Invalid request data' })
      }
    }
  },

  // File upload validation
  validateFileUpload: (options: FileValidationOptions) => {
    return (req: NextApiRequest, res: NextApiResponse, next: NextFunction) => {
      const files = req.files as Express.Multer.File[]
      
      if (!files || files.length === 0) {
        return res.status(400).json({ error: 'No files uploaded' })
      }
      
      for (const file of files) {
        // Check file size
        if (file.size > options.maxSize) {
          return res.status(400).json({
            error: `File ${file.originalname} exceeds maximum size of ${options.maxSize} bytes`
          })
        }
        
        // Check file type
        if (options.allowedTypes && !options.allowedTypes.includes(file.mimetype)) {
          return res.status(400).json({
            error: `File ${file.originalname} has invalid type. Allowed types: ${options.allowedTypes.join(', ')}`
          })
        }
        
        // Scan for malware (if configured)
        if (options.scanForMalware) {
          const scanResult = scanFileForMalware(file.buffer)
          if (scanResult.infected) {
            return res.status(400).json({
              error: `File ${file.originalname} failed security scan`
            })
          }
        }
      }
      
      next()
    }
  }
}
```

## 🔍 Security Monitoring

### Audit Logging

```typescript
// Comprehensive audit logging
const auditLogger = {
  // Log security events
  logSecurityEvent: async (
    userId: string,
    event: SecurityEvent,
    details: Record<string, any> = {},
    request?: NextApiRequest
  ) => {
    const logEntry = {
      user_id: userId,
      event_type: event,
      details: sanitization.removePII(details),
      ip_address: request?.ip || request?.headers['x-forwarded-for'],
      user_agent: request?.headers['user-agent'],
      timestamp: new Date().toISOString(),
      severity: getEventSeverity(event)
    }

    // Store in database
    await supabase.from('security_audit_logs').insert(logEntry)

    // Send alerts for high-severity events
    if (logEntry.severity === 'high' || logEntry.severity === 'critical') {
      await sendSecurityAlert(logEntry)
    }
  },

  // Log data access
  logDataAccess: async (
    userId: string,
    resource: string,
    action: 'read' | 'write' | 'delete',
    resourceId?: string
  ) => {
    await supabase.from('data_access_logs').insert({
      user_id: userId,
      resource_type: resource,
      resource_id: resourceId,
      action,
      timestamp: new Date().toISOString()
    })
  },

  // Log API requests
  logAPIRequest: async (req: NextApiRequest, res: NextApiResponse, duration: number) => {
    const logEntry = {
      method: req.method,
      url: req.url,
      status_code: res.statusCode,
      duration_ms: duration,
      ip_address: req.ip,
      user_agent: req.headers['user-agent'],
      user_id: req.user?.id,
      timestamp: new Date().toISOString()
    }

    await supabase.from('api_request_logs').insert(logEntry)
  }
}

// Security event severity mapping
const getEventSeverity = (event: SecurityEvent): 'low' | 'medium' | 'high' | 'critical' => {
  const severityMap = {
    login_success: 'low',
    login_failed: 'medium',
    password_changed: 'medium',
    mfa_enabled: 'medium',
    mfa_disabled: 'high',
    suspicious_activity: 'high',
    data_breach_attempt: 'critical',
    privilege_escalation: 'critical',
    account_locked: 'high',
    session_hijack_attempt: 'critical'
  }

  return severityMap[event] || 'medium'
}
```

### Vulnerability Scanning

```typescript
// Security scanning utilities
const securityScanner = {
  // Scan dependencies for vulnerabilities
  scanDependencies: async (): Promise<VulnerabilityReport> => {
    const auditResult = await exec('npm audit --json')
    const vulnerabilities = JSON.parse(auditResult.stdout)
    
    return {
      total: vulnerabilities.metadata.vulnerabilities.total,
      high: vulnerabilities.metadata.vulnerabilities.high,
      critical: vulnerabilities.metadata.vulnerabilities.critical,
      advisories: vulnerabilities.advisories
    }
  },

  // Scan uploaded files
  scanFile: async (fileBuffer: Buffer, fileName: string): Promise<ScanResult> => {
    // Check file signature
    const fileType = await FileType.fromBuffer(fileBuffer)
    if (!fileType) {
      return { safe: false, reason: 'Unknown file type' }
    }

    // Check for malicious patterns
    const maliciousPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /onload=/i,
      /onerror=/i
    ]

    const fileContent = fileBuffer.toString('utf8')
    for (const pattern of maliciousPatterns) {
      if (pattern.test(fileContent)) {
        return { safe: false, reason: 'Malicious content detected' }
      }
    }

    return { safe: true }
  },

  // Check for SQL injection patterns
  checkSQLInjection: (input: string): boolean => {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(--|\/\*|\*\/|;)/,
      /(\b(OR|AND)\b.*=.*)/i,
      /'.*OR.*'/i
    ]

    return sqlPatterns.some(pattern => pattern.test(input))
  },

  // Check for XSS patterns
  checkXSS: (input: string): boolean => {
    const xssPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe[^>]*>.*?<\/iframe>/gi,
      /<object[^>]*>.*?<\/object>/gi
    ]

    return xssPatterns.some(pattern => pattern.test(input))
  }
}
```

## 🚨 Incident Response

### Security Incident Handling

```typescript
// Incident response procedures
const incidentResponse = {
  // Detect and respond to security incidents
  handleSecurityIncident: async (incident: SecurityIncident) => {
    // Log the incident
    await auditLogger.logSecurityEvent(
      incident.userId,
      'security_incident',
      incident,
      incident.request
    )

    // Determine response based on severity
    switch (incident.severity) {
      case 'critical':
        await incidentResponse.criticalResponse(incident)
        break
      case 'high':
        await incidentResponse.highResponse(incident)
        break
      case 'medium':
        await incidentResponse.mediumResponse(incident)
        break
      default:
        await incidentResponse.lowResponse(incident)
    }
  },

  // Critical incident response
  criticalResponse: async (incident: SecurityIncident) => {
    // Immediately lock affected accounts
    if (incident.userId) {
      await lockUserAccount(incident.userId, 'security_incident')
    }

    // Invalidate all sessions for affected users
    await invalidateAllUserSessions(incident.userId)

    // Send immediate alerts to security team
    await sendCriticalSecurityAlert(incident)

    // Enable enhanced monitoring
    await enableEnhancedMonitoring(incident.userId)

    // Create incident ticket
    await createIncidentTicket(incident, 'critical')
  },

  // Automated threat mitigation
  mitigateThreat: async (threat: ThreatIndicator) => {
    switch (threat.type) {
      case 'brute_force':
        await blockIPAddress(threat.sourceIP, '1 hour')
        break
      case 'sql_injection':
        await blockIPAddress(threat.sourceIP, '24 hours')
        await alertSecurityTeam('SQL injection attempt detected')
        break
      case 'xss_attempt':
        await logXSSAttempt(threat)
        break
      case 'data_exfiltration':
        await lockUserAccount(threat.userId, 'data_exfiltration_attempt')
        await alertSecurityTeam('Data exfiltration attempt detected')
        break
    }
  }
}
```

---

**Next**: Learn about [Testing Strategies](testing.md) for comprehensive quality assurance.
