"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { JobApplicationConfirmationDialog } from "@/components/job-application-confirmation-dialog"
import { toast } from "sonner"
import {
  ArrowLeft,
  MapPin,
  Briefcase,
  Clock,
  DollarSign,
  Calendar,
  Users,
  Building,
  Globe,
  Heart,
  Share2,
  CheckCircle,
  Star,
  Award,
  Target,
  Zap,
  BookOpen,
  TrendingUp,
  Shield
} from "lucide-react"
import { cn } from "@/lib/utils"

interface JobDetailsProps {
  job: {
    id: string
    title: string
    company: string
    companyLogo: string | null
    companyDescription: string
    companyWebsite: string
    companyIndustry: string
    companySize: string
    companyFounded: number | null
    location: string
    jobType: string
    salary: string
    postedDate: string
    description: string
    requirements: string[]
    responsibilities: string[]
    skills: string[]
    isNew: boolean
    deadline: string | null
    benefits: string[]
    experienceLevel: string
    employmentType: string
  }
}

export default function JobDetailsPage({ job }: JobDetailsProps) {
  const [isSaved, setIsSaved] = useState(false)
  const [showApplyDialog, setShowApplyDialog] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleApplyNow = () => {
    setShowApplyDialog(true)
  }

  const handleConfirmApplication = async () => {
    setIsSubmitting(true)
    try {
      const response = await fetch('/api/applications/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jobId: job.id
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit application')
      }

      // Success
      toast.success('Application submitted successfully!', {
        description: `Your profile has been sent to ${job.company}. They will review your application and contact you if you're a good fit.`
      })

      setShowApplyDialog(false)

    } catch (error: any) {
      toast.error('Failed to submit application', {
        description: error.message || 'Please try again later.'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSaveJob = () => {
    setIsSaved(!isSaved)
    // Add save job logic here
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `${job.title} at ${job.company}`,
        text: `Check out this job opportunity: ${job.title} at ${job.company}`,
        url: window.location.href,
      })
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-6 max-w-5xl">
        {/* Back Button */}
        <Button
          variant="ghost"
          className="mb-6 text-muted-foreground hover:text-foreground"
          onClick={() => window.history.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Job Board
        </Button>

        {/* Job Header - Compact Design */}
        <Card className="border-none shadow-sm mb-6">
          <CardContent className="p-6">
            <div className="flex items-start gap-4 mb-4">
              <Avatar className="h-16 w-16 rounded-xl border-2 border-border">
                {job.companyLogo ? (
                  <AvatarImage src={job.companyLogo} alt={job.company} />
                ) : (
                  <AvatarFallback className="rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-lg">
                    {job.company.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                )}
              </Avatar>

              <div className="flex-1">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <div className="flex items-center gap-3 mb-2">
                      <h1 className="text-2xl md:text-3xl font-bold text-foreground">
                        {job.title}
                      </h1>
                      {job.isNew && (
                        <Badge className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                          <Zap className="h-3 w-3 mr-1" />
                          New
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-2 mb-3">
                      <Building className="h-4 w-4 text-muted-foreground" />
                      <span className="text-lg font-semibold text-foreground">{job.company}</span>
                      <div className="flex items-center text-sm text-blue-600 dark:text-blue-400 ml-2">
                        <Shield className="h-3 w-3 mr-1" />
                        <span>Verified</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleShare}
                      className="h-8 w-8 p-0"
                    >
                      <Share2 className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSaveJob}
                      className={cn(
                        "h-8 w-8 p-0",
                        isSaved && "bg-red-50 text-red-600 border-red-200 dark:bg-red-900/20"
                      )}
                    >
                      <Heart className={cn("h-3 w-3", isSaved && "fill-current")} />
                    </Button>
                  </div>
                </div>

                {/* Job Meta Info - Compact Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm mb-4">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <MapPin className="h-3 w-3" />
                    <span>{job.location}</span>
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Briefcase className="h-3 w-3" />
                    <span>{job.jobType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>{job.postedDate.replace('Posted ', '')}</span>
                  </div>
                  {job.deadline && (
                    <div className="flex items-center gap-2 text-amber-600 dark:text-amber-400">
                      <Calendar className="h-3 w-3" />
                      <span>Deadline: {job.deadline}</span>
                    </div>
                  )}
                </div>

                {/* Salary & Experience Level - Inline */}
                {(job.salary || job.experienceLevel) && (
                  <div className="flex flex-wrap items-center gap-4 p-3 bg-muted/50 rounded-lg mb-4">
                    {job.salary && (
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        <span className="font-semibold text-foreground">{job.salary}</span>
                      </div>
                    )}
                    {job.experienceLevel && (
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-muted-foreground">{job.experienceLevel}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-purple-600" />
                      <span className="font-medium text-muted-foreground">{job.employmentType}</span>
                    </div>
                  </div>
                )}

                {/* Action Buttons - Compact */}
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    size="lg"
                    className="bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 flex-1"
                    onClick={handleApplyNow}
                  >
                    <Award className="h-4 w-4 mr-2" />
                    Apply Now
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className="flex-1 sm:flex-none"
                    onClick={() => window.open(`mailto:hr@${job.company.toLowerCase().replace(/\s+/g, '')}.com?subject=Inquiry about ${job.title} position`, '_blank')}
                  >
                    <Users className="h-4 w-4 mr-2" />
                    Contact HR
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content - Single Column with Proper Text Width */}
        <div className="space-y-6">
          {/* Job Description */}
          <Card className="border-none shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Job Description
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose prose-gray dark:prose-invert max-w-4xl">
                <p className="text-muted-foreground leading-relaxed whitespace-pre-line text-base">
                  {job.description}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Requirements & Responsibilities - Side by Side */}
          <div className="grid md:grid-cols-2 gap-6">
            {job.requirements.length > 0 && (
              <Card className="border-none shadow-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Requirements
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {job.requirements.map((req, index) => (
                      <li key={index} className="flex items-start gap-3 text-muted-foreground">
                        <div className="w-1.5 h-1.5 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-sm leading-relaxed">{req}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {job.responsibilities.length > 0 && (
              <Card className="border-none shadow-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-blue-600" />
                    Key Responsibilities
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {job.responsibilities.map((resp, index) => (
                      <li key={index} className="flex items-start gap-3 text-muted-foreground">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-sm leading-relaxed">{resp}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Skills & Benefits - Side by Side */}
          <div className="grid md:grid-cols-2 gap-6">
            {/* Skills */}
            {job.skills.length > 0 && (
              <Card className="border-none shadow-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-purple-600" />
                    Required Skills
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {job.skills.map((skill, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="bg-purple-50 text-purple-700 hover:bg-purple-100 dark:bg-purple-900/30 dark:text-purple-300 px-2 py-1 text-xs"
                      >
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Benefits */}
            {job.benefits.length > 0 && (
              <Card className="border-none shadow-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2">
                    <Award className="h-5 w-5 text-amber-600" />
                    Benefits & Perks
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {job.benefits.map((benefit, index) => (
                      <li key={index} className="flex items-start gap-3 text-muted-foreground">
                        <CheckCircle className="h-3 w-3 text-green-600 mt-1 flex-shrink-0" />
                        <span className="text-sm leading-relaxed">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Company Information - Full Width */}
          <Card className="border-none shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5 text-indigo-600" />
                About {job.company}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start gap-4 mb-4">
                <Avatar className="h-16 w-16 rounded-xl border-2 border-border">
                  {job.companyLogo ? (
                    <AvatarImage src={job.companyLogo} alt={job.company} />
                  ) : (
                    <AvatarFallback className="rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-lg">
                      {job.company.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  )}
                </Avatar>
                <div className="flex-1">
                  <h3 className="font-semibold text-foreground text-lg mb-1">{job.company}</h3>
                  <p className="text-sm text-muted-foreground mb-3">{job.companyIndustry}</p>

                  {job.companyDescription && (
                    <p className="text-muted-foreground leading-relaxed mb-4 max-w-3xl">
                      {job.companyDescription}
                    </p>
                  )}

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    {job.companyIndustry && (
                      <div>
                        <span className="text-muted-foreground block">Industry</span>
                        <span className="font-medium">{job.companyIndustry}</span>
                      </div>
                    )}
                    {job.companySize && (
                      <div>
                        <span className="text-muted-foreground block">Company Size</span>
                        <span className="font-medium">{job.companySize}</span>
                      </div>
                    )}
                    {job.companyFounded && (
                      <div>
                        <span className="text-muted-foreground block">Founded</span>
                        <span className="font-medium">{job.companyFounded}</span>
                      </div>
                    )}
                    {job.companyWebsite && (
                      <div>
                        <span className="text-muted-foreground block">Website</span>
                        <Button
                          variant="link"
                          size="sm"
                          className="p-0 h-auto font-medium text-blue-600 hover:text-blue-700"
                          onClick={() => window.open(job.companyWebsite, '_blank')}
                        >
                          <Globe className="h-3 w-3 mr-1" />
                          Visit Website
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action Section - Compact */}
        <Card className="border-none shadow-sm bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 mt-6">
          <CardContent className="p-6 text-center">
            <div className="max-w-2xl mx-auto">
              <h3 className="text-xl font-bold text-foreground mb-2">Ready to Apply?</h3>
              <p className="text-muted-foreground mb-4">
                Join {job.company} and take the next step in your BPO career.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  size="lg"
                  className="bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 px-6"
                  onClick={handleApplyNow}
                >
                  <Award className="h-4 w-4 mr-2" />
                  Apply for this Position
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="px-6"
                  onClick={() => window.open(`mailto:hr@${job.company.toLowerCase().replace(/\s+/g, '')}.com?subject=Inquiry about ${job.title} position`, '_blank')}
                >
                  <Users className="h-4 w-4 mr-2" />
                  Contact HR Team
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Job Application Confirmation Dialog */}
        <JobApplicationConfirmationDialog
          isOpen={showApplyDialog}
          onClose={() => setShowApplyDialog(false)}
          onConfirm={handleConfirmApplication}
          job={{
            id: job.id,
            title: job.title,
            company: job.company,
            companyLogo: job.companyLogo
          }}
          isSubmitting={isSubmitting}
        />
      </div>
    </div>
  )
}
