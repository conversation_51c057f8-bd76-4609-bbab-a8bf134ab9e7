import { HelpCircle, Mail, MessageSquare, Phone } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

export function HelpSupportPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Help & Support</h1>
        <p className="text-muted-foreground">Get help with the platform or contact support</p>
      </div>

      <Tabs defaultValue="faq">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="faq">FAQs</TabsTrigger>
          <TabsTrigger value="contact">Contact Support</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
        </TabsList>
        
        <TabsContent value="faq" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>Find answers to common questions</CardDescription>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="item-1">
                  <AccordionTrigger>How do I reset my password?</AccordionTrigger>
                  <AccordionContent>
                    You can reset your password by clicking on the "Forgot Password" link on the login page. 
                    You will receive an email with instructions to reset your password.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-2">
                  <AccordionTrigger>How can I update my profile information?</AccordionTrigger>
                  <AccordionContent>
                    Go to the Profile page from the main navigation menu. 
                    There you can edit your personal information, upload a new profile picture, 
                    and update your resume and other details.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-3">
                  <AccordionTrigger>What are the requirements to apply for jobs?</AccordionTrigger>
                  <AccordionContent>
                    To apply for jobs, you must complete the required training modules 
                    and assessments specific to the job. Each job posting will indicate 
                    which training modules are prerequisites for applying.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-4">
                  <AccordionTrigger>How can I prepare for interviews?</AccordionTrigger>
                  <AccordionContent>
                    You can prepare for interviews by reviewing your training materials, practicing 
                    with our AI assistant in the Training section, and reviewing the interview tips in 
                    the Interviews page. We also recommend researching the company before your interview.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-5">
                  <AccordionTrigger>How long does each training module take?</AccordionTrigger>
                  <AccordionContent>
                    The time required to complete each training module varies. Basic modules typically 
                    take 2-3 hours, while advanced modules may take 5-10 hours. You can find the estimated 
                    completion time for each module on its description page.
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="contact" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Contact Support</CardTitle>
              <CardDescription>We're here to help with any questions or issues</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card className="border-none shadow-sm">
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center text-center">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 mb-4">
                        <MessageSquare className="h-6 w-6" />
                      </div>
                      <h3 className="font-medium mb-1">Live Chat</h3>
                      <p className="text-sm text-muted-foreground">Chat with our support team in real-time</p>
                      <Button className="mt-4">Start Chat</Button>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="border-none shadow-sm">
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center text-center">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-600 mb-4">
                        <Mail className="h-6 w-6" />
                      </div>
                      <h3 className="font-medium mb-1">Email Support</h3>
                      <p className="text-sm text-muted-foreground">Send us an email and we'll respond within 24 hours</p>
                      <Button className="mt-4">Email Us</Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <Card className="border-none shadow-sm">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 text-purple-600 mb-4">
                      <Phone className="h-6 w-6" />
                    </div>
                    <h3 className="font-medium mb-1">Phone Support</h3>
                    <p className="text-sm text-muted-foreground">Call us directly for urgent issues</p>
                    <p className="font-medium mt-2">+****************</p>
                    <p className="text-sm text-muted-foreground">Available Monday-Friday, 9am-5pm</p>
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="resources" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Helpful Resources</CardTitle>
              <CardDescription>Guides and documentation to help you succeed</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card className="border-none shadow-sm">
                  <CardContent className="p-6">
                    <h3 className="font-medium mb-2">Platform Guide</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      A comprehensive guide to using the BPO Training Platform
                    </p>
                    <Button variant="outline">View Guide</Button>
                  </CardContent>
                </Card>
                
                <Card className="border-none shadow-sm">
                  <CardContent className="p-6">
                    <h3 className="font-medium mb-2">Career Resources</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Resume tips, interview advice, and career planning resources
                    </p>
                    <Button variant="outline">Access Resources</Button>
                  </CardContent>
                </Card>
              </div>
              
              <Card className="border-none shadow-sm">
                <CardContent className="p-6">
                  <h3 className="font-medium mb-2">Video Tutorials</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Watch step-by-step tutorials on platform features and BPO skills
                  </p>
                  <div className="grid gap-2 md:grid-cols-3">
                    <Button variant="outline" size="sm">Platform Navigation</Button>
                    <Button variant="outline" size="sm">Training Modules</Button>
                    <Button variant="outline" size="sm">Application Process</Button>
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 