'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  User, 
  Phone, 
  Star, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Send,
  X
} from 'lucide-react'

interface JobApplicationConfirmationProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  job: {
    id: string
    title: string
    company: string
    companyLogo: string | null
  }
  isSubmitting?: boolean
}

export function JobApplicationConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  job,
  isSubmitting = false
}: JobApplicationConfirmationProps) {
  
  // Performance synopsis data (this would come from actual user data)
  const performanceSynopsis = {
    averageScore: 78,
    totalCalls: 24,
    totalHours: 4.2,
    readinessLevel: 'Intermediate',
    strongestSkill: 'Script Adherence',
    improvementArea: 'Active Listening',
    keyStrengths: [
      'Professional call opening and introduction',
      'Excellent product knowledge and presentation',
      'Calm under pressure with empathetic responses'
    ],
    areasForGrowth: [
      'Active listening and prospect engagement',
      'Confident closing and securing commitments'
    ]
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 70) return 'text-blue-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreStatus = (score: number) => {
    if (score >= 80) return 'excellent'
    if (score >= 70) return 'good'
    if (score >= 60) return 'needs-work'
    return 'poor'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent':
      case 'good':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'needs-work':
      case 'poor':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      default:
        return null
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Confirm Job Application
          </DialogTitle>
          <DialogDescription>
            Review your performance summary before sending your application to <strong>{job.company}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Job Information */}
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white font-bold">
                  {job.company.charAt(0)}
                </div>
                <div>
                  <h3 className="font-semibold">{job.title}</h3>
                  <p className="text-sm text-muted-foreground">{job.company}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* What Will Be Shared */}
          <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              Information That Will Be Shared:
            </h4>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Your complete public profile</li>
              <li>• Training performance and assessment reports</li>
              <li>• Skills, experience, and education details</li>
              <li>• Certificates and achievements</li>
            </ul>
          </div>

          {/* Performance Synopsis */}
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center gap-2 mb-4">
                <Phone className="h-5 w-5" />
                <h4 className="font-semibold">Your Call Performance Synopsis</h4>
              </div>

              {/* Overall Metrics */}
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Star className="h-4 w-4 text-yellow-600" />
                    {getStatusIcon(getScoreStatus(performanceSynopsis.averageScore))}
                  </div>
                  <p className={`text-lg font-bold ${getScoreColor(performanceSynopsis.averageScore)}`}>
                    {performanceSynopsis.averageScore}/100
                  </p>
                  <p className="text-xs text-muted-foreground">Average Score</p>
                </div>
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <Phone className="h-4 w-4 mx-auto mb-1 text-green-600" />
                  <p className="text-lg font-bold">{performanceSynopsis.totalCalls}</p>
                  <p className="text-xs text-muted-foreground">Total Calls</p>
                </div>
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <Clock className="h-4 w-4 mx-auto mb-1 text-purple-600" />
                  <p className="text-lg font-bold">{performanceSynopsis.totalHours}h</p>
                  <p className="text-xs text-muted-foreground">Practice Time</p>
                </div>
              </div>

              {/* Readiness Level */}
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm font-medium">Readiness Level:</span>
                <Badge className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">
                  {performanceSynopsis.readinessLevel}
                </Badge>
              </div>

              {/* Key Highlights */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h5 className="text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                    ✓ Key Strengths
                  </h5>
                  <ul className="text-xs space-y-1">
                    {performanceSynopsis.keyStrengths.map((strength, index) => (
                      <li key={index} className="text-muted-foreground">
                        • {strength}
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h5 className="text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    ⚡ Growth Areas
                  </h5>
                  <ul className="text-xs space-y-1">
                    {performanceSynopsis.areasForGrowth.map((area, index) => (
                      <li key={index} className="text-muted-foreground">
                        • {area}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Skills Summary */}
              <div className="mt-4 pt-4 border-t">
                <div className="flex items-center justify-between text-sm">
                  <span><strong>Strongest:</strong> {performanceSynopsis.strongestSkill}</span>
                  <span><strong>Focus:</strong> {performanceSynopsis.improvementArea}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Confirmation Message */}
          <div className="bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
            <p className="text-sm text-amber-800 dark:text-amber-200">
              <strong>By proceeding, you confirm that:</strong>
            </p>
            <ul className="text-sm text-amber-700 dark:text-amber-300 mt-2 space-y-1">
              <li>• You authorize sharing your profile and performance data with {job.company}</li>
              <li>• The information provided is accurate and up-to-date</li>
              <li>• You understand this application cannot be withdrawn once submitted</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isSubmitting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Sending Application...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Send Application
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
