const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugExactUploadData() {
  console.log('🔍 Debugging Exact Upload Data Structure...\n');

  try {
    // 1. Get the exact prospect data that would be used
    console.log('1️⃣ Getting prospect data...');
    
    const { data: prospects, error: prospectsError } = await supabase
      .from('prospects')
      .select('id, user_id')
      .limit(1);

    if (prospectsError || !prospects || prospects.length === 0) {
      console.error('❌ No prospects found:', prospectsError);
      return;
    }

    const testProspect = prospects[0];
    console.log('✅ Test prospect:', testProspect);

    // 2. Test the EXACT data structure from the upload API
    console.log('\n2️⃣ Testing exact upload API data structure...');
    
    // This is the exact same structure as in the upload API
    const fileData = {
      prospect_id: testProspect.id,
      module_id: null, // Explicitly set to null for uploaded files
      file_type: 'certificate', // This should be valid
      file_category: 'uploaded', // This should be valid
      title: 'Diploma', // This is what was in the form
      file_url: 'https://snihmpmulfxhrjjcnzau.supabase.co/storage/v1/object/public/files/test.pdf',
      original_filename: 'high-schol_diploma-1.jpg',
      file_size: 123456,
      mime_type: 'image/jpeg',
      issued_at: new Date().toISOString()
    };

    console.log('📋 Exact file data to insert:', JSON.stringify(fileData, null, 2));

    // 3. Validate each field against constraints
    console.log('\n3️⃣ Validating each field...');
    
    // Check prospect_id exists
    const { data: prospectCheck } = await supabase
      .from('prospects')
      .select('id')
      .eq('id', fileData.prospect_id)
      .single();
    
    console.log('✅ prospect_id valid:', !!prospectCheck);

    // Check file_type constraint
    const validFileTypes = ['certificate', 'document', 'training_certificate'];
    console.log('✅ file_type valid:', validFileTypes.includes(fileData.file_type));

    // Check file_category constraint  
    const validFileCategories = ['uploaded', 'system_generated'];
    console.log('✅ file_category valid:', validFileCategories.includes(fileData.file_category));

    // Check required fields
    console.log('✅ title valid:', !!fileData.title && fileData.title.length > 0);
    console.log('✅ file_url valid:', !!fileData.file_url && fileData.file_url.length > 0);

    // 4. Try the actual insert
    console.log('\n4️⃣ Attempting database insert...');
    
    const { data: insertResult, error: insertError } = await supabase
      .from('files')
      .insert(fileData)
      .select()
      .single();

    if (insertError) {
      console.error('❌ Insert failed with detailed error:');
      console.error('Message:', insertError.message);
      console.error('Details:', insertError.details);
      console.error('Hint:', insertError.hint);
      console.error('Code:', insertError.code);
      console.error('Full error object:', JSON.stringify(insertError, null, 2));
    } else {
      console.log('✅ Insert successful!', insertResult);
      
      // Clean up
      await supabase.from('files').delete().eq('id', insertResult.id);
      console.log('🧹 Test record cleaned up');
    }

    // 5. Check table structure
    console.log('\n5️⃣ Checking table structure...');
    
    const { data: tableInfo } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_schema', 'public')
      .eq('table_name', 'files')
      .order('ordinal_position');

    console.log('📋 Files table structure:');
    tableInfo?.forEach(col => {
      const required = col.is_nullable === 'NO' ? '(REQUIRED)' : '(optional)';
      console.log(`  - ${col.column_name}: ${col.data_type} ${required}`);
    });

    // 6. Check constraints
    console.log('\n6️⃣ Checking table constraints...');
    
    const { data: constraints } = await supabase
      .from('information_schema.table_constraints')
      .select('constraint_name, constraint_type')
      .eq('table_schema', 'public')
      .eq('table_name', 'files');

    console.log('📋 Table constraints:');
    constraints?.forEach(constraint => {
      console.log(`  - ${constraint.constraint_name}: ${constraint.constraint_type}`);
    });

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Run the debug
debugExactUploadData();
