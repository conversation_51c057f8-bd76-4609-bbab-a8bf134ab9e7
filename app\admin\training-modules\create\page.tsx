'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function CreateTrainingModulePage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the main training modules page which has the create functionality
    router.push('/admin/training-modules');
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h2 className="text-lg font-semibold">Redirecting...</h2>
        <p className="text-gray-600">Taking you to the training modules page.</p>
      </div>
    </div>
  );
}
