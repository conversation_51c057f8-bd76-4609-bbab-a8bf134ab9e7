'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { 
  Users, 
  Building2, 
  GraduationCap, 
  TrendingUp, 
  UserPlus, 
  FileText, 
  Settings, 
  BarChart3,
  Calendar,
  Award,
  Activity,
  Eye,
  Plus,
  ArrowUpRight,
  Clock,
  AlertTriangle,
  CheckCircle2,
  Star,
  Zap,
  Target,
  Globe,
  Database
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { AreaChart, Area, BarChart, Bar, LineChart, Line, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from 'recharts';
import { HardLogoutControl } from '@/components/admin/hard-logout-control';

// Enhanced data for more sophisticated charts
const userGrowthData = [
  { month: 'Jan', prospects: 45, bpos: 8, applications: 120, completions: 67 },
  { month: 'Feb', prospects: 67, bpos: 12, applications: 189, completions: 98 },
  { month: 'Mar', prospects: 89, bpos: 15, applications: 245, completions: 134 },
  { month: 'Apr', prospects: 123, bpos: 18, applications: 312, completions: 187 },
  { month: 'May', prospects: 156, bpos: 22, applications: 398, completions: 245 },
  { month: 'Jun', prospects: 198, bpos: 28, applications: 467, completions: 298 },
];

const modulePerformanceData = [
  { module: 'Communication Skills', completed: 342, failed: 23, avgScore: 87, satisfaction: 4.6 },
  { module: 'Customer Service', completed: 298, failed: 31, avgScore: 82, satisfaction: 4.4 },
  { module: 'Technical Skills', completed: 267, failed: 45, avgScore: 79, satisfaction: 4.2 },
  { module: 'Sales Training', completed: 389, failed: 19, avgScore: 91, satisfaction: 4.8 },
  { module: 'Problem Solving', completed: 321, failed: 28, avgScore: 85, satisfaction: 4.5 },
];

const revenueData = [
  { month: 'Jan', subscriptions: 12400, training: 8600, consulting: 5200 },
  { month: 'Feb', subscriptions: 14200, training: 9800, consulting: 6100 },
  { month: 'Mar', subscriptions: 15800, training: 11200, consulting: 7300 },
  { month: 'Apr', subscriptions: 17500, training: 12800, consulting: 8200 },
  { month: 'May', subscriptions: 19200, training: 14300, consulting: 9100 },
  { month: 'Jun', subscriptions: 21100, training: 15900, consulting: 10400 },
];

// Belize districts data for statistical display
const belizeDistrictsData = [
  { 
    name: 'Belize District', 
    users: 342, 
    prospects: 198, 
    bpos: 12, 
    color: '#3b82f6',
    growth: '+12%',
    cities: ['Belize City', 'Ladyville', 'Hattieville']
  },
  { 
    name: 'Cayo District', 
    users: 189, 
    prospects: 123, 
    bpos: 8, 
    color: '#8b5cf6',
    growth: '+8%',
    cities: ['San Ignacio', 'Benque Viejo', 'Spanish Lookout']
  },
  { 
    name: 'Orange Walk District', 
    users: 156, 
    prospects: 89, 
    bpos: 6, 
    color: '#10b981',
    growth: '+15%',
    cities: ['Orange Walk Town', 'Carmelita', 'August Pine Ridge']
  },
  { 
    name: 'Corozal District', 
    users: 134, 
    prospects: 76, 
    bpos: 5, 
    color: '#f59e0b',
    growth: '+6%',
    cities: ['Corozal Town', 'Consejo', 'Sarteneja']
  },
  { 
    name: 'Stann Creek District', 
    users: 98, 
    prospects: 54, 
    bpos: 3, 
    color: '#ef4444',
    growth: '+4%',
    cities: ['Dangriga', 'Hopkins', 'Placencia']
  },
  { 
    name: 'Toledo District', 
    users: 67, 
    prospects: 34, 
    bpos: 2, 
    color: '#6b7280',
    growth: '+2%',
    cities: ['Punta Gorda', 'Barranco', 'Blue Creek']
  },
];

const chartConfig = {
  prospects: { label: 'Prospects', color: '#8b5cf6' },
  bpos: { label: 'BPO Companies', color: '#3b82f6' },
  applications: { label: 'Applications', color: '#10b981' },
  completions: { label: 'Completions', color: '#f59e0b' },
  subscriptions: { label: 'Subscriptions', color: '#3b82f6' },
  training: { label: 'Training Revenue', color: '#10b981' },
  consulting: { label: 'Consulting', color: '#f59e0b' },
};

export default function AdminDashboard() {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalProspects: 0,
    totalBPOs: 0,
    activeTrainings: 0,
    completedAssessments: 0,
    scheduledInterviews: 0,
    monthlyRevenue: 0,
    platformUptime: 99.9,
    avgCompletionRate: 78,
    customerSatisfaction: 4.6,
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchStats() {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/stats');
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch stats');
        }
        
        const data = await response.json();
        setStats({
          ...data.stats,
          activeTrainings: 45,
          completedAssessments: 256,
          scheduledInterviews: 89,
          monthlyRevenue: 47600,
          platformUptime: 99.9,
          avgCompletionRate: 78,
          customerSatisfaction: 4.6,
        });
      } catch (error: any) {
        console.error('Error fetching stats:', error);
        setError(error.message || 'Failed to load dashboard statistics');
        // Set mock data for demo purposes
        setStats({
          totalUsers: 1247,
          totalProspects: 1089,
          totalBPOs: 158,
          activeTrainings: 45,
          completedAssessments: 1267,
          scheduledInterviews: 89,
          monthlyRevenue: 47600,
          platformUptime: 99.9,
          avgCompletionRate: 78,
          customerSatisfaction: 4.6,
        });
      } finally {
        setLoading(false);
      }
    }

    fetchStats();
  }, []);

  const quickActions = [
    {
      title: 'Add User',
      icon: UserPlus,
      href: '/admin/users/create',
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      title: 'Register BPO',
      icon: Building2,
      href: '/admin/bpos/create',
      color: 'bg-purple-600 hover:bg-purple-700',
    },
    {
      title: 'Create Training',
      icon: GraduationCap,
      href: '/admin/training-modules/create',
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      title: 'New Assessment',
      icon: Award,
      href: '/admin/assessments/create',
      color: 'bg-orange-600 hover:bg-orange-700',
    },
  ];

  // Get current time greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Welcome Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="px-6 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                {getGreeting()}, Admin! 👋
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                You're overseeing the platform brilliantly! You've successfully managed <span className="font-semibold text-blue-600">{stats.totalUsers || 0}</span> users across the platform.
              </p>
              
              {/* Key Updates */}
              <div className="flex items-center gap-6 mt-4 text-sm">
                <div className="flex items-center gap-2 text-red-600">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span>3 pending BPO approvals</span>
                </div>
                <div className="flex items-center gap-2 text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>15 new user registrations</span>
                </div>
                <div className="flex items-center gap-2 text-orange-600">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span>Platform revenue increased 12%</span>
                </div>
            </div>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="px-6 pt-6">
          <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 text-red-700 dark:text-red-300">
                <AlertTriangle className="h-5 w-5" />
                <p className="text-sm">{error}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="px-6 py-6 space-y-6">
        {/* Quick Actions - Above KPIs */}
        <div className="flex items-center gap-3">
          {quickActions.map((action, index) => (
            <Link key={index} href={action.href}>
              <Button
                variant="outline"
                className="h-9 px-4 hover:shadow-sm transition-all duration-200 hover:border-blue-300 hover:text-blue-600"
              >
                <action.icon className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">{action.title}</span>
              </Button>
            </Link>
          ))}
        </div>

        {/* KPI Cards - Exact prospect dashboard style */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Platform Users - Matches prospect dashboard exactly */}
          <Card className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
                  +12%
                </Badge>
              </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Platform Users</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {loading ? '...' : (stats.totalUsers || 0).toLocaleString()}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Total registered users
                </p>
                    </div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-blue-200 dark:bg-blue-800">
                <div className="h-full w-3/4 bg-blue-600"></div>
              </div>
            </CardContent>
          </Card>

          {/* Active Training */}
          <Card className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                  <GraduationCap className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
                <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50">
                  In progress
                </Badge>
          </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Training</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {loading ? '...' : (stats.activeTrainings || 0)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Training sessions ongoing
                </p>
            </div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-green-200 dark:bg-green-800">
                <div className="h-full w-2/3 bg-green-600"></div>
          </div>
            </CardContent>
          </Card>

          {/* BPO Partners */}
          <Card className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                  <Building2 className="h-6 w-6 text-orange-600 dark:text-orange-400" />
        </div>
                <Badge variant="outline" className="text-orange-600 border-orange-200 bg-orange-50">
                  Partners
                </Badge>
              </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">BPO Partners</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                      {loading ? '...' : (stats.totalBPOs || 0)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Registered companies
                </p>
                    </div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-orange-200 dark:bg-orange-800">
                <div className="h-full w-4/5 bg-orange-600"></div>
              </div>
            </CardContent>
          </Card>

          {/* System Health */}
          <Card className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                  <Activity className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
                <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
                  {(stats.platformUptime || 99.9)}%
                </Badge>
          </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">System Health</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  Excellent
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Platform uptime status
                </p>
            </div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-purple-200 dark:bg-purple-800">
                <div className="h-full w-full bg-purple-600"></div>
          </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue & Performance Analytics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Revenue & Performance
              </CardTitle>
              <CardDescription>Platform financial performance and key metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-80">
                <AreaChart data={revenueData}>
                  <defs>
                    <linearGradient id="revenue-gradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                    </linearGradient>
                    <linearGradient id="training-gradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                    </linearGradient>
                    <linearGradient id="consulting-gradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#f59e0b" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Area dataKey="subscriptions" stackId="1" stroke="#3b82f6" fill="url(#revenue-gradient)" />
                  <Area dataKey="training" stackId="1" stroke="#10b981" fill="url(#training-gradient)" />
                  <Area dataKey="consulting" stackId="1" stroke="#f59e0b" fill="url(#consulting-gradient)" />
                </AreaChart>
              </ChartContainer>
            </CardContent>
          </Card>

          {/* Belize Districts Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                District Performance
              </CardTitle>
              <CardDescription>User distribution and growth across Belize districts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {belizeDistrictsData.map((district, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:shadow-sm transition-shadow">
                    <div className="flex items-center gap-4">
                      <div 
                        className="w-4 h-4 rounded-full flex-shrink-0"
                        style={{ backgroundColor: district.color }}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {district.name.replace(' District', '')}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {district.cities.slice(0, 2).join(', ')}
                          {district.cities.length > 2 && ` +${district.cities.length - 2} more`}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-6 text-sm">
                      <div className="text-center">
                        <p className="font-semibold text-gray-900 dark:text-white">{district.users}</p>
                        <p className="text-xs text-gray-500">Users</p>
                      </div>
                      <div className="text-center">
                        <p className="font-semibold text-gray-900 dark:text-white">{district.prospects}</p>
                        <p className="text-xs text-gray-500">Prospects</p>
                      </div>
                      <div className="text-center">
                        <p className="font-semibold text-gray-900 dark:text-white">{district.bpos}</p>
                        <p className="text-xs text-gray-500">BPOs</p>
              </div>
                      <Badge 
                        variant="outline" 
                        className="text-green-600 border-green-200 bg-green-50 dark:text-green-400 dark:border-green-800 dark:bg-green-950/20"
                      >
                        {district.growth}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>

              {/* Summary Stats */}
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {belizeDistrictsData.reduce((sum, d) => sum + d.users, 0)}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Users</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {belizeDistrictsData.reduce((sum, d) => sum + d.prospects, 0)}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Prospects</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {belizeDistrictsData.reduce((sum, d) => sum + d.bpos, 0)}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total BPOs</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analytics & Insights */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Analytics & Insights</h2>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Track platform performance and user engagement</p>
            </div>
            <TabsList className="grid w-auto grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="training">Training</TabsTrigger>
              <TabsTrigger value="revenue">Revenue</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Platform Overview</CardTitle>
                  <CardDescription>Key metrics and performance indicators</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                          <CheckCircle2 className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">System Uptime</p>
                          <p className="text-sm text-gray-500">Last 30 days</p>
            </div>
          </div>
                      <div className="text-right">
                        <p className="font-semibold">{(stats.platformUptime || 99.9)}%</p>
                        <p className="text-sm text-green-600">+0.1%</p>
        </div>
      </div>
      
                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                          <Target className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">Completion Rate</p>
                          <p className="text-sm text-gray-500">Training modules</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{(stats.avgCompletionRate || 0)}%</p>
                        <p className="text-sm text-blue-600">+5.2%</p>
                      </div>
      </div>
      
                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
                          <Star className="h-4 w-4 text-yellow-600" />
                        </div>
                        <div>
                          <p className="font-medium">User Satisfaction</p>
                          <p className="text-sm text-gray-500">Average rating</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{(stats.customerSatisfaction || 0)}/5</p>
                        <p className="text-sm text-yellow-600">+0.2</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Revenue Overview</CardTitle>
                  <CardDescription>Monthly revenue breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center mb-4">
                    <p className="text-3xl font-bold text-gray-900 dark:text-white">
                      ${((stats.monthlyRevenue || 0) / 1000).toFixed(1)}k
                    </p>
                    <p className="text-sm text-gray-500">This month</p>
                    <Badge variant="outline" className="mt-2 text-green-600 border-green-200 bg-green-50">
                      +12.5% from last month
                    </Badge>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Subscriptions</span>
                      <span className="font-medium">$21.1k</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: '45%' }}></div>
        </div>
        
                    <div className="flex justify-between text-sm">
                      <span>Training Revenue</span>
                      <span className="font-medium">$15.9k</span>
              </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '34%' }}></div>
              </div>
                    
                    <div className="flex justify-between text-sm">
                      <span>Consulting</span>
                      <span className="font-medium">$10.4k</span>
              </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-orange-600 h-2 rounded-full" style={{ width: '21%' }}></div>
          </div>
        </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="training" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Training Module Performance
                </CardTitle>
                <CardDescription>
                  Detailed analytics on training effectiveness and completion rates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="h-80">
                  <BarChart data={modulePerformanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="module" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="completed" fill="#10b981" />
                    <Bar dataKey="failed" fill="#ef4444" />
                  </BarChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="revenue" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Revenue Analytics
                </CardTitle>
                <CardDescription>
                  Revenue breakdown by service category
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="h-80">
                  <LineChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Line type="monotone" dataKey="subscriptions" stroke="#3b82f6" strokeWidth={2} />
                    <Line type="monotone" dataKey="training" stroke="#10b981" strokeWidth={2} />
                    <Line type="monotone" dataKey="consulting" stroke="#f59e0b" strokeWidth={2} />
                  </LineChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Session Management Section */}
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Session Management</h2>
            <p className="text-gray-600 dark:text-gray-400 text-sm">Monitor and control user sessions across the platform</p>
          </div>
          <HardLogoutControl />
        </div>
      </div>
    </div>
  );
}