import { Suspense } from "react"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { FilesPage as FilesPageComponent } from "@/components/files-page"
import { Database } from "@/types/database.types"

async function FilesData() {
  try {
    console.log("🚀 FilesData component starting")
    const supabase = createServerComponentClient<Database>({ cookies })

    // Check if user is authenticated using getUser
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    console.log("👤 Auth check:", { userId: user?.id, error: userError?.message })

    if (userError) {
      console.error("Authentication error:", userError)
      redirect("/login")
    }
    if (!user) {
      console.log("❌ No user found, redirecting to login")
      redirect("/login")
    }
    
    // Get the prospect ID from the users table
    const { data: userData, error: userDataError } = await supabase
      .from("users")
      .select("id, role")
      .eq("id", user.id)
      .single()

    console.log("👥 User data check:", { userData, error: userDataError?.message })

    if (userDataError) {
      console.error("User data error:", userDataError)
      redirect("/unauthorized")
    }
    if (!userData || userData.role !== "prospect") {
      console.log("❌ User is not a prospect, redirecting")
      redirect("/unauthorized")
    }
    
    // Get the prospect's profile
    const { data: prospectData, error: prospectError } = await supabase
      .from("prospects")
      .select("id")
      .eq("user_id", userData.id)
      .single()
    
    if (prospectError) {
      console.error("Prospect error:", prospectError)
      return <div>Error loading prospect data. Please try again later.</div>
    }
    if (!prospectData) {
      return <div>Prospect profile not found.</div>
    }
    
    // Fetch all files for this prospect (simplified query first)
    console.log("Fetching files for prospect:", prospectData.id)

    const { data: files, error: filesError } = await supabase
      .from("files")
      .select("*")
      .eq("prospect_id", prospectData.id)
      .order("created_at", { ascending: false })

    console.log("Files query result:", { files: files?.length, error: filesError?.message })

    if (filesError) {
      console.error("Files error:", filesError)
      return <div>Error loading files. Please try again later.</div>
    }

    // Transform files data for the component
    const transformedFiles = files?.map(file => ({
      id: file.id,
      file_name: file.original_filename || file.title || "Untitled File",
      file_type: file.file_type || "document",
      file_url: file.file_url || "",
      file_size: file.file_size || 0,
      upload_date: file.issued_at || "",
      module_title: "", // Removed training_modules join for now
      module_description: "", // Removed training_modules join for now
      issue_date: file.issued_at || "",
      expiry_date: file.expires_at || "",
      verification_code: file.verification_code || "",
      created_at: file.created_at,
      updated_at: file.updated_at,
      mime_type: file.mime_type || "",
      original_filename: file.original_filename || "",
      title: file.title || ""
    })) || []

    return (
      <FilesPageComponent
        files={transformedFiles}
        prospectId={prospectData.id}
      />
    )
  } catch (error) {
    console.error("Unexpected error:", error)
    return <div>An unexpected error occurred. Please try again later.</div>
  }
}

export default function FilesPageWrapper() {
  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <Suspense fallback={<div>Loading files...</div>}>
        <FilesData />
      </Suspense>
    </div>
  )
}