import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import type { Database } from '@/types/database.types';

// Debug function to log middleware activity
function debugLog(message: string, data?: any) {
  // Log to server console
  console.log(`[MIDDLEWARE DEBUG] ${message}`, data ? JSON.stringify(data) : '');
}

// Cache for Supabase clients to prevent multiple initializations
const clientCache = new Map();

export async function middleware(req: NextRequest) {
  // Log the request
  debugLog(`Request path: ${req.nextUrl.pathname}`);

  // Initialize response
  let res = NextResponse.next();

  // Skip middleware for static files and API routes
  if (
    req.nextUrl.pathname.startsWith('/_next/') ||
    req.nextUrl.pathname.startsWith('/api/') ||
    req.nextUrl.pathname.startsWith('/debug/') ||
    req.nextUrl.pathname.match(/\.(ico|png|jpg|jpeg|svg|css|js)$/)
  ) {
    debugLog('Skipping middleware for static/API/debug route');
    return res;
  }

  // Public routes that don't require authentication
  const publicRoutes = ['/login', '/logout', '/signup', '/reset-password'];
  const isPublicRoute = publicRoutes.some(route => req.nextUrl.pathname.startsWith(route));

  try {
    debugLog('Initializing Supabase client');

    // Check if environment variables are set
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('[MIDDLEWARE ERROR] Missing Supabase environment variables');
      return NextResponse.redirect(new URL('/login', req.url));
    }

    const supabase = createMiddlewareClient<Database>({ req, res });

    // Check auth status using getUser instead of getSession (more secure)
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError) {
      debugLog('Auth error:', userError);
      // If there's an auth error and it's not a public route, redirect to login
      if (!isPublicRoute) {
        return NextResponse.redirect(new URL('/login', req.url));
      }
      return res;
    }

    debugLog('Auth user:', user ? { id: user.id, email: user.email } : 'No user');

    // If user is logged in and tries to access login page, redirect to home
    if (user && req.nextUrl.pathname === '/login') {
      debugLog('Authenticated user trying to access login, redirecting to home');
      return NextResponse.redirect(new URL('/', req.url));
    }

    // If no user and trying to access protected route, redirect to login
    if (!user && !isPublicRoute) {
      debugLog('Unauthenticated user trying to access protected route, redirecting to login');
      const redirectUrl = new URL('/login', req.url);
      // Add the original URL as a query parameter to redirect back after login
      redirectUrl.searchParams.set('redirectTo', req.nextUrl.pathname);
      return NextResponse.redirect(redirectUrl);
    }

    // For authenticated users accessing protected routes
    if (user) {
      debugLog('User is authenticated, setting session cookie');

      // Check role-based access for dashboard routes
      const pathname = req.nextUrl.pathname;

      // Get user role from database
      const { data: userData, error: roleError } = await supabase
        .from('users')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!roleError && userData) {
        const userRole = userData.role;
        debugLog('User role:', userRole);

        // Role-based route protection
        if (pathname.startsWith('/prospect/') && userRole !== 'prospect') {
          debugLog('Non-prospect user trying to access prospect dashboard, redirecting');
          return NextResponse.redirect(new URL('/', req.url));
        }

        if (pathname.startsWith('/bpo/') && !['bpo_admin', 'bpo_user'].includes(userRole)) {
          debugLog('Non-BPO user trying to access BPO dashboard, redirecting');
          return NextResponse.redirect(new URL('/', req.url));
        }

        // Super admin can access all routes, so no restriction for 'super_admin'
      }

      // Create a new response with the session
      res = NextResponse.next({
        request: {
          headers: req.headers,
        },
      });
    }

    return res;
  } catch (error) {
    console.error('[MIDDLEWARE ERROR]', error);
    // For errors, redirect to login if not a public route
    if (!isPublicRoute) {
      return NextResponse.redirect(new URL('/login', req.url));
    }
    return res;
  }
}

// Clean up cache periodically (every 5 minutes)
setInterval(() => {
  clientCache.clear();
}, 5 * 60 * 1000);

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}; 