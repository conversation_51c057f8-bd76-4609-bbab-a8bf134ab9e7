import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Test auth API called')
    const supabase = createRouteHandlerClient({ cookies })
    
    // Check authentication
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    console.log('👤 User check:', { user: user?.id, error: userError?.message })
    
    if (userError || !user) {
      return NextResponse.json({ 
        error: 'Unauthorized',
        details: userError?.message || 'No user found'
      }, { status: 401 })
    }

    // Get user data
    const { data: userData, error: userDataError } = await supabase
      .from('users')
      .select('id, role, email')
      .eq('id', user.id)
      .single()

    if (userDataError) {
      return NextResponse.json({ 
        error: 'User data error',
        details: userDataError.message
      }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: userData.role
      }
    })

  } catch (error) {
    console.error('Test auth error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
