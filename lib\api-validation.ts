/**
 * API Validation Middleware
 * Comprehensive request validation and sanitization
 */

import { NextRequest, NextResponse } from 'next/server';
import { createError, ErrorType } from '@/lib/utils';
import { handleApiError } from '@/lib/api-error-handler';
import { securityAudit, SecurityEventType } from '@/lib/security-audit';

// Validation schema types
export interface ValidationSchema {
  [key: string]: FieldValidator;
}

export interface FieldValidator {
  type: 'string' | 'number' | 'boolean' | 'email' | 'url' | 'uuid' | 'date' | 'array' | 'object';
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  enum?: string[] | number[];
  custom?: (value: any) => boolean | string;
  sanitize?: boolean;
  items?: FieldValidator; // For arrays
  properties?: ValidationSchema; // For objects
}

// Common validation patterns
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  PHONE: /^\+?[\d\s\-\(\)]{10,}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  SAFE_STRING: /^[a-zA-Z0-9\s\-_.,!?()]+$/
} as const;

// Common validation schemas
export const COMMON_SCHEMAS = {
  USER_CREATE: {
    email: { type: 'email', required: true, maxLength: 255 },
    full_name: { type: 'string', required: true, minLength: 2, maxLength: 100, sanitize: true },
    role: { type: 'string', required: true, enum: ['admin', 'bpo_admin', 'prospect'] },
    password: { type: 'string', required: true, pattern: VALIDATION_PATTERNS.PASSWORD }
  },
  
  USER_UPDATE: {
    full_name: { type: 'string', minLength: 2, maxLength: 100, sanitize: true },
    avatar_url: { type: 'url', maxLength: 500 },
    status: { type: 'string', enum: ['active', 'inactive', 'pending_activation'] }
  },
  
  PROFILE_UPDATE: {
    contact_info: { 
      type: 'object',
      properties: {
        phone: { type: 'string', pattern: VALIDATION_PATTERNS.PHONE },
        address: { type: 'string', maxLength: 500, sanitize: true },
        city: { type: 'string', maxLength: 100, sanitize: true },
        country: { type: 'string', maxLength: 100, sanitize: true }
      }
    },
    skills: {
      type: 'array',
      items: { type: 'string', maxLength: 50, sanitize: true }
    },
    experience: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          company: { type: 'string', required: true, maxLength: 100, sanitize: true },
          position: { type: 'string', required: true, maxLength: 100, sanitize: true },
          duration: { type: 'string', maxLength: 50, sanitize: true },
          description: { type: 'string', maxLength: 1000, sanitize: true }
        }
      }
    }
  },
  
  FILE_UPLOAD: {
    fileType: { type: 'string', required: true, enum: ['avatar', 'resume', 'video', 'document'] },
    fileName: { type: 'string', required: true, maxLength: 255, pattern: VALIDATION_PATTERNS.SAFE_STRING }
  }
} as const;

/**
 * Request validation middleware
 */
export function withValidation(
  schema: ValidationSchema,
  options: {
    validateQuery?: boolean;
    validateBody?: boolean;
    allowExtraFields?: boolean;
    logValidationErrors?: boolean;
  } = {}
) {
  const {
    validateQuery = false,
    validateBody = true,
    allowExtraFields = false,
    logValidationErrors = true
  } = options;
  
  return function validationMiddleware(
    handler: (request: NextRequest) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest): Promise<NextResponse> {
      try {
        // Validate query parameters
        if (validateQuery) {
          const queryParams = Object.fromEntries(request.nextUrl.searchParams);
          const queryValidation = validateData(queryParams, schema, allowExtraFields);
          
          if (!queryValidation.isValid) {
            if (logValidationErrors) {
              securityAudit.logApiSecurityEvent(
                SecurityEventType.MALFORMED_REQUEST,
                request,
                'Query parameter validation failed',
                { errors: queryValidation.errors }
              );
            }
            
            return handleApiError(queryValidation.error!, 'Query Validation');
          }
        }
        
        // Validate request body
        if (validateBody && (request.method === 'POST' || request.method === 'PUT' || request.method === 'PATCH')) {
          try {
            const body = await request.json();
            const bodyValidation = validateData(body, schema, allowExtraFields);
            
            if (!bodyValidation.isValid) {
              if (logValidationErrors) {
                securityAudit.logApiSecurityEvent(
                  SecurityEventType.MALFORMED_REQUEST,
                  request,
                  'Request body validation failed',
                  { errors: bodyValidation.errors }
                );
              }
              
              return handleApiError(bodyValidation.error!, 'Body Validation');
            }
            
            // Create new request with validated/sanitized data
            const sanitizedRequest = new NextRequest(request.url, {
              method: request.method,
              headers: request.headers,
              body: JSON.stringify(bodyValidation.sanitizedData)
            });
            
            return await handler(sanitizedRequest);
            
          } catch (jsonError) {
            const malformedError = createError(
              ErrorType.VALIDATION,
              'Invalid JSON in request body',
              'Request body must be valid JSON',
              { originalError: jsonError }
            );
            
            if (logValidationErrors) {
              securityAudit.logApiSecurityEvent(
                SecurityEventType.MALFORMED_REQUEST,
                request,
                'Invalid JSON in request body'
              );
            }
            
            return handleApiError(malformedError, 'JSON Validation');
          }
        }
        
        return await handler(request);
        
      } catch (error) {
        return handleApiError(error, 'Validation Middleware Error');
      }
    };
  };
}

/**
 * Validate data against schema
 */
export function validateData(
  data: any,
  schema: ValidationSchema,
  allowExtraFields = false
): {
  isValid: boolean;
  errors?: string[];
  error?: Error;
  sanitizedData?: any;
} {
  const errors: string[] = [];
  const sanitizedData: any = {};
  
  // Check for required fields
  for (const [fieldName, validator] of Object.entries(schema)) {
    if (validator.required && (data[fieldName] === undefined || data[fieldName] === null)) {
      errors.push(`Field '${fieldName}' is required`);
    }
  }
  
  // Validate each field
  for (const [fieldName, value] of Object.entries(data)) {
    const validator = schema[fieldName];
    
    // Check if field is allowed
    if (!validator && !allowExtraFields) {
      errors.push(`Field '${fieldName}' is not allowed`);
      continue;
    }
    
    // Skip validation if no validator and extra fields are allowed
    if (!validator) {
      sanitizedData[fieldName] = value;
      continue;
    }
    
    // Skip validation for undefined/null optional fields
    if ((value === undefined || value === null) && !validator.required) {
      continue;
    }
    
    // Validate field
    const fieldValidation = validateField(fieldName, value, validator);
    if (!fieldValidation.isValid) {
      errors.push(...fieldValidation.errors);
    } else {
      sanitizedData[fieldName] = fieldValidation.sanitizedValue;
    }
  }
  
  if (errors.length > 0) {
    return {
      isValid: false,
      errors,
      error: createError(
        ErrorType.VALIDATION,
        `Validation failed: ${errors.join(', ')}`,
        'Please check your input and try again',
        { validationErrors: errors }
      )
    };
  }
  
  return {
    isValid: true,
    sanitizedData
  };
}

/**
 * Validate individual field
 */
function validateField(
  fieldName: string,
  value: any,
  validator: FieldValidator
): {
  isValid: boolean;
  errors: string[];
  sanitizedValue: any;
} {
  const errors: string[] = [];
  let sanitizedValue = value;
  
  // Type validation
  switch (validator.type) {
    case 'string':
      if (typeof value !== 'string') {
        errors.push(`Field '${fieldName}' must be a string`);
        break;
      }
      
      // Sanitize if requested
      if (validator.sanitize) {
        sanitizedValue = sanitizeString(value);
      }
      
      // Length validation
      if (validator.minLength && sanitizedValue.length < validator.minLength) {
        errors.push(`Field '${fieldName}' must be at least ${validator.minLength} characters`);
      }
      if (validator.maxLength && sanitizedValue.length > validator.maxLength) {
        errors.push(`Field '${fieldName}' must be at most ${validator.maxLength} characters`);
      }
      
      // Pattern validation
      if (validator.pattern && !validator.pattern.test(sanitizedValue)) {
        errors.push(`Field '${fieldName}' has invalid format`);
      }
      
      // Enum validation
      if (validator.enum && !validator.enum.includes(sanitizedValue)) {
        errors.push(`Field '${fieldName}' must be one of: ${validator.enum.join(', ')}`);
      }
      break;
      
    case 'number':
      if (typeof value !== 'number' || isNaN(value)) {
        errors.push(`Field '${fieldName}' must be a number`);
        break;
      }
      
      if (validator.min !== undefined && value < validator.min) {
        errors.push(`Field '${fieldName}' must be at least ${validator.min}`);
      }
      if (validator.max !== undefined && value > validator.max) {
        errors.push(`Field '${fieldName}' must be at most ${validator.max}`);
      }
      break;
      
    case 'boolean':
      if (typeof value !== 'boolean') {
        errors.push(`Field '${fieldName}' must be a boolean`);
      }
      break;
      
    case 'email':
      if (typeof value !== 'string' || !VALIDATION_PATTERNS.EMAIL.test(value)) {
        errors.push(`Field '${fieldName}' must be a valid email address`);
      } else {
        sanitizedValue = value.toLowerCase().trim();
      }
      break;
      
    case 'url':
      if (typeof value !== 'string' || !VALIDATION_PATTERNS.URL.test(value)) {
        errors.push(`Field '${fieldName}' must be a valid URL`);
      }
      break;
      
    case 'uuid':
      if (typeof value !== 'string' || !VALIDATION_PATTERNS.UUID.test(value)) {
        errors.push(`Field '${fieldName}' must be a valid UUID`);
      }
      break;
      
    case 'date':
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        errors.push(`Field '${fieldName}' must be a valid date`);
      } else {
        sanitizedValue = date.toISOString();
      }
      break;
      
    case 'array':
      if (!Array.isArray(value)) {
        errors.push(`Field '${fieldName}' must be an array`);
        break;
      }
      
      if (validator.items) {
        const sanitizedArray: any[] = [];
        value.forEach((item, index) => {
          const itemValidation = validateField(`${fieldName}[${index}]`, item, validator.items!);
          if (!itemValidation.isValid) {
            errors.push(...itemValidation.errors);
          } else {
            sanitizedArray.push(itemValidation.sanitizedValue);
          }
        });
        sanitizedValue = sanitizedArray;
      }
      break;
      
    case 'object':
      if (typeof value !== 'object' || value === null || Array.isArray(value)) {
        errors.push(`Field '${fieldName}' must be an object`);
        break;
      }
      
      if (validator.properties) {
        const objectValidation = validateData(value, validator.properties, true);
        if (!objectValidation.isValid) {
          errors.push(...(objectValidation.errors || []));
        } else {
          sanitizedValue = objectValidation.sanitizedData;
        }
      }
      break;
  }
  
  // Custom validation
  if (validator.custom && errors.length === 0) {
    const customResult = validator.custom(sanitizedValue);
    if (customResult !== true) {
      errors.push(typeof customResult === 'string' ? customResult : `Field '${fieldName}' failed custom validation`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue
  };
}

/**
 * Sanitize string input
 */
function sanitizeString(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .replace(/\0/g, ''); // Remove null bytes
}
