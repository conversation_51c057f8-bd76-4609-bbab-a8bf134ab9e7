'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, ChevronRight, Clock, Award, CheckCircle, FileText, BarChart4 } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/hooks/use-auth';
import { getAssessmentWithStatus } from '@/lib/api/assessments';
import { getAssessmentAnswers, getProspectTypingStats } from '@/lib/api/assessment-answers';
import { AssessmentWithStatus } from '@/types/assessment';
import { AssessmentAnswer } from '@/lib/api/assessment-answers';

interface AssessmentResultsProps {
  assessmentId: string;
}

export function AssessmentResults({ assessmentId }: AssessmentResultsProps) {
  const [assessment, setAssessment] = useState<AssessmentWithStatus | null>(null);
  const [answers, setAnswers] = useState<AssessmentAnswer[]>([]);
  const [typingStats, setTypingStats] = useState<{
    avgWpm: number;
    avgAccuracy: number;
    assessmentCount: number;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    async function loadAssessmentResults() {
      if (!user) {
        setError('You must be logged in to view assessment results');
        setLoading(false);
        return;
      }

      try {
        const assessmentData = await getAssessmentWithStatus(assessmentId, user.id);
        if (!assessmentData) {
          throw new Error('Assessment not found');
        }
        
        // Check if assessment is completed
        if (assessmentData.status !== 'completed') {
          throw new Error('Assessment is not completed');
        }
        
        setAssessment(assessmentData);
        
        // Load user's answers
        const answersData = await getAssessmentAnswers(assessmentId, user.id);
        setAnswers(answersData);
        
        // Load typing stats if applicable
        try {
          const stats = await getProspectTypingStats(user.id);
          setTypingStats(stats);
        } catch (err) {
          console.warn('Could not load typing stats:', err);
        }
      } catch (err: any) {
        console.error('Error loading assessment results:', err);
        setError(err.message || 'Failed to load assessment results');
      } finally {
        setLoading(false);
      }
    }
    
    loadAssessmentResults();
  }, [assessmentId, user]);

  // Format time as MM:SS
  const formatTime = (seconds: number | null) => {
    if (!seconds) return 'N/A';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  if (loading) {
    return (
      <div className="max-w-3xl mx-auto p-4">
        <Card>
          <CardHeader>
            <div className="h-8 w-3/4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse mt-2"></div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4 grid-cols-3">
              <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
            </div>
            <div className="h-40 bg-gray-200 rounded animate-pulse"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-3xl mx-auto p-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
          <Button 
            variant="outline" 
            className="mt-4" 
            onClick={() => router.push('/prospect/assessments')}
          >
            Back to Assessments
          </Button>
        </Alert>
      </div>
    );
  }

  if (!assessment) {
    return (
      <div className="max-w-3xl mx-auto p-4">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Assessment Not Found</AlertTitle>
          <AlertDescription>
            The assessment results you're looking for don't exist or have been removed.
          </AlertDescription>
          <Button 
            variant="outline" 
            className="mt-4" 
            onClick={() => router.push('/prospect/assessments')}
          >
            Back to Assessments
          </Button>
        </Alert>
      </div>
    );
  }

  const isPassed = assessment.score !== null && assessment.score >= assessment.passing_score;

  return (
    <div className="max-w-3xl mx-auto p-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">{assessment.title} Results</CardTitle>
              <p className="text-muted-foreground mt-1">{assessment.description}</p>
            </div>
            <Badge variant={isPassed ? "success" : "destructive"} className="mt-1">
              {isPassed ? 'Passed' : 'Failed'}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Score overview */}
          <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
            <Card className="bg-muted/50 border-0">
              <CardContent className="p-4 flex flex-col items-center justify-center">
                <Award className="h-8 w-8 text-primary mb-2" />
                <div className="text-xs text-muted-foreground">Your Score</div>
                <div className="text-2xl font-bold mt-1">{assessment.score}%</div>
                <div className="text-xs text-muted-foreground mt-1">
                  Passing: {assessment.passing_score}%
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-muted/50 border-0">
              <CardContent className="p-4 flex flex-col items-center justify-center">
                <Clock className="h-8 w-8 text-primary mb-2" />
                <div className="text-xs text-muted-foreground">Completion Time</div>
                <div className="text-2xl font-bold mt-1">
                  {formatTime(assessment.completion_time)}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {assessment.duration} allowed
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-muted/50 border-0">
              <CardContent className="p-4 flex flex-col items-center justify-center">
                <FileText className="h-8 w-8 text-primary mb-2" />
                <div className="text-xs text-muted-foreground">Questions</div>
                <div className="text-2xl font-bold mt-1">
                  {assessment.total_questions}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {answers.length} answered
                </div>
              </CardContent>
            </Card>
          </div>

          <Separator />

          {/* Score progress bar */}
          <div>
            <h3 className="text-lg font-medium mb-3">Score Breakdown</h3>
            <div className="space-y-2">
              <div className="flex justify-between items-center text-sm">
                <div>Your Score: {assessment.score}%</div>
                <div>Passing Score: {assessment.passing_score}%</div>
              </div>
              <div className="relative h-4">
                <Progress value={assessment.score || 0} className="h-full" />
                <div 
                  className="absolute top-0 bottom-0 border-r-2 border-black"
                  style={{ left: `${assessment.passing_score}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Typing stats if available */}
          {typingStats && answers.some(a => a.additional_data?.wpm) && (
            <>
              <Separator />
              
              <div>
                <h3 className="text-lg font-medium mb-3">Typing Performance</h3>
                <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
                  <Card className="bg-muted/50 border-0">
                    <CardContent className="p-4 flex items-center">
                      <BarChart4 className="h-10 w-10 text-primary mr-4" />
                      <div>
                        <div className="text-sm text-muted-foreground">Typing Speed</div>
                        <div className="text-2xl font-bold">{typingStats.avgWpm} WPM</div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card className="bg-muted/50 border-0">
                    <CardContent className="p-4 flex items-center">
                      <CheckCircle className="h-10 w-10 text-primary mr-4" />
                      <div>
                        <div className="text-sm text-muted-foreground">Accuracy</div>
                        <div className="text-2xl font-bold">{typingStats.avgAccuracy}%</div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </>
          )}
          
          {/* Feedback message */}
          <Alert variant={isPassed ? "default" : "destructive"} className={isPassed ? "bg-green-50 border-green-200" : ""}>
            {isPassed ? (
              <>
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertTitle>Congratulations!</AlertTitle>
                <AlertDescription>
                  You've successfully passed this assessment. This will be visible on your profile for BPOs to review.
                </AlertDescription>
              </>
            ) : (
              <>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Assessment Not Passed</AlertTitle>
                <AlertDescription>
                  You didn't meet the passing score for this assessment. You can retake it after 24 hours.
                </AlertDescription>
              </>
            )}
          </Alert>
        </CardContent>

        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push('/prospect/assessments')}>
            Back to Assessments
          </Button>
          
          <Button onClick={() => router.push('/prospect/profile')}>
            View Profile <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
} 