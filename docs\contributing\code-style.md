# 🎨 Code Style Guide

Comprehensive coding standards and style guidelines for the BPO Training Platform to ensure consistent, maintainable, and readable code.

## 🎯 General Principles

### Code Philosophy

- **Clarity over cleverness**: Write code that is easy to understand
- **Consistency**: Follow established patterns throughout the codebase
- **Maintainability**: Write code that is easy to modify and extend
- **Performance**: Consider performance implications of your code
- **Security**: Always consider security implications

### Naming Conventions

```typescript
// Use descriptive, meaningful names
// Good
const userAuthenticationToken = generateToken()
const isUserAuthenticated = checkAuthStatus()
const calculateTrainingProgress = (completed, total) => completed / total

// Bad
const token = gen()
const auth = check()
const calc = (c, t) => c / t
```

## 📝 TypeScript Guidelines

### Type Definitions

```typescript
// Use explicit types for function parameters and return values
function calculateCompletionPercentage(
  completedActivities: number,
  totalActivities: number
): number {
  return Math.round((completedActivities / totalActivities) * 100)
}

// Use interfaces for object shapes
interface User {
  id: string
  email: string
  full_name: string
  role: 'admin' | 'bpo_admin' | 'prospect'
  status: 'active' | 'inactive' | 'pending_activation'
  created_at: string
  updated_at: string
}

// Use type unions for specific values
type TrainingStatus = 'not_started' | 'in_progress' | 'completed'
type EmploymentType = 'full_time' | 'part_time' | 'contract'

// Use generic types for reusable components
interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  error?: string
}

// Use utility types when appropriate
type CreateUserRequest = Omit<User, 'id' | 'created_at' | 'updated_at'>
type UpdateUserRequest = Partial<Pick<User, 'full_name' | 'email'>>
```

### Type Organization

```typescript
// types/database.types.ts - Database-related types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: User
        Insert: CreateUserRequest
        Update: UpdateUserRequest
      }
      // ... other tables
    }
  }
}

// types/api.types.ts - API-related types
export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  user: User
  session: {
    access_token: string
    refresh_token: string
    expires_at: string
  }
}

// types/components.types.ts - Component prop types
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  loading?: boolean
}
```

## ⚛️ React Component Guidelines

### Component Structure

```typescript
// components/ui/Button.tsx
import * as React from 'react'
import { cn } from '@/lib/utils'
import { buttonVariants } from './button-variants'

// 1. Interface definition
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

// 2. Component implementation
export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant = 'default', 
    size = 'default', 
    loading = false,
    leftIcon,
    rightIcon,
    children,
    disabled,
    ...props 
  }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size }), className)}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && <LoadingSpinner className="mr-2 h-4 w-4" />}
        {!loading && leftIcon && <span className="mr-2">{leftIcon}</span>}
        {children}
        {!loading && rightIcon && <span className="ml-2">{rightIcon}</span>}
      </button>
    )
  }
)

// 3. Display name for debugging
Button.displayName = 'Button'

// 4. Default export
export default Button
```

### Component Organization

```typescript
// Feature-based component structure
components/
├── ui/                    # Reusable UI components
│   ├── button.tsx
│   ├── card.tsx
│   ├── input.tsx
│   └── index.ts          # Barrel exports
├── forms/                # Form-specific components
│   ├── login-form.tsx
│   ├── register-form.tsx
│   └── index.ts
├── training/             # Training feature components
│   ├── module-card.tsx
│   ├── lesson-player.tsx
│   ├── progress-tracker.tsx
│   └── index.ts
└── layout/               # Layout components
    ├── header.tsx
    ├── sidebar.tsx
    ├── footer.tsx
    └── index.ts
```

### Hooks Guidelines

```typescript
// Custom hooks should start with 'use'
// hooks/use-auth.ts
export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  // Use useCallback for functions that might be dependencies
  const login = useCallback(async (email: string, password: string) => {
    setLoading(true)
    try {
      const response = await authService.login(email, password)
      setUser(response.user)
      return response
    } catch (error) {
      throw error
    } finally {
      setLoading(false)
    }
  }, [])

  // Use useMemo for expensive computations
  const isAuthenticated = useMemo(() => !!user, [user])

  return {
    user,
    loading,
    isAuthenticated,
    login,
    logout: authService.logout
  }
}

// Performance-optimized hooks
// hooks/use-optimized-state.ts
export function useOptimizedState<T>(
  initialValue: T,
  equalityFn?: (a: T, b: T) => boolean
) {
  const [state, setState] = useState(initialValue)

  const setOptimizedState = useCallback((newValue: T | ((prev: T) => T)) => {
    setState(prev => {
      const nextValue = typeof newValue === 'function' 
        ? (newValue as (prev: T) => T)(prev)
        : newValue

      // Use custom equality function or default comparison
      if (equalityFn ? equalityFn(prev, nextValue) : prev === nextValue) {
        return prev // Prevent unnecessary re-renders
      }

      return nextValue
    })
  }, [equalityFn])

  return [state, setOptimizedState] as const
}
```

## 🎨 Styling Guidelines

### Tailwind CSS Best Practices

```typescript
// Use consistent spacing scale
const spacingClasses = {
  xs: 'p-2',      // 8px
  sm: 'p-3',      // 12px
  md: 'p-4',      // 16px
  lg: 'p-6',      // 24px
  xl: 'p-8'       // 32px
}

// Group related classes together
<div className={cn(
  // Layout
  'flex items-center justify-between',
  // Spacing
  'px-4 py-2',
  // Appearance
  'bg-white border border-gray-200 rounded-lg shadow-sm',
  // Interactive states
  'hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500',
  // Responsive
  'md:px-6 md:py-3',
  // Conditional classes
  isActive && 'bg-blue-50 border-blue-200',
  className
)} />

// Use semantic color names
const colorScheme = {
  primary: 'bg-blue-600 text-white',
  secondary: 'bg-gray-100 text-gray-900',
  success: 'bg-green-600 text-white',
  warning: 'bg-yellow-500 text-white',
  error: 'bg-red-600 text-white'
}

// Create reusable variant classes
const buttonVariants = cva(
  // Base classes
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'underline-offset-4 hover:underline text-primary'
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 px-3 rounded-md',
        lg: 'h-11 px-8 rounded-md',
        icon: 'h-10 w-10'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  }
)
```

### CSS Custom Properties

```css
/* globals.css */
:root {
  /* Color system */
  --color-primary: 222.2 84% 4.9%;
  --color-primary-foreground: 210 40% 98%;
  --color-secondary: 210 40% 96%;
  --color-secondary-foreground: 222.2 84% 4.9%;
  
  /* Spacing system */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Typography */
  --font-family-sans: 'Inter', system-ui, sans-serif;
  --font-family-mono: 'Fira Code', monospace;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
}
```

## 📁 File Organization

### Directory Structure

```
src/
├── app/                  # Next.js app directory
│   ├── (auth)/          # Route groups
│   ├── admin/
│   ├── api/
│   └── globals.css
├── components/          # React components
│   ├── ui/             # Base UI components
│   ├── forms/          # Form components
│   ├── layout/         # Layout components
│   └── features/       # Feature-specific components
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries
│   ├── auth.ts
│   ├── database.ts
│   ├── utils.ts
│   └── validations.ts
├── types/              # TypeScript type definitions
│   ├── database.types.ts
│   ├── api.types.ts
│   └── global.types.ts
├── styles/             # Additional styles
└── utils/              # Helper functions
```

### Import Organization

```typescript
// 1. External library imports
import React, { useState, useEffect, useCallback } from 'react'
import { NextPage } from 'next'
import { useRouter } from 'next/router'
import { z } from 'zod'

// 2. Internal imports (absolute paths)
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/hooks/use-auth'
import { supabase } from '@/lib/supabase'

// 3. Relative imports
import './component.css'

// 4. Type-only imports (separate from value imports)
import type { User, TrainingModule } from '@/types/database.types'
import type { ComponentProps } from 'react'
```

## 🔧 Function Guidelines

### Function Naming and Structure

```typescript
// Use descriptive function names
// Good
function calculateTrainingCompletionPercentage(completed: number, total: number): number {
  if (total === 0) return 0
  return Math.round((completed / total) * 100)
}

function validateEmailAddress(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Bad
function calc(c: number, t: number): number {
  return Math.round((c / t) * 100)
}

function check(e: string): boolean {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)
}

// Use pure functions when possible
function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency
  }).format(amount)
}

// Handle errors appropriately
async function fetchUserProfile(userId: string): Promise<User | null> {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Failed to fetch user profile:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Unexpected error fetching user profile:', error)
    return null
  }
}
```

### Async/Await Guidelines

```typescript
// Prefer async/await over Promises
// Good
async function createTrainingModule(moduleData: CreateModuleRequest): Promise<TrainingModule> {
  try {
    const { data, error } = await supabase
      .from('training_modules')
      .insert(moduleData)
      .select()
      .single()

    if (error) throw error
    return data
  } catch (error) {
    console.error('Failed to create training module:', error)
    throw new Error('Failed to create training module')
  }
}

// Handle multiple async operations
async function initializeUserDashboard(userId: string) {
  try {
    const [user, progress, applications] = await Promise.all([
      fetchUserProfile(userId),
      fetchTrainingProgress(userId),
      fetchJobApplications(userId)
    ])

    return {
      user,
      progress,
      applications
    }
  } catch (error) {
    console.error('Failed to initialize dashboard:', error)
    throw error
  }
}
```

## 📝 Comments and Documentation

### Code Comments

```typescript
/**
 * Calculates the completion percentage for a training module
 * 
 * @param completedActivities - Number of activities completed by the user
 * @param totalActivities - Total number of activities in the module
 * @returns Completion percentage as a number between 0 and 100
 * @throws Error if totalActivities is 0 or negative
 * 
 * @example
 * ```typescript
 * const percentage = calculateCompletionPercentage(8, 10)
 * console.log(percentage) // 80
 * ```
 */
function calculateCompletionPercentage(
  completedActivities: number,
  totalActivities: number
): number {
  // Validate input parameters
  if (totalActivities <= 0) {
    throw new Error('Total activities must be greater than 0')
  }

  // Calculate and round to nearest integer
  return Math.round((completedActivities / totalActivities) * 100)
}

// Use comments to explain complex business logic
function calculateSalaryRange(
  baseAmount: number,
  experienceLevel: ExperienceLevel,
  location: string
): SalaryRange {
  // Apply experience multiplier based on industry standards
  const experienceMultipliers = {
    entry_level: 1.0,
    mid_level: 1.3,
    senior_level: 1.8
  }

  // Apply location-based cost of living adjustment
  const locationMultiplier = getLocationMultiplier(location)
  
  const adjustedBase = baseAmount * experienceMultipliers[experienceLevel] * locationMultiplier

  return {
    min: Math.round(adjustedBase * 0.9), // 10% below adjusted base
    max: Math.round(adjustedBase * 1.2), // 20% above adjusted base
    currency: 'USD'
  }
}
```

### TODO Comments

```typescript
// TODO: Implement caching for frequently accessed training modules
// TODO: Add input validation for file uploads
// FIXME: Handle edge case when user has no training progress
// HACK: Temporary workaround for Supabase RLS issue - remove when fixed
// NOTE: This function assumes UTC timezone for all date calculations
```

## 🔍 Error Handling

### Error Handling Patterns

```typescript
// Use custom error classes
class ValidationError extends Error {
  constructor(message: string, public field: string) {
    super(message)
    this.name = 'ValidationError'
  }
}

class DatabaseError extends Error {
  constructor(message: string, public originalError: unknown) {
    super(message)
    this.name = 'DatabaseError'
  }
}

// Implement proper error boundaries
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
    
    // Send to error tracking service
    if (process.env.NODE_ENV === 'production') {
      trackError(error, { errorInfo })
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h2>Something went wrong</h2>
          <p>We're sorry, but something unexpected happened.</p>
          <button onClick={() => this.setState({ hasError: false })}>
            Try again
          </button>
        </div>
      )
    }

    return this.props.children
  }
}
```

## 🧪 Testing Guidelines

### Test Structure

```typescript
// Use descriptive test names
describe('calculateCompletionPercentage', () => {
  it('should return 0 when no activities are completed', () => {
    const result = calculateCompletionPercentage(0, 10)
    expect(result).toBe(0)
  })

  it('should return 100 when all activities are completed', () => {
    const result = calculateCompletionPercentage(10, 10)
    expect(result).toBe(100)
  })

  it('should round to nearest integer for partial completion', () => {
    const result = calculateCompletionPercentage(1, 3) // 33.333...
    expect(result).toBe(33)
  })

  it('should throw error when total activities is zero', () => {
    expect(() => calculateCompletionPercentage(5, 0)).toThrow(
      'Total activities must be greater than 0'
    )
  })
})

// Use setup and teardown appropriately
describe('UserService', () => {
  let mockSupabase: jest.Mocked<typeof supabase>

  beforeEach(() => {
    mockSupabase = createMockSupabase()
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  // Test cases...
})
```

---

**Next**: Review the [Pull Request Template](pull-request-template.md) for contribution guidelines.
