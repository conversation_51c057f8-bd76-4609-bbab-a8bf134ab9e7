'use client';

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function DebugPage() {
  const [authInfo, setAuthInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient();
  const router = useRouter();

  useEffect(() => {
    async function checkAuth() {
      try {
        setLoading(true);
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          throw error;
        }
        
        setAuthInfo({
          session: data.session ? {
            user: {
              id: data.session.user.id,
              email: data.session.user.email,
            },
            expires_at: data.session.expires_at,
          } : null
        });

        // Get user info if logged in
        if (data.session) {
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.session.user.id)
            .single();
            
          if (!userError && userData) {
            setAuthInfo((prev: any) => ({
              ...prev,
              userData
            }));
          }
        }
        
      } catch (err: any) {
        setError(err.message || 'An error occurred checking authentication');
        console.error('Auth check error:', err);
      } finally {
        setLoading(false);
      }
    }
    
    checkAuth();
  }, [supabase]);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Middleware Debug Page</h1>
      
      <div className="mb-6 p-4 bg-gray-100 rounded dark:bg-gray-800">
        <h2 className="text-lg font-semibold mb-2">Authentication Status</h2>
        {loading ? (
          <p>Loading authentication status...</p>
        ) : error ? (
          <p className="text-red-500">{error}</p>
        ) : (
          <div>
            <p><strong>Status:</strong> {authInfo?.session ? 'Authenticated' : 'Not authenticated'}</p>
            {authInfo?.session && (
              <div className="mt-2">
                <p><strong>User ID:</strong> {authInfo.session.user.id}</p>
                <p><strong>Email:</strong> {authInfo.session.user.email}</p>
                <p><strong>Session Expires:</strong> {new Date(authInfo.session.expires_at * 1000).toLocaleString()}</p>
              </div>
            )}
            
            {authInfo?.userData && (
              <div className="mt-4 p-3 bg-gray-200 dark:bg-gray-700 rounded">
                <h3 className="font-medium mb-2">User Data:</h3>
                <pre className="text-xs overflow-auto">{JSON.stringify(authInfo.userData, null, 2)}</pre>
              </div>
            )}
          </div>
        )}
      </div>
      
      <div className="space-y-2">
        <h2 className="text-lg font-semibold mb-2">Navigation Links</h2>
        <div className="flex flex-wrap gap-2">
          <Link href="/" className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
            Home
          </Link>
          <Link href="/login" className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
            Login
          </Link>
          <Link href="/prospect/dashboard" className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
            Prospect Dashboard
          </Link>
          <Link href="/bpo/dashboard" className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
            BPO Dashboard
          </Link>
          <button
            onClick={() => supabase.auth.signOut().then(() => router.push('/login'))}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Sign Out
          </button>
        </div>
      </div>
      
      <div className="mt-8 p-4 bg-gray-100 rounded dark:bg-gray-800">
        <h2 className="text-lg font-semibold mb-2">Request Info</h2>
        <p><strong>URL:</strong> {typeof window !== 'undefined' ? window.location.href : ''}</p>
        <p><strong>User Agent:</strong> {typeof window !== 'undefined' ? window.navigator.userAgent : ''}</p>
      </div>
    </div>
  );
} 