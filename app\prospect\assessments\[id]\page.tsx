// Server Component (no "use client" here)
import React from 'react';
import { ErrorBoundary } from '@/components/error-boundary';
import { AssessmentContent } from './assessment-content';
import { Suspense } from 'react';

// Loading fallback for assessment content
function AssessmentLoading() {
  return (
    <div className="max-w-3xl mx-auto p-4">
      <div className="h-10 w-3/4 bg-gray-200 rounded animate-pulse mb-4"></div>
      <div className="h-6 w-1/2 bg-gray-200 rounded animate-pulse mb-8"></div>
      <div className="space-y-4">
        <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-40 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  );
}

// This is a server component that can properly handle params
export default function AssessmentPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params promise with React.use()
  const resolvedParams = React.use(params);
  const id = resolvedParams.id;
  
  return (
    <ErrorBoundary>
      <Suspense fallback={<AssessmentLoading />}>
        <AssessmentContent id={id} />
      </Suspense>
    </ErrorBoundary>
  );
} 