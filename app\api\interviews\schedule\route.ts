import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const { interviewId, scheduledAt, durationMinutes = 60 } = await request.json()
    
    if (!interviewId || !scheduledAt) {
      return NextResponse.json(
        { error: 'Interview ID and scheduled time are required' },
        { status: 400 }
      )
    }

    // Verify the user is the prospect for this interview
    const { data: interview, error: interviewError } = await supabase
      .from('interviews')
      .select(`
        id,
        application_id,
        status,
        applications!inner(
          prospect_id,
          prospects!inner(user_id)
        )
      `)
      .eq('id', interviewId)
      .single()

    if (interviewError || !interview) {
      return NextResponse.json(
        { error: 'Interview not found' },
        { status: 404 }
      )
    }

    // Verify the current user is the prospect for this interview
    if (interview.applications.prospects.user_id !== session.user.id) {
      return NextResponse.json(
        { error: 'Unauthorized: You can only schedule your own interviews' },
        { status: 403 }
      )
    }

    // Update interview with scheduled time
    const { data: updatedInterview, error: updateError } = await supabase
      .from('interviews')
      .update({
        scheduled_at: scheduledAt,
        duration_minutes: durationMinutes,
        status: 'scheduled'
      })
      .eq('id', interviewId)
      .select()
      .single()

    if (updateError) {
      console.error('Error scheduling interview:', updateError)
      return NextResponse.json(
        { error: 'Failed to schedule interview' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      interview: updatedInterview
    })

  } catch (error) {
    console.error('Error in interview scheduling:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get prospect ID for the current user
    const { data: prospect, error: prospectError } = await supabase
      .from('prospects')
      .select('id')
      .eq('user_id', session.user.id)
      .single()

    if (prospectError || !prospect) {
      return NextResponse.json(
        { error: 'Prospect profile not found' },
        { status: 404 }
      )
    }

    // Fetch interviews for this prospect
    const { data: interviews, error: interviewsError } = await supabase
      .from('interviews')
      .select(`
        id,
        scheduled_at,
        duration_minutes,
        status,
        notes,
        meeting_link,
        applications!inner(
          id,
          job_id,
          job_postings!inner(
            title,
            bpos!inner(name, logo_url)
          )
        )
      `)
      .eq('applications.prospect_id', prospect.id)
      .order('scheduled_at', { ascending: true })

    if (interviewsError) {
      console.error('Error fetching interviews:', interviewsError)
      return NextResponse.json(
        { error: 'Failed to fetch interviews' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      interviews: interviews || []
    })

  } catch (error) {
    console.error('Error fetching interviews:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
