'use client';

import { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { 
  Briefcase, 
  Plus, 
  MapPin, 
  Clock, 
  Users, 
  Search, 
  MoreVertical, 
  Edit, 
  Copy, 
  Eye, 
  Archive,
  CheckCircle2, 
  XCircle,
  CalendarDays,
  X,
  Trash2,
  Alert<PERSON>riangle
} from 'lucide-react';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetClose
} from '@/components/ui/sheet';
import VacancyForm, { VacancyFormData } from '@/components/vacancy-form';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { checkBPOMembership } from '@/lib/bpo-auth-check';

// Component that uses searchParams
function BPOVacanciesContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [vacancies, setVacancies] = useState<any[]>([]);
  const [user, setUser] = useState<any>(null);
  const [bpoData, setBpoData] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [vacancyStats, setVacancyStats] = useState({
    total: 0,
    active: 0,
    draft: 0,
    closed: 0,
    applications: 0
  });
  // State variables for modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentVacancy, setCurrentVacancy] = useState<any>(null);
  const [editFormData, setEditFormData] = useState<VacancyFormData | undefined>(undefined);
  
  // Delete confirmation dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [vacancyToDelete, setVacancyToDelete] = useState<{id: string, title: string} | null>(null);
  const [confirmTitle, setConfirmTitle] = useState('');
  const [hasApplications, setHasApplications] = useState(false);
  const [applicationCount, setApplicationCount] = useState(0);

  // Extract the fetchData function from useEffect for reuse
  async function fetchData() {
    try {
      setLoading(true);
      
      // Use the standardized membership check
      const membershipResult = await checkBPOMembership(supabase);
      
      if (!membershipResult.authorized) {
        console.log('User not authorized:', membershipResult.error?.message || 'Not a BPO team member');
        toast({
          title: "Access error",
          description: "You don't have permission to access the BPO vacancies.",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }
      
      // Store user info
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        setUser(session.user);
      }
      
      // We now have the bpoId from the membership check
      const bpoId = membershipResult.bpoId;
      
      // Get BPO data using the verified bpoId
      const { data: bpo, error: bpoError } = await supabase
        .from('bpos')
        .select('*')
        .eq('id', bpoId)
        .single();
        
      if (bpoError || !bpo) {
        console.error('Error fetching BPO data:', bpoError);
        toast({
          title: "Data error",
          description: "Could not load company data.",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }
      
      setBpoData(bpo);
      
      // Get vacancies using the security definer function
      try {
        // Use our new RPC function that bypasses RLS
        const { data: vacancyData, error: vacancyError } = await supabase
          .rpc('get_bpo_job_postings', { bpo_id_param: bpoId });
          
        if (vacancyError) {
          console.error('Error fetching vacancies:', vacancyError);
          toast({
            title: "Data error",
            description: "Could not load vacancy data. Please try again.",
            variant: "destructive",
          });
          setVacancies([]);
          return;
        }
        
        // Check if we got valid data
        if (!vacancyData || !Array.isArray(vacancyData)) {
          console.log('No vacancy data returned or invalid format');
          setVacancies([]);
          return;
        }
        
        console.log(`Successfully loaded ${vacancyData.length} vacancies`);
        
        // Process vacancy data - with no reliance on relations
        const processedVacancies = vacancyData.map(vacancy => {
          return {
            ...vacancy,
            application_count: 0,  // We'll update this later if possible
            applications: []  // Empty array for consistency
          };
        });
        
        setVacancies(processedVacancies);
        
        // Update application counts if possible
        try {
          // Simplified approach: just get counts in bulk if the table exists
          const { data: tableExists } = await supabase
            .rpc('table_exists', { table_name: 'applications' });
            
          if (tableExists) {
            console.log('Applications table exists, fetching counts');
            
            // For each vacancy, get application count separately
            for (const vacancy of processedVacancies) {
              try {
                const { count, error } = await supabase
                  .from('applications')
                  .select('*', { count: 'exact', head: true })
                  .eq('job_id', vacancy.id);
                  
                if (!error && count !== null) {
                  vacancy.application_count = count;
                }
              } catch (individualCountError) {
                console.log(`Error counting applications for vacancy ${vacancy.id}:`, individualCountError);
                // Continue with next vacancy
              }
            }
            
            // Update vacancies with new count data
            setVacancies([...processedVacancies]);
          } else {
            console.log('Applications table does not exist, skipping counts');
          }
        } catch (countError) {
          console.error('Error checking applications table:', countError);
          // Continue with the existing vacancies data
        }
        
        // Calculate statistics
        const stats = {
          total: processedVacancies.length,
          active: processedVacancies.filter(v => v.status === 'published').length,
          draft: processedVacancies.filter(v => v.status === 'draft').length,
          closed: processedVacancies.filter(v => v.status === 'archived').length,
          applications: processedVacancies.reduce((sum, v) => sum + v.application_count, 0)
        };
        
        setVacancyStats(stats);
        
      } catch (vacancyError) {
        console.error('Error processing vacancies:', vacancyError);
        // Set empty vacancies
        setVacancies([]);
        setVacancyStats({
          total: 0,
          active: 0,
          draft: 0,
          closed: 0,
          applications: 0
        });
      }
      
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchData();
    
    // Check URL params to open the appropriate modals
    const shouldOpenCreateDialog = searchParams.get('create') === 'true';
    const editId = searchParams.get('edit');
    
    if (shouldOpenCreateDialog) {
      // Wait for fetchData to complete so we have the bpoData
      setTimeout(() => {
        setIsCreateModalOpen(true);
        // Update the URL to remove the query parameter
        window.history.replaceState({}, '', '/bpo/vacancies');
      }, 500);
    }
    
    if (editId) {
      // Load the vacancy data and open edit modal
      setTimeout(() => {
        loadVacancyForEdit(editId);
        // Update the URL to remove the query parameter
        window.history.replaceState({}, '', '/bpo/vacancies');
      }, 500);
    }
  }, [searchParams]);

  const handleCreateVacancy = () => {
    setIsCreateModalOpen(true);
  };

  const handleEditVacancy = async (id: string) => {
    await loadVacancyForEdit(id);
  };
  
  const loadVacancyForEdit = async (id: string) => {
    try {
      setLoading(true);
      
      // Get the vacancy data - directly query the table now that RLS is disabled
      const { data: vacancy, error: vacancyError } = await supabase
        .from('job_postings')
        .select('*')
        .eq('id', id)
        .single();
        
      if (vacancyError || !vacancy) {
        console.error('Error fetching vacancy:', vacancyError);
        toast({
          title: "Not found",
          description: "The requested vacancy was not found or you don't have access",
          variant: "destructive",
        });
        return;
      }
      
      setCurrentVacancy(vacancy);
      
      // Parse location and salary data
      const location = vacancy.location || {};
      const salaryRange = vacancy.salary_range || {};
      
      // Format the date for the input field (YYYY-MM-DD)
      let deadlineDate = '';
      if (vacancy.application_deadline) {
        const date = new Date(vacancy.application_deadline);
        deadlineDate = date.toISOString().split('T')[0];
      }
      
      // Prepare form data
      const formData: VacancyFormData = {
        title: vacancy.title || "",
        description: vacancy.description || "",
        requirements: vacancy.requirements || "",
        job_type: vacancy.job_type as any || "full_time",
        location_city: location.city || "",
        location_country: location.country || "",
        is_remote: location.remote || false,
        salary_min: salaryRange.min ? String(salaryRange.min) : "",
        salary_max: salaryRange.max ? String(salaryRange.max) : "",
        salary_currency: salaryRange.currency || "USD",
        application_deadline: deadlineDate,
        status: vacancy.status as any || "draft"
      };
      
      setEditFormData(formData);
      setIsEditModalOpen(true);
    } catch (error) {
      console.error('Error loading vacancy for edit:', error);
      toast({
        title: "Error",
        description: "There was a problem loading the vacancy data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleViewApplications = (id: string) => {
    router.push(`/bpo/vacancies/${id}/applications`);
  };

  const handleDuplicateVacancy = async (id: string) => {
    try {
      setLoading(true);
      
      // Get the vacancy to duplicate
      const { data: vacancy, error: fetchError } = await supabase
        .from('job_postings')
        .select('*')
        .eq('id', id)
        .single();
        
      if (fetchError || !vacancy) {
        throw fetchError;
      }
      
      // Create a duplicate with a new ID and "draft" status
      const newVacancy = {
        ...vacancy,
        id: undefined, // Let Supabase generate a new ID
        title: `${vacancy.title} (Copy)`,
        status: 'draft',
        created_at: new Date()
      };
      
      const { data: newVacancyData, error: insertError } = await supabase
        .from('job_postings')
        .insert(newVacancy)
        .select()
        .single();
        
      if (insertError) {
        throw insertError;
      }
      
      // Add the new vacancy to the local state
      setVacancies([
        {
          ...newVacancyData,
          application_count: 0,
          applications: []
        },
        ...vacancies
      ]);
      
      // Update stats
      setVacancyStats({
        ...vacancyStats,
        total: vacancyStats.total + 1,
        draft: vacancyStats.draft + 1
      });
      
      toast({
        title: "Vacancy duplicated",
        description: "The vacancy has been duplicated and saved as a draft",
      });
      
    } catch (error) {
      console.error('Error duplicating vacancy:', error);
      toast({
        title: "Duplication failed",
        description: "There was an error duplicating the vacancy",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleArchiveVacancy = async (id: string, currentStatus: string) => {
    const newStatus = currentStatus === 'archived' ? 'published' : 'archived';
    const action = currentStatus === 'archived' ? 'publish' : 'archive';
    
    if (!window.confirm(`Are you sure you want to ${action} this vacancy?`)) {
      return;
    }
    
    try {
      setLoading(true);
      
      const { error } = await supabase
        .from('job_postings')
        .update({ status: newStatus })
        .eq('id', id);
        
      if (error) {
        throw error;
      }
      
      // Update local state
      setVacancies(vacancies.map(vacancy => {
        if (vacancy.id === id) {
          return { ...vacancy, status: newStatus };
        }
        return vacancy;
      }));
      
      // Update stats
      setVacancyStats({
        ...vacancyStats,
        active: newStatus === 'published' 
          ? vacancyStats.active + 1 
          : vacancyStats.active - 1,
        closed: newStatus === 'archived' 
          ? vacancyStats.closed + 1 
          : vacancyStats.closed - 1
      });
      
      toast({
        title: `Vacancy ${action}d`,
        description: `The vacancy has been ${action}d successfully`,
      });
      
    } catch (error) {
      console.error(`Error ${action}ing vacancy:`, error);
      toast({
        title: `${action.charAt(0).toUpperCase() + action.slice(1)} failed`,
        description: `There was an error ${action}ing the vacancy`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const showDeleteConfirmation = (id: string, title: string) => {
    setVacancyToDelete({id, title});
    setConfirmTitle('');
    setHasApplications(false);
    setApplicationCount(0);
    
    // Check if there are applications for this vacancy
    const checkApplications = async () => {
      try {
        const { count, error } = await supabase
          .from('applications')
          .select('*', { count: 'exact', head: true })
          .eq('job_id', id);
          
        if (!error && count && count > 0) {
          setHasApplications(true);
          setApplicationCount(count);
        }
      } catch (error) {
        console.error('Error checking applications:', error);
      }
    };
    
    checkApplications();
    setDeleteDialogOpen(true);
  };
  
  const confirmDelete = async () => {
    if (!vacancyToDelete) return;
    
    try {
      setLoading(true);
      
      // First, verify that the vacancy exists and belongs to this BPO
      const { data: vacancyCheck, error: checkError } = await supabase
        .from('job_postings')
        .select('id')
        .eq('id', vacancyToDelete.id)
        .eq('bpo_id', bpoData.id)
        .single();
        
      if (checkError) {
        console.error('Error verifying vacancy:', checkError);
        throw new Error(`Vacancy verification failed: ${checkError.message}`);
      }
      
      if (!vacancyCheck) {
        throw new Error('Vacancy not found or you don\'t have permission to delete it');
      }
      
      // First delete all applications for this vacancy to avoid foreign key constraint errors
      if (hasApplications) {
        console.log(`Attempting to delete ${applicationCount} applications for vacancy ${vacancyToDelete.id}`);
        try {
          // First check that the applications still exist
          const { count, error: recountError } = await supabase
            .from('applications')
            .select('*', { count: 'exact', head: true })
            .eq('job_id', vacancyToDelete.id);
            
          if (recountError) {
            console.log('Error recounting applications:', recountError);
            // Continue anyway - applications might have been deleted by another process
          } else {
            console.log(`Confirmed ${count} applications still exist`);
          }
          
          // Delete the applications
          const { error: applicationDeleteError } = await supabase
            .from('applications')
            .delete()
            .eq('job_id', vacancyToDelete.id);
            
          if (applicationDeleteError) {
            console.error('Error deleting applications:', applicationDeleteError);
            throw new Error(`Application deletion failed: ${applicationDeleteError.message}`);
          }
          
          console.log('Applications deleted successfully');
        } catch (appDeleteError) {
          console.error('Error during application deletion:', appDeleteError);
          // Continue with vacancy deletion anyway
        }
      }
      
      // After applications are deleted (if any), delete the vacancy
      console.log(`Attempting to delete vacancy ${vacancyToDelete.id}`);
      const { error } = await supabase
        .from('job_postings')
        .delete()
        .eq('id', vacancyToDelete.id)
        .eq('bpo_id', bpoData.id); // Security check to ensure user has permission
        
      if (error) {
        console.error('Specific delete error:', error);
        throw new Error(`Vacancy deletion failed: ${error.message}`);
      }
      
      // Update local state
      const deletedVacancyObject = vacancies.find(v => v.id === vacancyToDelete.id);
      setVacancies(vacancies.filter(vacancy => vacancy.id !== vacancyToDelete.id));
      
      // Update stats
      if (deletedVacancyObject) {
        setVacancyStats({
          ...vacancyStats,
          total: vacancyStats.total - 1,
          active: deletedVacancyObject.status === 'published' ? vacancyStats.active - 1 : vacancyStats.active,
          draft: deletedVacancyObject.status === 'draft' ? vacancyStats.draft - 1 : vacancyStats.draft,
          closed: deletedVacancyObject.status === 'archived' ? vacancyStats.closed - 1 : vacancyStats.closed,
          applications: vacancyStats.applications - (deletedVacancyObject.application_count || 0)
        });
      }
      
      toast({
        title: "Vacancy deleted",
        description: "The vacancy has been permanently deleted",
      });
      
      // Close dialog
      setDeleteDialogOpen(false);
      
    } catch (error) {
      console.error('Error deleting vacancy:', error);
      toast({
        title: "Deletion failed",
        description: error instanceof Error ? error.message : "There was an error deleting the vacancy. It may have associated data preventing deletion.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteVacancy = async (id: string, title: string) => {
    showDeleteConfirmation(id, title);
  };

  const getFilteredVacancies = () => {
    return vacancies.filter(vacancy => {
      // Apply status filter
      if (statusFilter !== 'all' && vacancy.status !== statusFilter) {
        return false;
      }
      
      // Apply search query
      if (searchQuery && !vacancy.title.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }
      
      return true;
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 border-green-200 dark:border-green-800">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Published
          </Badge>
        );
      case 'draft':
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
            Draft
          </Badge>
        );
      case 'archived':
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 border-red-200 dark:border-red-800">
            <XCircle className="h-3 w-3 mr-1" />
            Archived
          </Badge>
        );
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const formatSalary = (salaryRange: any) => {
    if (!salaryRange) return 'Not specified';

    const { min, max, currency = 'USD' } = salaryRange;

    if (min && max && typeof min === 'number' && typeof max === 'number') {
      return `${currency} ${min.toLocaleString()} - ${max.toLocaleString()}`;
    } else if (min && typeof min === 'number') {
      return `${currency} ${min.toLocaleString()}+`;
    } else if (max && typeof max === 'number') {
      return `Up to ${currency} ${max.toLocaleString()}`;
    }

    return 'Not specified';
  };

  const formatLocation = (location: any) => {
    if (!location) return 'Remote';
    
    const { city, country, remote } = location;
    
    if (remote) return 'Remote';
    
    if (city && country) {
      return `${city}, ${country}`;
    } else if (city) {
      return city;
    } else if (country) {
      return country;
    }
    
    return 'Not specified';
  };

  // Add this function to handle the success of vacancy creation
  const handleVacancyCreateSuccess = () => {
    // Close the modal
    setIsCreateModalOpen(false);
    
    // Refresh the vacancies data
    fetchData();
  };
  
  // Add a function to handle the success of vacancy editing
  const handleVacancyEditSuccess = () => {
    // Close the modal
    setIsEditModalOpen(false);
    setCurrentVacancy(null);
    setEditFormData(undefined);
    
    // Refresh the vacancies data
    fetchData();
  };
  
  // Add a function to close the create modal
  const handleCreateModalClose = () => {
    setIsCreateModalOpen(false);
  };
  
  // Add a function to close the edit modal
  const handleEditModalClose = () => {
    setIsEditModalOpen(false);
    setCurrentVacancy(null);
    setEditFormData(undefined);
  };

  if (loading && vacancies.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading vacancies...</p>
        </div>
      </div>
    );
  }

  const filteredVacancies = getFilteredVacancies();

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-8">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Vacancies</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Manage your job vacancies and openings
          </p>
        </div>
        
        <Button onClick={handleCreateVacancy}>
          <Plus className="h-4 w-4 mr-2" />
          <span>Create Vacancy</span>
        </Button>
      </div>
      
      {/* Create Vacancy Slide Out Panel */}
      <Sheet open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <SheetContent side="right-wide" className="overflow-y-auto">
          <SheetHeader className="mb-5">
            <SheetTitle>Create New Job Vacancy</SheetTitle>
            <SheetDescription>
              Fill in the details to create a new job opening. You can save it as a draft or publish it immediately.
            </SheetDescription>
          </SheetHeader>
          
          {bpoData && bpoData.id && (
            <VacancyForm
              bpoId={bpoData.id}
              onSuccess={handleVacancyCreateSuccess}
              onCancel={handleCreateModalClose}
              isEditing={false}
            />
          )}
        </SheetContent>
      </Sheet>
      
      {/* Edit Vacancy Slide Out Panel */}
      <Sheet open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <SheetContent side="right-wide" className="overflow-y-auto">
          <SheetHeader className="mb-5">
            <SheetTitle>Edit Job Vacancy</SheetTitle>
            <SheetDescription>
              Update details for this job vacancy.
            </SheetDescription>
          </SheetHeader>
          
          {bpoData && bpoData.id && currentVacancy && editFormData && (
            <VacancyForm
              initialData={editFormData}
              bpoId={bpoData.id}
              onSuccess={handleVacancyEditSuccess}
              onCancel={handleEditModalClose}
              isEditing={true}
              vacancyId={currentVacancy.id}
            />
          )}
        </SheetContent>
      </Sheet>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-blue-500 rounded-md p-3">
                <Briefcase className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Total Vacancies
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {vacancyStats.total}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                <CheckCircle2 className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Active Vacancies
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {vacancyStats.active}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-amber-500 rounded-md p-3">
                <Clock className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Draft Vacancies
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {vacancyStats.draft}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-purple-500 rounded-md p-3">
                <Users className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Total Applications
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {vacancyStats.applications}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500 dark:text-gray-400" />
          <Input
            placeholder="Search vacancies..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="flex gap-2">
          <Button
            variant={statusFilter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('all')}
          >
            All
          </Button>
          <Button
            variant={statusFilter === 'published' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('published')}
          >
            Active
          </Button>
          <Button
            variant={statusFilter === 'draft' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('draft')}
          >
            Draft
          </Button>
          <Button
            variant={statusFilter === 'archived' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('archived')}
          >
            Archived
          </Button>
        </div>
      </div>
      
      {/* Vacancies Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Job Title</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Job Type</TableHead>
                <TableHead>Salary</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Applications</TableHead>
                <TableHead>Deadline</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredVacancies.map((vacancy) => (
                <TableRow key={vacancy.id}>
                  <TableCell className="font-medium">
                    {vacancy.title}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <MapPin className="h-3.5 w-3.5 mr-1 text-gray-500" />
                      <span>{formatLocation(vacancy.location)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {vacancy.job_type}
                  </TableCell>
                  <TableCell>
                    {formatSalary(vacancy.salary_range)}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(vacancy.status)}
                  </TableCell>
                  <TableCell>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleViewApplications(vacancy.id)}
                    >
                      <Users className="h-3.5 w-3.5 mr-1" />
                      {vacancy.application_count}
                    </Button>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <CalendarDays className="h-3.5 w-3.5 mr-1 text-gray-500" />
                      <span>
                        {vacancy.application_deadline 
                          ? new Date(vacancy.application_deadline).toLocaleDateString() 
                          : 'No deadline'}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        
                        <DropdownMenuItem onClick={() => handleEditVacancy(vacancy.id)}>
                          <Edit className="h-4 w-4 mr-2" />
                          <span>Edit Vacancy</span>
                        </DropdownMenuItem>
                        
                        <DropdownMenuItem onClick={() => handleViewApplications(vacancy.id)}>
                          <Eye className="h-4 w-4 mr-2" />
                          <span>View Applications</span>
                        </DropdownMenuItem>
                        
                        <DropdownMenuItem onClick={() => handleDuplicateVacancy(vacancy.id)}>
                          <Copy className="h-4 w-4 mr-2" />
                          <span>Duplicate</span>
                        </DropdownMenuItem>
                        
                        <DropdownMenuSeparator />
                        
                        <DropdownMenuItem 
                          onClick={() => handleArchiveVacancy(vacancy.id, vacancy.status)}
                          className={vacancy.status === 'archived' ? 'text-green-600' : 'text-red-600'}
                        >
                          <Archive className="h-4 w-4 mr-2" />
                          <span>{vacancy.status === 'archived' ? 'Publish Vacancy' : 'Archive Vacancy'}</span>
                        </DropdownMenuItem>
                        
                        <DropdownMenuSeparator />
                        
                        <DropdownMenuItem 
                          onClick={() => showDeleteConfirmation(vacancy.id, vacancy.title)}
                          className="text-red-600 font-medium"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          <span>Delete Permanently</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
              
              {filteredVacancies.length === 0 && (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center">
                      <Briefcase className="h-8 w-8 mb-2 text-gray-400" />
                      <p>No vacancies found</p>
                      <p className="text-sm mt-1">
                        {vacancies.length > 0 
                          ? 'Try changing your filters or search query' 
                          : 'Create your first job vacancy to get started'}
                      </p>
                      
                      {vacancies.length === 0 && (
                        <Button 
                          variant="outline" 
                          className="mt-4"
                          onClick={handleCreateVacancy}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          <span>Create Vacancy</span>
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600 flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Permanently Delete Vacancy
            </AlertDialogTitle>
            
            {/* Description content */}
            <div className="text-sm text-muted-foreground mt-2">
              {hasApplications ? (
                <div className="space-y-2">
                  <div className="font-semibold">
                    Warning: This vacancy has {applicationCount} application(s).
                  </div>
                  <div>
                    Deleting this vacancy will also permanently delete all associated applications. 
                    This action <span className="font-semibold">cannot be undone</span>.
                  </div>
                </div>
              ) : (
                <div>
                  This will permanently delete this vacancy. 
                  This action <span className="font-semibold">cannot be undone</span>.
                </div>
              )}
              
              <div className="mt-4">
                <div className="mb-2">To confirm deletion, please type <span className="font-semibold">{vacancyToDelete?.title}</span> below:</div>
                <Input 
                  value={confirmTitle}
                  onChange={(e) => setConfirmTitle(e.target.value)}
                  placeholder="Enter vacancy title to confirm"
                  className="mt-1"
                />
              </div>
            </div>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete} 
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              disabled={loading || confirmTitle !== vacancyToDelete?.title}
            >
              {loading ? "Deleting..." : "Delete Permanently"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

// Main exported component with Suspense boundary
export default function BPOVacanciesPage() {
  return (
    <Suspense fallback={<div className="p-6">Loading vacancies...</div>}>
      <BPOVacanciesContent />
    </Suspense>
  );
} 