import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from '@supabase/supabase-js'
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { Database } from "@/types/supabase"
import { ArrowLeft, Clock, CheckCircle, Play, FileText, Lock, Layout, 
  Users, BarChart, BookOpen, ChevronRight, AlertCircle, FileQuestion, Video } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { RichTextDisplay } from "@/components/ui/rich-text-display"

// Create admin client for operations that need to bypass RLS
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ""
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ""
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey)

export default async function ModuleDetailsPage({
  params,
}: {
  params: { moduleId: string }
}) {
  const moduleId = params.moduleId
  const supabase = createServerComponentClient<Database>({ cookies })
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect("/login")
  }
  
  // Get user data
  const { data: userData, error: userError } = await supabase
    .from("users")
    .select("id, role")
    .eq("id", session.user.id)
    .single()
  
  if (userError || !userData || userData.role !== "prospect") {
    redirect("/unauthorized")
  }
  
  // Get the prospect's profile with admin client to bypass RLS
  let prospectData = null
  
  try {
    const { data } = await adminClient
      .from("prospects")
      .select("id, training_status")
      .eq("user_id", userData.id)
    
    if (data && data.length > 0) {
      prospectData = data[0]
    }
  } catch (error) {
    console.error("Error fetching prospect data:", error)
  }
  
  const prospectId = prospectData?.id
  
  // Fetch the module using admin client
  const { data: module, error: moduleError } = await adminClient
    .from("training_modules")
    .select("*")
    .eq("id", moduleId)
    .eq("status", "published")
    .single()
  
  if (moduleError || !module) {
    redirect("/prospect/training")
  }
  
  // Fetch the lessons using admin client
  const { data: lessons, error: lessonsError } = await adminClient
    .from("lessons")
    .select("*")
    .eq("module_id", moduleId)
    .order("order_index", { ascending: true })
  
  if (lessonsError) {
    console.error("Error fetching lessons:", lessonsError)
  }
  
  // Fetch module assessments (quizzes)
  const { data: moduleAssessmentsData, error: moduleAssessmentsError } = await adminClient
    .from("module_assessments")
    .select(`
      id, 
      order_index, 
      is_required, 
      passing_required,
      assessments:assessment_id (
        id, 
        title, 
        description, 
        duration, 
        total_questions
      )
    `)
    .eq("module_id", moduleId)
    .order("order_index", { ascending: true })
    
  if (moduleAssessmentsError) {
    console.error("Error fetching module assessments:", moduleAssessmentsError)
  }
  
  // Convert module assessments to lesson format for the content list
  const quizLessons = moduleAssessmentsData ? moduleAssessmentsData.map(moduleAssessment => {
    const assessment = moduleAssessment.assessments as any;
    
    // Extract duration in minutes from the duration string if available
    let durationMinutes = null;
    if (assessment?.duration) {
      const match = assessment.duration.match(/(\d+)/);
      if (match) {
        durationMinutes = parseInt(match[1], 10);
      }
    }
    
    return {
      id: moduleAssessment.id,
      title: assessment?.title || "Untitled Quiz",
      description: assessment?.description || null,
      order_index: moduleAssessment.order_index,
      duration_minutes: durationMinutes,
      module_id: moduleId,
      is_quiz: true,
      lesson_type: 'quiz',
      is_required: moduleAssessment.is_required || false,
      passing_required: moduleAssessment.passing_required || false,
      assessment_id: assessment?.id,
      total_questions: assessment?.total_questions || 0
    };
  }) : [];
  
  // Combine lessons and quizzes, sort by order_index
  const allContent = [...(lessons || []), ...quizLessons].sort((a, b) => a.order_index - b.order_index);
  
  // Calculate progress if prospect exists
  let lessonProgress: Record<string, any> = {}
  
  if (prospectId && allContent && allContent.length > 0) {
    for (const lessonItem of allContent) {
      try {
        // For quizzes, handle differently than regular lessons
        if (lessonItem.is_quiz) {
          // TODO: Add actual quiz progress tracking here
          lessonProgress[lessonItem.id] = {
            status: "not_started",
            progressPercentage: 0,
            activities: [],
          }
          continue
        }
        
        const { data: activities } = await adminClient
          .from("activities")
          .select("id, title, type, order_index")
          .eq("lesson_id", lessonItem.id)
          .order("order_index", { ascending: true })
        
        if (!activities || activities.length === 0) {
          lessonProgress[lessonItem.id] = {
            status: "not_started",
            progressPercentage: 0,
            activities: [],
          }
          continue
        }
        
        const activityIds = activities.map(activity => activity.id)
        const { data: progressRecords } = await adminClient
          .from("progress_records")
          .select("*")
          .eq("prospect_id", prospectId)
          .in("activity_id", activityIds)
        
        const completedActivities = progressRecords?.filter(record => record.status === "completed") || []
        const progressPercentage = Math.round((completedActivities.length / activities.length) * 100)
        
        let status = "not_started"
        if (progressPercentage === 100) {
          status = "completed"
        } else if (progressPercentage > 0) {
          status = "in_progress"
        }
        
        lessonProgress[lessonItem.id] = {
          status,
          progressPercentage,
          activities,
          progressRecords: progressRecords || [],
        }
      } catch (error) {
        console.error(`Error fetching progress for lesson ${lessonItem.id}:`, error)
        continue
      }
    }
  }
  
  // Calculate overall module progress
  const totalLessons = allContent?.length || 0
  const completedLessons = Object.values(lessonProgress).filter(
    (progress: any) => progress.status === "completed"
  ).length
  const inProgressLessons = Object.values(lessonProgress).filter(
    (progress: any) => progress.status === "in_progress"
  ).length
  
  const moduleProgressPercentage = totalLessons > 0 
    ? Math.round((completedLessons / totalLessons) * 100) 
    : 0
  
  const moduleStatus = completedLessons === totalLessons && totalLessons > 0
    ? "completed"
    : completedLessons > 0 || inProgressLessons > 0
      ? "in_progress"
      : "not_started"
  
  // Format descriptions to limit length
  const truncateDescription = (description: string | null, maxLength = 100) => {
    if (!description) return "";
    
    let textContent = description;
    if (description.includes("<")) {
      textContent = description.replace(/<[^>]*>/g, '');
    }
    
    return textContent.length > maxLength
      ? textContent.substring(0, maxLength).trim() + "..."
      : textContent;
  };
  
  // Determine next lesson to continue
  const getNextLesson = () => {
    if (!allContent || allContent.length === 0) return null;
    
    // First check for in-progress lessons
    const inProgressLesson = allContent.find(lesson => {
      const progress = lessonProgress[lesson.id] || {}
      return progress.status === "in_progress"
    });
    
    if (inProgressLesson) return inProgressLesson;
    
    // Then find first not-started lesson
    const notStartedLesson = allContent.find(lesson => {
      const progress = lessonProgress[lesson.id] || {}
      return progress.status === "not_started"
    });
    
    if (notStartedLesson) return notStartedLesson;
    
    // If all lessons are completed, return the first one
    return allContent[0];
  };
  
  const nextLesson = getNextLesson();
  
  return (
    <main className="min-h-screen bg-background">
      {/* Header with back navigation and title */}
      <div className="border-b sticky top-0 bg-background/95 backdrop-blur-sm z-10">
        <div className="max-w-screen-xl mx-auto px-4 sm:px-6 py-4">
          <div className="flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-4">
              <Link href="/prospect/training">
                <ArrowLeft className="h-4 w-4 mr-1" />
                <span>Back</span>
              </Link>
            </Button>
            <h1 className="font-medium text-lg text-foreground truncate">{module.title}</h1>
          </div>
        </div>
      </div>
      
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 py-6 md:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Main content column */}
          <div className="lg:col-span-8 space-y-6">
            {/* Module info card */}
            <Card className="overflow-hidden border-none shadow-sm">
              <CardContent className="p-0">
                {/* Module status bar */}
                {moduleStatus !== "not_started" && (
                  <div className="bg-primary/5 dark:bg-primary/10 px-6 py-3 flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <BarChart className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        {moduleStatus === "completed" 
                          ? "Module completed" 
                          : `${moduleProgressPercentage}% complete`}
                      </span>
                    </div>
                    <Progress 
                      value={moduleProgressPercentage} 
                      className="w-24 h-2 bg-primary/10 [&>div]:bg-primary"
                    />
                  </div>
                )}
                
                <div className="p-6 bg-blue-50 dark:bg-blue-900/10">
                  {/* Module metadata */}
                  <div className="flex flex-wrap gap-4 mb-6">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Layout className="h-4 w-4 mr-2" />
                      <span>{totalLessons} {totalLessons === 1 ? 'lesson' : 'lessons'}</span>
                    </div>
                    
                    {module.duration_minutes && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="h-4 w-4 mr-2" />
                        <span>
                          {Math.round(module.duration_minutes / 60)} hours
                        </span>
                      </div>
                    )}
                    
                    {/* Example of another metadata field if needed */}
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Users className="h-4 w-4 mr-2" />
                      <span>For BPO prospects</span>
                    </div>
                  </div>
                  
                                     {/* Description */}
                   <div className="prose prose-xs max-w-none dark:prose-invert text-sm [&>p]:text-sm [&>ul]:text-sm [&>ol]:text-sm">
                     <RichTextDisplay 
                       content={module.description || "<p>No description available for this module.</p>"} 
                     />
                   </div>
                  
                  {/* Continue button - only if there are lessons */}
                  {allContent && allContent.length > 0 && (
                    <div className="mt-6">
                      <Button 
                        size="lg"
                        className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white"
                        asChild
                      >
                        <Link href={`/prospect/training/${moduleId}/lessons/${nextLesson?.id}`}>
                          {moduleStatus === "completed" ? (
                            <>Review Module</>
                          ) : moduleStatus === "in_progress" ? (
                            <>Continue Learning</>
                          ) : (
                            <>Start Module</>
                          )}
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
            
            {/* Lessons section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Course Content</h2>
                <div className="text-sm text-muted-foreground">
                  {completedLessons}/{totalLessons} completed
                </div>
              </div>
              
              {/* Empty state */}
              {(!allContent || allContent.length === 0) && (
                <Card className="border-dashed bg-transparent py-12">
                  <CardContent className="flex flex-col items-center text-center p-6">
                    <AlertCircle className="h-10 w-10 text-muted-foreground mb-3" />
                    <h3 className="text-lg font-medium mb-1">No lessons available</h3>
                    <p className="text-sm text-muted-foreground max-w-md">
                      This module doesn't have any content yet. Please check back later.
                    </p>
                  </CardContent>
                </Card>
              )}
              
              {/* Lesson list */}
              {allContent && allContent.length > 0 && (
                <div className="divide-y rounded-md border overflow-hidden">
                  {allContent.map((content, index) => {
                    const progress = lessonProgress[content.id] || { 
                      status: "not_started", 
                      progressPercentage: 0
                    };
                    
                    const isCompleted = progress.status === "completed";
                    const isInProgress = progress.status === "in_progress";
                    const isLocked = false; // Logic for lesson locking would go here
                    const isQuiz = content.is_quiz || content.lesson_type === 'quiz';
                    
                    return (
                      <div 
                        key={content.id} 
                        className={`bg-card hover:bg-accent/5 transition-colors ${
                          isLocked ? "opacity-60" : ""
                        }`}
                      >
                        <Link 
                          href={isLocked ? "#" : `/prospect/training/${moduleId}/lessons/${content.id}`}
                          className={`block ${isLocked ? "cursor-not-allowed" : ""}`}
                        >
                          <div className="px-4 py-3 sm:px-6 sm:py-4">
                            <div className="flex items-center justify-between">
                              <div className="min-w-0 flex-1">
                                <div className="flex items-center">
                                  {/* Status indicator */}
                                  <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0 ${
                                    isCompleted 
                                      ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400" 
                                      : isInProgress 
                                        ? "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
                                        : isLocked
                                          ? "bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400"
                                          : isQuiz
                                            ? "bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400"
                                            : "bg-primary/10 text-primary"
                                  }`}>
                                    {isCompleted ? (
                                      <CheckCircle className="h-3.5 w-3.5" />
                                    ) : isLocked ? (
                                      <Lock className="h-3.5 w-3.5" />
                                    ) : isQuiz ? (
                                      <FileQuestion className="h-3.5 w-3.5" />
                                    ) : (
                                      <Video className="h-3.5 w-3.5" />
                                    )}
                                  </div>
                                  
                                  <div className="min-w-0 flex-1">
                                    <h3 className="text-sm font-medium truncate flex items-center">
                                      {content.title}
                                      {isQuiz && (
                                        <Badge 
                                          variant="outline" 
                                          className="ml-2 bg-purple-50 text-purple-700 border-purple-200 text-xs"
                                        >
                                          Quiz
                                        </Badge>
                                      )}
                                    </h3>
                                    
                                    <div className="flex items-center mt-1">
                                      {content.duration_minutes && (
                                        <span className="text-xs text-muted-foreground flex items-center">
                                          <Clock className="h-3 w-3 mr-1" />
                                          {content.duration_minutes} min
                                        </span>
                                      )}
                                      
                                      {isQuiz && content.total_questions > 0 && (
                                        <span className="text-xs text-muted-foreground flex items-center ml-3">
                                          <FileQuestion className="h-3 w-3 mr-1" />
                                          {content.total_questions} {content.total_questions === 1 ? 'question' : 'questions'}
                                        </span>
                                      )}
                                      
                                      {isInProgress && (
                                        <Badge 
                                          variant="secondary" 
                                          className="ml-2 text-xs px-1.5 h-5 rounded-sm"
                                        >
                                          In progress
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              
                              <ChevronRight className="h-5 w-5 text-muted-foreground/50" />
                            </div>
                            
                            {/* Progress bar - only for in-progress lessons */}
                            {isInProgress && (
                              <div className="mt-2">
                                <Progress 
                                  value={progress.progressPercentage} 
                                  className="h-1 [&>div]:bg-blue-500"
                                />
                              </div>
                            )}
                          </div>
                        </Link>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
          
          {/* Sidebar column */}
          <div className="lg:col-span-4">
            <div className="lg:sticky lg:top-24 space-y-6">
              {/* Module progress card */}
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-lg font-medium mb-4">Your Progress</h3>
                  
                  <div className="space-y-3 mb-6">
                    <div className="flex justify-between text-sm">
                      <span>Completion</span>
                      <span className="font-medium">{moduleProgressPercentage}%</span>
                    </div>
                    <Progress 
                      value={moduleProgressPercentage} 
                      className="h-2 [&>div]:bg-primary"
                    />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="bg-primary/5 dark:bg-primary/10 rounded-md p-3">
                      <div className="text-2xl font-bold">{totalLessons}</div>
                      <div className="text-xs text-muted-foreground">Total</div>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 rounded-md p-3">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">{completedLessons}</div>
                      <div className="text-xs text-muted-foreground">Completed</div>
                    </div>
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-3">
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{inProgressLessons}</div>
                      <div className="text-xs text-muted-foreground">In Progress</div>
                    </div>
                  </div>
                  
                  {/* Action button based on status */}
                  {allContent && allContent.length > 0 && (
                    <div className="mt-5">
                      <Button 
                        variant={moduleStatus === "not_started" ? "default" : "secondary"}
                        className="w-full"
                        asChild
                      >
                        <Link href={`/prospect/training/${moduleId}/lessons/${nextLesson?.id}`}>
                          <span className="flex items-center justify-center">
                            {moduleStatus === "completed" ? (
                              <>
                                <FileText className="h-4 w-4 mr-2" />
                                Review First Lesson
                              </>
                            ) : moduleStatus === "in_progress" ? (
                              <>
                                <BookOpen className="h-4 w-4 mr-2" />
                                Continue Learning
                              </>
                            ) : (
                              <>
                                <Play className="h-4 w-4 mr-2" />
                                Start First Lesson
                              </>
                            )}
                          </span>
                        </Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
              
              {/* Additional card for related modules, resources, or help */}
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-lg font-medium mb-4">Need Help?</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    If you need assistance with this training module or have questions, please reach out to our support team.
                  </p>
                  <Button variant="outline" className="w-full">Contact Support</Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
} 