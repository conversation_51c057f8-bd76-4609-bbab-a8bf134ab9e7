@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Rich Text Editor Styles */
.ProseMirror ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.ProseMirror ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.ProseMirror li {
  margin: 0.25rem 0;
  padding-left: 0.25rem;
}

.ProseMirror li p {
  margin: 0;
}

/* Basic list styling */
.ProseMirror ul[data-type="bulletList"] {
  list-style-type: disc;
}

.ProseMirror ol[data-type="orderedList"] {
  list-style-type: decimal;
}

/* Ensure indented lists maintain proper bullet/number styling */
.ProseMirror ul[style*="margin-left"] {
  list-style-type: disc;
  /* Override default list indentation to keep bullets aligned with text */
  padding-left: 1.5rem !important;
}

.ProseMirror ol[style*="margin-left"] {
  list-style-type: decimal;
  /* Override default list indentation to keep numbers aligned with text */
  padding-left: 1.5rem !important;
}

/* Fix for nested lists to ensure proper alignment */
.ProseMirror ul[style*="margin-left"] ul,
.ProseMirror ol[style*="margin-left"] ol,
.ProseMirror ul[style*="margin-left"] ol,
.ProseMirror ol[style*="margin-left"] ul {
  margin-left: 0 !important;
}

/* Special cases for proper bullet/number alignment */
.ProseMirror li[style*="margin-left"] {
  margin-left: 0 !important; /* Remove any individual list item indentation */
}

/* Remove any additional spacing caused by indentation in the editor */
.ProseMirror .is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

.bg-grid-black {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(15 23 42 / 0.04)'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

.bg-grid-white {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(255 255 255 / 0.05)'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

@keyframes wave {
  0% {
    transform: rotate(0deg);
  }
  10% {
    transform: rotate(14deg);
  }
  20% {
    transform: rotate(-8deg);
  }
  30% {
    transform: rotate(14deg);
  }
  40% {
    transform: rotate(-4deg);
  }
  50% {
    transform: rotate(10deg);
  }
  60% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

.animate-wave {
  animation: wave 2.5s ease-in-out infinite;
  transform-origin: 70% 70%;
  display: inline-block;
}

@layer utilities {
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent dark:from-blue-400 dark:to-indigo-400;
  }

  .max-content-width {
    @apply max-w-6xl mx-auto w-full;
  }

  .content-container {
    @apply w-full max-w-6xl mx-auto;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Indentation level classes to control exact spacing */
.ProseMirror .indent-1 {
  margin-left: 2em !important;
}

.ProseMirror .indent-2 {
  margin-left: 4em !important;
}

.ProseMirror .indent-3 {
  margin-left: 6em !important;
}

.ProseMirror .indent-4 {
  margin-left: 8em !important;
}

.ProseMirror .indent-5 {
  margin-left: 10em !important;
}

/* Ensure proper alignment with bullets/numbers regardless of indentation */
.ProseMirror ul.indent-1,
.ProseMirror ul.indent-2,
.ProseMirror ul.indent-3,
.ProseMirror ul.indent-4,
.ProseMirror ul.indent-5 {
  padding-left: 1.5rem !important;
  list-style-type: disc;
}

.ProseMirror ol.indent-1,
.ProseMirror ol.indent-2,
.ProseMirror ol.indent-3,
.ProseMirror ol.indent-4,
.ProseMirror ol.indent-5 {
  padding-left: 1.5rem !important;
  list-style-type: decimal;
}

/* Fix nested lists to prevent extra indentation */
.ProseMirror ul.indent-1 ul,
.ProseMirror ul.indent-2 ul,
.ProseMirror ul.indent-3 ul,
.ProseMirror ul.indent-4 ul,
.ProseMirror ul.indent-5 ul,
.ProseMirror ol.indent-1 ol,
.ProseMirror ol.indent-2 ol,
.ProseMirror ol.indent-3 ol,
.ProseMirror ol.indent-4 ol,
.ProseMirror ol.indent-5 ol,
.ProseMirror ul.indent-1 ol,
.ProseMirror ul.indent-2 ol,
.ProseMirror ul.indent-3 ol,
.ProseMirror ul.indent-4 ol,
.ProseMirror ul.indent-5 ol,
.ProseMirror ol.indent-1 ul,
.ProseMirror ol.indent-2 ul,
.ProseMirror ol.indent-3 ul,
.ProseMirror ol.indent-4 ul,
.ProseMirror ol.indent-5 ul {
  margin-left: 0 !important;
}
