{"timestamp": "2025-06-06T19:24:04.343Z", "database_url": "https://snihmpmulfxhrjjcnzau.supabase.co", "tables": {"users": {"exists": true, "columns": ["id", "email", "encrypted_password", "full_name", "role", "status", "is_password_change_required", "last_login", "created_at", "updated_at", "avatar_url", "is_placeholder"], "sample": {"id": "8cb815d2-c488-467e-a62e-83075741bdc2", "email": "<EMAIL>", "encrypted_password": "MANAGED_BY_SUPABASE_AUTH", "full_name": "<PERSON>", "role": "bpo_admin", "status": "active", "is_password_change_required": false, "last_login": "2025-06-04T14:09:37.451+00:00", "created_at": "2025-05-17T21:17:50.697649+00:00", "updated_at": "2025-06-04T14:09:40.127095+00:00", "avatar_url": null, "is_placeholder": false}}, "prospects": {"exists": true, "columns": ["id", "user_id", "contact_info", "education", "experience", "skills", "intro_video_url", "resume_url", "profile_visibility", "training_status", "created_at", "updated_at", "profile_image"], "sample": {"id": "f08d34cc-6222-4a94-8476-ada6cad4abc3", "user_id": "d699cd51-019d-4380-9fd3-05e4f4bd775d", "contact_info": {"email": "<EMAIL>", "phone": "5016144542", "location": {"city": "City of Belmopan", "country": "Belize"}}, "education": [{"degree": "Diploma", "current": false, "end_year": 2020, "location": "Belize City, Belize", "start_year": 2016, "description": "", "institution": "<PERSON> High School", "field_of_study": "Business Administration"}], "experience": [{"title": "Customer Support Agent", "company": "Ready Call Center", "end_date": "April 2025", "end_year": 2025, "location": "Belize City, Belize", "end_month": "April", "start_date": "August 2020", "start_year": 2020, "description": "Designed intuitive UX to enhance functionality and usability, including prototyping in Figma.\nBuilt custom web applications, eCommerce platforms, and dynamic portals using WordPress CMS.", "start_month": "August"}], "skills": [{"name": "Customer Service", "level": "Advanced", "proficiency": 90}, {"name": "Microsoft Office", "level": "Advanced", "proficiency": 90}], "intro_video_url": "https://snihmpmulfxhrjjcnzau.supabase.co/storage/v1/object/public/profile-videos//SSYouTube.online_SELF-INTRODUCTION%20VIDEO%20_%2030%20SECONDS_720p.mp4", "resume_url": "https://snihmpmulfxhrjjcnzau.supabase.co/storage/v1/object/public/resumes/f08d34cc-6222-4a94-8476-ada6cad4abc3_1706c031-ea62-4361-b5f1-7f9effbbd2fe_Patricia_Espiritu_Resume.pdf", "profile_visibility": true, "training_status": "not_started", "created_at": "2025-05-17T21:11:57.267959+00:00", "updated_at": "2025-06-04T14:27:56.464751+00:00", "profile_image": "https://snihmpmulfxhrjjcnzau.supabase.co/storage/v1/object/public/profile-images/f08d34cc-6222-4a94-8476-ada6cad4abc3_1ca89b73-6899-4a93-a755-9ffb365b6683_profile-picture-smiling-millennial-asian-260nw-1836020740.PNG"}}, "bpos": {"exists": true, "columns": ["id", "name", "description", "logo_url", "industry", "size_range", "founded_year", "website_url", "location", "contact_email", "created_by", "reviews", "workplace_stats", "created_at", "updated_at", "phone"], "sample": {"id": "4fa51cb5-e415-40e0-ab91-772b907f4a08", "name": "Accelerate BPO", "description": "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.", "logo_url": "https://snihmpmulfxhrjjcnzau.supabase.co/storage/v1/object/public/company-logos/logos/logo-4fa51cb5-e415-40e0-ab91-772b907f4a08-1749046199557.png", "industry": "Customer Service", "size_range": "300-500", "founded_year": 2005, "website_url": "https://smooth.calls", "location": {"city": "Bronx", "address": "123 Fiery Street", "country": "Belize"}, "contact_email": "<EMAIL>", "created_by": "8b41a564-772e-4517-ad6e-26dd00d74f16", "reviews": [], "workplace_stats": {}, "created_at": "2025-05-17T21:39:34.743759+00:00", "updated_at": "2025-06-04T14:09:59.892787+00:00", "phone": "2237584"}}, "bpo_teams": {"exists": true, "columns": ["id", "bpo_id", "user_id", "role", "permissions", "invited_at", "accepted_at", "created_at", "updated_at", "is_placeholder", "invited_by"], "sample": {"id": "54fb15b3-d82b-44cd-b7a4-e96e7ecede55", "bpo_id": "4fa51cb5-e415-40e0-ab91-772b907f4a08", "user_id": "8cb815d2-c488-467e-a62e-83075741bdc2", "role": "admin", "permissions": {}, "invited_at": "2025-05-17T21:39:32.341+00:00", "accepted_at": "2025-05-17T21:39:32.341+00:00", "created_at": "2025-05-17T21:39:34.90659+00:00", "updated_at": "2025-05-26T02:28:46.667208+00:00", "is_placeholder": false, "invited_by": null}}, "training_modules": {"exists": true, "columns": ["id", "title", "description", "cover_image_url", "duration_minutes", "required_order", "status", "created_by", "created_at", "updated_at", "requires_assessment"], "sample": {"id": "067fd2f3-2873-4772-9503-ee4ecf06f02d", "title": "Problem Solving & Critical Thinking", "description": "<p>BPO agents are frontline problem-solvers. This module sharpens learners’ ability to assess, analyze, and resolve customer issues quickly and accurately. Through real-world call scenarios and hands-on activities, learners will develop the logical thinking and situational judgment that top-performing agents rely on.</p><p>They’ll learn how to troubleshoot common technical or service problems, apply structured thinking under pressure, and know when and how to escalate an issue to the next level. Just as importantly, they’ll master how to document interactions clearly and concisely—ensuring smooth handoffs and professional recordkeeping. The module ends with interactive mock calls using branching scenarios, allowing learners to practice applying what they've learned in realistic settings.</p><p><strong>Outcomes:</strong><br>\nBy the end of this module, learners will be able to:</p><ul class=\"list-disc pl-6 my-2\"><li class=\"pl-1 my-1\"><p>Use a structured process to troubleshoot common customer issues</p></li><li class=\"pl-1 my-1\"><p>Approach problems with logic, patience, and solution-oriented thinking</p></li><li class=\"pl-1 my-1\"><p>Identify when escalation is needed and follow proper protocols</p></li><li class=\"pl-1 my-1\"><p>Accurately document customer interactions for internal records</p></li><li class=\"pl-1 my-1\"><p>Demonstrate critical thinking through mock calls and decision trees</p><p></p></li></ul>", "cover_image_url": null, "duration_minutes": 60, "required_order": 5, "status": "published", "created_by": "8b41a564-772e-4517-ad6e-26dd00d74f16", "created_at": "2025-05-25T21:45:11.155318+00:00", "updated_at": "2025-06-04T12:16:47.472098+00:00", "requires_assessment": false}}, "lessons": {"exists": true, "columns": ["id", "module_id", "title", "description", "order_index", "duration_minutes", "created_at", "updated_at", "lesson_type", "video_url", "media_url"], "sample": {"id": "cc6aef31-a974-42e3-9b91-6a3dd8647ee0", "module_id": "7d5cec99-3c36-4394-a7cc-21b3bc5b6827", "title": "What is a BPO?", "description": "<p><strong>Welcome to your first step</strong> into the world of Business Process Outsourcing, or BPO. This lesson is designed to give you a strong foundation and help you understand what the BPO industry is all about — and why it might be the perfect fit for you.</p><h2>✅ What Do<strong>es “BPO” Mean?</strong></h2><p><strong>BPO stands for Business Process Outsourcing.</strong></p><p>It’s when companies — usually large ones — <em>outsource</em> or contract parts of their business to external providers. Instead of hiring people in-house for every task, they partner with other companies, often in different countries, to handle specific processes. These can include:</p><ol class=\"list-decimal pl-6 my-2 indent-1\" style=\"margin-left: 2em\"><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\"><strong>Customer Service</strong> (calls, emails, chats)</p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\"><strong>Technical Support</strong></p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\"><strong>Sales and Lead Generation</strong></p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\"><strong>Data Entry</strong></p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\"><strong>Billing and Collections</strong></p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\"><strong>Virtual Assistance</strong></p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\"><strong>Back-office tasks</strong> like payroll and HR</p></li></ol><h2>🌍 Why Do Companies Outsource?</h2><p>Outsourcing is a smart business move, especially for companies looking to grow, save money, or provide round-the-clock services. The three big reasons are:</p><ol class=\"list-decimal pl-6 my-2 indent-1\" style=\"margin-left: 2em\"><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\"><strong>Cost Savings</strong><br>It’s more affordable to hire skilled teams in countries like Belize than it is to hire staff in North America or Europe.</p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\"><strong>Focus on Core Business</strong><br>By outsourcing customer service or admin tasks, a company can stay focused on what it does best — whether that’s building apps, selling clothes, or developing new products.</p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\"><strong>Global Talent Access</strong><br>There’s amazing talent around the world — like YOU. BPOs connect that talent with global brands.</p></li></ol><h3>🧠 Real-Life Example</h3><p>Let’s say there’s a tech company in Canada that sells smart home devices. They get hundreds of customer questions every day about setting up the devices or fixing problems.</p><p>Instead of hiring a local support team — which would cost a lot and be hard to manage — they partner with a BPO in Belize. That BPO trains local agents to handle calls and chats on their behalf.</p><p>As a result:</p><ul class=\"list-disc pl-6 my-2 indent-1\" style=\"margin-left: 2em\"><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\">Customers get help quickly.</p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\">The Canadian company saves money.</p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\">And agents in Belize gain valuable, well-paying jobs.</p></li></ul><p>Everybody wins.</p><h2>📈 What Makes BPO So Powerful?</h2><p>BPOs play a <em>huge</em> role in helping businesses grow and serve customers better. In fact, the global BPO industry is worth <strong>hundreds of billions of dollars</strong> and continues to grow rapidly.</p><p>Countries like India, the Philippines, and now Belize are becoming <strong>key players</strong> because they offer:</p><ul class=\"list-disc pl-6 my-2 indent-1\" style=\"margin-left: 2em\"><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\">English-speaking talent</p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\">Great communication skills</p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\">Lower costs and high service quality</p></li></ul><p>That’s why international companies are turning to countries like ours to build their teams.</p><h2>👨🏾‍💻 Why It Matters for You</h2><p>Working in a BPO gives you:</p><ul class=\"list-disc pl-6 my-2 indent-1\" style=\"margin-left: 2em\"><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\">A chance to <strong>earn income</strong> in a fast-growing industry</p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\">Real-world <strong>experience with global brands</strong></p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\"><strong>Career growth</strong> and promotion opportunities</p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\">Valuable <strong>skills</strong> that are transferable to many industries</p></li><li class=\"pl-1 my-1\"><p style=\"margin-left: 2em\">Flexible hours and exposure to <strong>international cultures</strong></p></li></ul><p>And the best part? You don’t need a college degree to get started — just the right <strong>attitude</strong>, <strong>training</strong>, and <strong>communication skills</strong>.</p><h3>💬 Key Takeaway</h3><blockquote><p><strong>A BPO is a company that provides services — like customer support or technical help — on behalf of another business. It’s one of the best entry points into a global career, and it’s growing fast.</strong></p></blockquote><p>In the next lesson, we’ll explore <strong>why the BPO industry is growing so rapidly</strong>, and what that means for your future.</p><p></p>", "order_index": 2, "duration_minutes": 15, "created_at": "2025-05-18T03:45:04.260455+00:00", "updated_at": "2025-05-30T14:42:22.019241+00:00", "lesson_type": "video", "video_url": "https://snihmpmulfxhrjjcnzau.supabase.co/storage/v1/object/public/profile-videos//SSYouTube.online_Course%20Intro_%20Customer%20Service%20Training%20Videos%201_9_720p.mp4", "media_url": null}}, "activities": {"exists": true, "columns": ["id", "lesson_id", "title", "description", "type", "content", "content_url", "passing_criteria", "order_index", "duration_minutes", "created_at", "updated_at"], "sample": {"id": "0578b43a-8f82-441f-94a5-f22a56257717", "lesson_id": "cc6aef31-a974-42e3-9b91-6a3dd8647ee0", "title": "Auto-generated completion activity for lesson cc6aef31-a974-42e3-9b91-6a3dd8647ee0", "description": null, "type": "reading", "content": {}, "content_url": null, "passing_criteria": {}, "order_index": 1, "duration_minutes": null, "created_at": "2025-05-18T13:10:00.116+00:00", "updated_at": "2025-05-18T13:10:00.116+00:00"}}, "progress_records": {"exists": true, "columns": ["id", "prospect_id", "activity_id", "status", "score", "attempts", "completed_at", "time_spent_seconds", "created_at", "updated_at", "is_module_assessment"], "sample": {"id": "3c5e9456-c76e-43cc-a201-af082994608f", "prospect_id": "f08d34cc-6222-4a94-8476-ada6cad4abc3", "activity_id": "0578b43a-8f82-441f-94a5-f22a56257717", "status": "completed", "score": 100, "attempts": 0, "completed_at": "2025-05-18T13:10:00.237+00:00", "time_spent_seconds": 0, "created_at": "2025-05-18T13:10:00.237+00:00", "updated_at": "2025-05-18T13:10:00.237+00:00", "is_module_assessment": false}}, "certificates": {"exists": true, "columns": [], "sample": null}, "job_postings": {"exists": true, "columns": ["id", "bpo_id", "title", "description", "requirements", "responsibilities", "location", "job_type", "salary_range", "required_skills", "required_certifications", "application_deadline", "status", "created_at", "updated_at"], "sample": {"id": "9f021ecf-8d64-47e8-9e64-d2c5a563fe5d", "bpo_id": "4fa51cb5-e415-40e0-ab91-772b907f4a08", "title": "Customer Support Agent", "description": "We are seeking a dedicated and customer-focused Customer Service Agent to join our team. In this role, you will be the first point of contact for customers, handling inquiries, resolving complaints, and ensuring a high level of customer satisfaction through excellent communication and problem-solving skills.\n\nKey Responsibilities:\nRespond to customer inquiries via phone, email, chat, or social media in a timely and professional manner\n\nProvide accurate information about products, services, and company policies\n\nResolve customer complaints or issues efficiently and empathetically\n\nDocument customer interactions and update records in the system\n\nEscalate complex issues to the appropriate departments or supervisors\n\nMeet performance goals related to response time, customer satisfaction, and issue resolution\n\nMaintain a positive, empathetic, and professional attitude toward customers at all times\n\nStay informed about product updates and company policies to provide accurate assistance\n\n", "requirements": "Education & Experience:\n\nHigh school diploma or equivalent required\n\nAssociate’s or Bachelor’s degree is a plus\n\nPrevious experience in customer service, call center, or a similar role preferred\n\nSkills:\n\nExcellent verbal and written communication skills\n\nStrong problem-solving and conflict-resolution abilities\n\nProficiency in using customer service software, CRM systems, or call center tools\n\nAbility to multitask and manage time effectively in a fast-paced environment\n\nBasic computer literacy and typing skills\n\nPersonal Traits:\n\nPatience and empathy\n\nProfessionalism and a positive attitude\n\nStrong attention to detail\n\nAbility to work independently and as part of a team\n\nPreferred Qualifications:\nBilingual or multilingual abilities\n\nExperience in a specific industry (e.g., tech support, financial services, healthcare)\n\nFamiliarity with remote communication platforms (e.g., Zoom, Teams, Slack)", "responsibilities": [], "location": {"city": "Belize City", "remote": true, "country": "Belize"}, "job_type": "part_time", "salary_range": {"max": 21000, "min": 18000, "currency": "USD"}, "required_skills": [], "required_certifications": [], "application_deadline": "2025-06-26T00:00:00+00:00", "status": "published", "created_at": "2025-05-18T21:34:59.318+00:00", "updated_at": "2025-06-04T14:18:24.803883+00:00"}}, "applications": {"exists": true, "columns": [], "sample": null}, "interviews": {"exists": true, "columns": [], "sample": null}, "ai_conversations": {"exists": true, "columns": [], "sample": null}, "assessments": {"exists": true, "columns": ["id", "title", "description", "category", "duration", "instructions", "total_questions", "passing_score", "what_it_checks", "what_to_expect", "is_active", "completions", "avg_score", "created_at", "updated_at", "order_index"], "sample": {"id": "355d6375-a188-442d-a571-ac08cec408ca", "title": "🎭 Real-World Reaction Game", "description": "Test how you'd handle everyday work situations", "category": "Temperament", "duration": "45 minutes", "instructions": "You will be presented with workplace scenarios and must choose the most appropriate response. There may be multiple acceptable answers, but some are better than others.", "total_questions": 15, "passing_score": 80, "what_it_checks": "How you'd handle everyday work situations and make decisions.", "what_to_expect": "Role-play style questions where you choose the best response to workplace scenarios.", "is_active": true, "completions": 0, "avg_score": 0, "created_at": "2025-05-25T01:23:57.161984+00:00", "updated_at": "2025-05-27T19:27:25.566539+00:00", "order_index": 4}}, "assessment_questions": {"exists": true, "columns": ["id", "assessment_id", "question_text", "question_type", "options", "correct_answer", "points", "time_limit", "created_at", "updated_at"], "sample": {"id": "5b347d0a-ddb4-49c0-b4ee-cb58b7e6d499", "assessment_id": "e6cebbcc-b1cf-4ad3-af40-1532feb08f8a", "question_text": "You are a customer support representative working for a logistics company. A customer called in because their package did not arrive on time. After resolving the issue, you need to document the situation in the internal system. This record must clearly summarize what happened, what was done to resolve it, how the customer reacted, and any internal actions required to prevent future delays. Type the following report exactly as it should appear in a real agent note, paying close attention to punctuation, spelling, and clarity.", "question_type": "typing_test", "options": null, "correct_answer": "Due to unforeseen logistical issues, the customer’s package did not arrive on the originally promised date. After contacting the regional dispatch center, we confirmed that the delay was caused by a mechanical failure in the primary delivery vehicle. The item was rerouted to an alternative courier and is now expected to arrive within the next 24 hours.\n\nThe customer expressed frustration but remained cooperative during the call. A formal apology was issued, and a ten percent refund was applied to the customer's account as a gesture of goodwill. A follow-up email confirming the new estimated delivery window has been sent.\n\nGoing forward, similar issues should be escalated to the logistics team within two hours to ensure prompt response. All team members are advised to document customer interactions thoroughly and flag recurring complaints for review in the weekly quality assurance meeting.", "points": 30, "time_limit": 300, "created_at": "2025-05-25T03:29:14.259735+00:00", "updated_at": "2025-05-25T03:29:14.259735+00:00"}}, "module_assessments": {"exists": true, "columns": ["id", "module_id", "assessment_id", "is_required", "order_index", "passing_required", "created_at", "updated_at", "title", "description", "category", "duration", "instructions", "total_questions", "passing_score", "is_active", "completions", "avg_score", "is_module_quiz", "image_url"], "sample": {"id": "616864d3-1a0b-4744-af95-bed6cd723156", "module_id": "e279d8c3-5b38-49c9-9cd0-7084456207d2", "assessment_id": null, "is_required": true, "order_index": 7, "passing_required": true, "created_at": "2025-05-27T19:24:04.545+00:00", "updated_at": "2025-05-27T19:24:04.545+00:00", "title": "Module 7: Assessments", "description": "", "category": "Critical Thinking", "duration": "45 min", "instructions": "Please answer all questions to the best of your ability.", "total_questions": 1, "passing_score": 80, "is_active": true, "completions": 0, "avg_score": 0, "is_module_quiz": null, "image_url": null}}}}