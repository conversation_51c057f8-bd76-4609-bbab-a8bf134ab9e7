"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useErrorHandler } from '@/hooks/use-error-handler';
import { ErrorAlert, LoadingWithError } from '@/components/ui/error-display';
import { ErrorBoundary } from '@/components/error-boundary';

/**
 * Example component demonstrating standardized error handling patterns
 */
export function StandardizedErrorExample() {
  return (
    <ErrorBoundary context="StandardizedErrorExample">
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Standardized Error Handling Examples</CardTitle>
            <CardDescription>
              This component demonstrates the new standardized error handling patterns
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <DataFetchingExample />
            <FormSubmissionExample />
            <RetryLogicExample />
          </CardContent>
        </Card>
      </div>
    </ErrorBoundary>
  );
}

/**
 * Example of data fetching with error handling
 */
function DataFetchingExample() {
  const [modules, setModules] = useState<any[]>([]);
  const errorHandler = useErrorHandler({ 
    context: 'DataFetchingExample',
    showToast: true 
  });

  const fetchModules = async () => {
    const result = await errorHandler.executeAsync(async () => {
      const response = await fetch('/api/example-standardized');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error?.userMessage || 'Failed to fetch modules');
      }
      
      return data.data;
    }, 'Fetching training modules');

    if (result) {
      setModules(result.modules || []);
      errorHandler.showSuccessToast('Training modules loaded successfully');
    }
  };

  useEffect(() => {
    fetchModules();
  }, []);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Data Fetching Example</h3>
      
      <LoadingWithError
        isLoading={errorHandler.isLoading}
        error={errorHandler.error}
        onRetry={fetchModules}
        loadingText="Loading training modules..."
      >
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              Found {modules.length} training modules
            </p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchModules}
              disabled={errorHandler.isLoading}
            >
              Refresh
            </Button>
          </div>
          
          {modules.length > 0 ? (
            <div className="grid gap-2">
              {modules.slice(0, 3).map((module) => (
                <div key={module.id} className="p-2 border rounded text-sm">
                  <div className="font-medium">{module.title}</div>
                  <div className="text-muted-foreground">{module.description}</div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">No modules found</p>
          )}
        </div>
      </LoadingWithError>
    </div>
  );
}

/**
 * Example of form submission with error handling
 */
function FormSubmissionExample() {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    duration_minutes: ''
  });
  
  const errorHandler = useErrorHandler({ 
    context: 'FormSubmissionExample',
    showToast: true 
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const result = await errorHandler.executeAsync(async () => {
      // Validate form data
      if (!formData.title.trim()) {
        throw new Error('Title is required');
      }
      
      if (!formData.description.trim()) {
        throw new Error('Description is required');
      }
      
      if (!formData.duration_minutes || parseInt(formData.duration_minutes) <= 0) {
        throw new Error('Duration must be a positive number');
      }

      const response = await fetch('/api/example-standardized', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description,
          duration_minutes: parseInt(formData.duration_minutes)
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.userMessage || 'Failed to create module');
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error?.userMessage || 'Failed to create module');
      }

      return data.data;
    }, 'Creating training module');

    if (result) {
      // Reset form on success
      setFormData({ title: '', description: '', duration_minutes: '' });
      errorHandler.showSuccessToast('Training module created successfully!');
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Form Submission Example</h3>
      
      {errorHandler.error && (
        <ErrorAlert 
          error={errorHandler.error} 
          onDismiss={errorHandler.clearError}
        />
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="title">Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            placeholder="Enter module title"
            disabled={errorHandler.isLoading}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Enter module description"
            disabled={errorHandler.isLoading}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="duration">Duration (minutes)</Label>
          <Input
            id="duration"
            type="number"
            value={formData.duration_minutes}
            onChange={(e) => setFormData(prev => ({ ...prev, duration_minutes: e.target.value }))}
            placeholder="Enter duration in minutes"
            disabled={errorHandler.isLoading}
          />
        </div>
        
        <Button 
          type="submit" 
          disabled={errorHandler.isLoading}
          className="w-full"
        >
          {errorHandler.isLoading ? 'Creating...' : 'Create Module'}
        </Button>
      </form>
    </div>
  );
}

/**
 * Example of retry logic with error handling
 */
function RetryLogicExample() {
  const [attemptCount, setAttemptCount] = useState(0);
  const [lastResult, setLastResult] = useState<string>('');
  
  const errorHandler = useErrorHandler({ 
    context: 'RetryLogicExample',
    showToast: true 
  });

  const simulateUnreliableOperation = async () => {
    setAttemptCount(prev => prev + 1);
    
    const result = await errorHandler.executeWithRetry(async () => {
      // Simulate an operation that fails 70% of the time
      const shouldFail = Math.random() < 0.7;
      
      if (shouldFail) {
        throw new Error('Simulated network error - operation failed');
      }
      
      return `Success! Operation completed on attempt ${attemptCount + 1}`;
    }, 3, 1000, 'Simulating unreliable operation');

    if (result) {
      setLastResult(result);
      errorHandler.showSuccessToast('Operation completed successfully!');
    }
  };

  const resetExample = () => {
    setAttemptCount(0);
    setLastResult('');
    errorHandler.clearError();
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Retry Logic Example</h3>
      <p className="text-sm text-muted-foreground">
        This simulates an unreliable operation that fails 70% of the time. 
        The error handler will automatically retry up to 3 times.
      </p>
      
      {errorHandler.error && (
        <ErrorAlert 
          error={errorHandler.error} 
          onRetry={simulateUnreliableOperation}
          onDismiss={errorHandler.clearError}
        />
      )}
      
      {lastResult && (
        <div className="p-3 bg-green-50 border border-green-200 rounded text-sm text-green-800">
          {lastResult}
        </div>
      )}
      
      <div className="flex gap-2">
        <Button 
          onClick={simulateUnreliableOperation}
          disabled={errorHandler.isLoading}
        >
          {errorHandler.isLoading ? 'Attempting...' : 'Try Unreliable Operation'}
        </Button>
        
        <Button 
          variant="outline" 
          onClick={resetExample}
          disabled={errorHandler.isLoading}
        >
          Reset
        </Button>
      </div>
      
      <p className="text-xs text-muted-foreground">
        Attempts made: {attemptCount}
      </p>
    </div>
  );
}
