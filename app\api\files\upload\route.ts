import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { Database } from '@/types/database.types'

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Upload API called at:', new Date().toISOString())

    // Step 1: Initialize Supabase client
    console.log('📝 Step 1: Initializing Supabase client...')
    let supabase;
    try {
      supabase = createRouteHandlerClient<Database>({ cookies })
      console.log('✅ Step 1: Supabase client initialized successfully')
    } catch (clientError) {
      console.error('❌ Step 1: Failed to initialize Supabase client:', clientError)
      return NextResponse.json({
        error: 'Failed to initialize database client',
        step: 'client_initialization',
        details: clientError instanceof Error ? clientError.message : 'Unknown client error'
      }, { status: 500 })
    }

    // Step 2: Check authentication
    console.log('📝 Step 2: Checking authentication...')
    let user;
    try {
      const { data: { user: authUser }, error: userError } = await supabase.auth.getUser()
      user = authUser
      console.log('👤 Step 2: Auth result:', {
        hasUser: !!user,
        userId: user?.id,
        userEmail: user?.email,
        error: userError?.message
      })

      if (userError) {
        console.error('❌ Step 2: Auth error:', userError)
        return NextResponse.json({
          error: 'Authentication error',
          step: 'authentication',
          details: userError.message
        }, { status: 401 })
      }

      if (!user) {
        console.log('❌ Step 2: No user found')
        return NextResponse.json({
          error: 'No authenticated user',
          step: 'authentication'
        }, { status: 401 })
      }

      console.log('✅ Step 2: Authentication successful')
    } catch (authError) {
      console.error('❌ Step 2: Authentication check failed:', authError)
      return NextResponse.json({
        error: 'Authentication check failed',
        step: 'authentication',
        details: authError instanceof Error ? authError.message : 'Unknown auth error'
      }, { status: 500 })
    }

    // Step 3: Get user data
    console.log('📝 Step 3: Fetching user data...')
    let userData;
    try {
      const { data: userResult, error: userDataError } = await supabase
        .from('users')
        .select('id, role')
        .eq('id', user.id)
        .single()

      console.log('👥 Step 3: User data result:', {
        userData: userResult,
        error: userDataError?.message
      })

      if (userDataError) {
        console.error('❌ Step 3: User data error:', userDataError)
        return NextResponse.json({
          error: 'Failed to fetch user data',
          step: 'user_data',
          details: userDataError.message
        }, { status: 500 })
      }

      userData = userResult
      if (!userData || userData.role !== 'prospect') {
        console.log('❌ Step 3: User is not a prospect:', { userData })
        return NextResponse.json({
          error: 'User is not authorized as prospect',
          step: 'user_role_check',
          userRole: userData?.role
        }, { status: 401 })
      }

      console.log('✅ Step 3: User data validated successfully')
    } catch (userFetchError) {
      console.error('❌ Step 3: Failed to fetch user data:', userFetchError)
      return NextResponse.json({
        error: 'User data fetch failed',
        step: 'user_data',
        details: userFetchError instanceof Error ? userFetchError.message : 'Unknown user fetch error'
      }, { status: 500 })
    }

    // Step 4: Get prospect data
    console.log('📝 Step 4: Fetching prospect data...')
    let prospectData;
    try {
      const { data: prospectResult, error: prospectError } = await supabase
        .from('prospects')
        .select('id')
        .eq('user_id', userData.id)
        .single()

      console.log('🏢 Step 4: Prospect data result:', {
        prospectData: prospectResult,
        error: prospectError?.message
      })

      if (prospectError) {
        console.error('❌ Step 4: Prospect data error:', prospectError)
        return NextResponse.json({
          error: 'Failed to fetch prospect data',
          step: 'prospect_data',
          details: prospectError.message
        }, { status: 500 })
      }

      prospectData = prospectResult
      if (!prospectData) {
        console.log('❌ Step 4: No prospect found for user')
        return NextResponse.json({
          error: 'Prospect profile not found',
          step: 'prospect_data'
        }, { status: 404 })
      }

      console.log('✅ Step 4: Prospect data validated successfully')
    } catch (prospectFetchError) {
      console.error('❌ Step 4: Failed to fetch prospect data:', prospectFetchError)
      return NextResponse.json({
        error: 'Prospect data fetch failed',
        step: 'prospect_data',
        details: prospectFetchError instanceof Error ? prospectFetchError.message : 'Unknown prospect fetch error'
      }, { status: 500 })
    }

    // Step 5: Parse and validate form data
    console.log('📝 Step 5: Parsing form data...')
    let file, fileType, fileName, dateReceived, issuer;
    try {
      const formData = await request.formData()
      file = formData.get('file') as File
      fileType = formData.get('fileType') as string
      fileName = formData.get('fileName') as string
      dateReceived = formData.get('dateReceived') as string
      issuer = formData.get('issuer') as string

      console.log('📋 Step 5: Form data parsed:', {
        hasFile: !!file,
        fileName: file?.name,
        fileType,
        customFileName: fileName,
        dateReceived,
        issuer,
        fileSize: file?.size,
        fileMimeType: file?.type
      })

      if (!file) {
        console.log('❌ Step 5: No file provided in form data')
        return NextResponse.json({
          error: 'No file provided',
          step: 'form_validation'
        }, { status: 400 })
      }

      if (!fileType || !['certificate', 'document'].includes(fileType)) {
        console.log('❌ Step 5: Invalid file type:', fileType)
        return NextResponse.json({
          error: 'Invalid file type. Must be certificate or document',
          step: 'form_validation',
          providedType: fileType
        }, { status: 400 })
      }

      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024 // 5MB
      if (file.size > maxSize) {
        console.log('❌ Step 5: File too large:', file.size)
        return NextResponse.json({
          error: 'File too large. Maximum size is 5MB.',
          step: 'file_validation',
          fileSize: file.size,
          maxSize
        }, { status: 400 })
      }

      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg']
      if (!allowedTypes.includes(file.type)) {
        console.log('❌ Step 5: Invalid file format:', file.type)
        return NextResponse.json({
          error: 'Invalid file format. Only PDF, JPG, and PNG files are allowed.',
          step: 'file_validation',
          providedType: file.type,
          allowedTypes
        }, { status: 400 })
      }

      console.log('✅ Step 5: Form data validation successful')
    } catch (formError) {
      console.error('❌ Step 5: Form data parsing failed:', formError)
      return NextResponse.json({
        error: 'Failed to parse form data',
        step: 'form_parsing',
        details: formError instanceof Error ? formError.message : 'Unknown form parsing error'
      }, { status: 400 })
    }

    // Step 6: Generate unique filename and upload to storage
    console.log('📝 Step 6: Uploading to storage...')
    let uploadData, publicUrl;
    try {
      const timestamp = Date.now()
      const fileExtension = file.name.split('.').pop()
      const uniqueFileName = `${prospectData.id}/${timestamp}-${file.name}`

      console.log('📁 Step 6: Generated filename:', uniqueFileName)

      // Use service role client for storage operations to ensure permissions
      const supabaseServiceRole = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      )

      console.log('🔑 Step 6: Using service role for storage upload')

      const uploadResult = await supabaseServiceRole.storage
        .from('files')
        .upload(uniqueFileName, file, {
          cacheControl: '3600',
          upsert: false
        })

      uploadData = uploadResult.data
      const uploadError = uploadResult.error

      console.log('☁️ Step 6: Storage upload result:', {
        success: !!uploadData,
        path: uploadData?.path,
        error: uploadError?.message
      })

      if (uploadError) {
        console.error('❌ Step 6: Storage upload failed:', uploadError)
        return NextResponse.json({
          error: 'Failed to upload file to storage',
          step: 'storage_upload',
          details: uploadError.message,
          storageError: uploadError
        }, { status: 500 })
      }

      // Get public URL using service role client
      const urlResult = supabaseServiceRole.storage
        .from('files')
        .getPublicUrl(uniqueFileName)

      publicUrl = urlResult.data.publicUrl

      console.log('🔗 Step 6: Generated public URL:', publicUrl)
      console.log('✅ Step 6: Storage upload successful')
    } catch (storageError) {
      console.error('❌ Step 6: Storage operation failed:', storageError)
      return NextResponse.json({
        error: 'Storage operation failed',
        step: 'storage_upload',
        details: storageError instanceof Error ? storageError.message : 'Unknown storage error'
      }, { status: 500 })
    }

    // Step 7: Save file record to database
    console.log('📝 Step 7: Saving to database...')
    let fileRecord;
    try {
      const fileData = {
        prospect_id: prospectData.id,
        module_id: null, // Explicitly set to null for uploaded files
        file_type: fileType,
        file_category: 'uploaded',
        title: fileName || file.name,
        file_url: publicUrl,
        original_filename: file.name,
        file_size: file.size,
        mime_type: file.type,
        issued_at: dateReceived ? new Date(dateReceived).toISOString() : new Date().toISOString(),
        // Add issuer information for certificates
        ...(fileType === 'certificate' && issuer && { description: `Issued by: ${issuer}` })
      }

      console.log('💾 Step 7: Inserting file record:', JSON.stringify(fileData, null, 2))
      console.log('🔍 Step 7: Data validation check:', {
        prospect_id_valid: !!fileData.prospect_id,
        file_type_valid: ['certificate', 'document', 'training_certificate'].includes(fileData.file_type),
        file_category_valid: ['uploaded', 'system_generated'].includes(fileData.file_category),
        title_valid: !!fileData.title && fileData.title.length > 0,
        file_url_valid: !!fileData.file_url && fileData.file_url.length > 0,
        file_size_valid: typeof fileData.file_size === 'number',
        mime_type_valid: !!fileData.mime_type
      })

      // Try using the same client approach as our successful test
      console.log('🔄 Step 7: Using service role client for database insert...')
      const supabaseServiceRole = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      )

      const dbResult = await supabaseServiceRole
        .from('files')
        .insert(fileData)
        .select()
        .single()

      fileRecord = dbResult.data
      const dbError = dbResult.error

      console.log('🗄️ Step 7: Database insert result:', {
        success: !!fileRecord,
        recordId: fileRecord?.id,
        error: dbError?.message
      })

      if (dbError) {
        console.error('❌ Step 7: Database insert failed:', dbError)
        console.error('❌ Step 7: Full database error details:', {
          message: dbError.message,
          details: dbError.details,
          hint: dbError.hint,
          code: dbError.code
        })

        // Clean up uploaded file if database insert fails
        console.log('🧹 Step 7: Cleaning up uploaded file due to database error...')
        try {
          const uniqueFileName = `${prospectData.id}/${timestamp}-${file.name}`
          await supabaseServiceRole.storage.from('files').remove([uniqueFileName])
          console.log('✅ Step 7: File cleanup successful')
        } catch (cleanupError) {
          console.error('❌ Step 7: File cleanup failed:', cleanupError)
        }

        return NextResponse.json({
          error: 'Failed to save file record to database',
          step: 'database_insert',
          details: dbError.message,
          dbErrorCode: dbError.code,
          dbErrorDetails: dbError.details,
          dbErrorHint: dbError.hint,
          insertedData: fileData
        }, { status: 500 })
      }

      console.log('✅ Step 7: Database record saved successfully')
    } catch (dbOperationError) {
      console.error('❌ Step 7: Database operation failed:', dbOperationError)
      return NextResponse.json({
        error: 'Database operation failed',
        step: 'database_insert',
        details: dbOperationError instanceof Error ? dbOperationError.message : 'Unknown database error'
      }, { status: 500 })
    }

    // Step 8: Return success response
    console.log('📝 Step 8: Preparing success response...')
    const successResponse = {
      success: true,
      message: 'File uploaded successfully',
      file: {
        id: fileRecord.id,
        name: fileRecord.title,
        type: fileRecord.file_type,
        size: fileRecord.file_size,
        url: fileRecord.file_url
      },
      timestamp: new Date().toISOString()
    }

    console.log('🎉 Step 8: Upload completed successfully:', successResponse)
    return NextResponse.json(successResponse)

  } catch (error) {
    console.error('❌ CRITICAL ERROR: Unexpected error in upload API:', error)

    // Log detailed error information
    const errorDetails = {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : 'UnknownError',
      timestamp: new Date().toISOString()
    }

    console.error('❌ Error details:', errorDetails)

    return NextResponse.json({
      error: 'Internal server error',
      step: 'unexpected_error',
      details: errorDetails.message,
      timestamp: errorDetails.timestamp
    }, { status: 500 })
  }
}
