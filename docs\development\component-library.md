# 🧩 Component Library

Comprehensive documentation for the BPO Training Platform's reusable UI components, built with React, TypeScript, and Tailwind CSS.

## 🎨 Design System

### Color Palette
```css
/* Primary Colors */
--primary: 222.2 84% 4.9%        /* Dark blue-gray */
--primary-foreground: 210 40% 98% /* Light text */

/* Secondary Colors */
--secondary: 210 40% 96%          /* Light gray */
--secondary-foreground: 222.2 84% 4.9% /* Dark text */

/* Accent Colors */
--accent: 210 40% 96%             /* Subtle accent */
--accent-foreground: 222.2 84% 4.9%

/* Status Colors */
--success: 142 76% 36%            /* Green */
--warning: 38 92% 50%             /* Orange */
--error: 0 84% 60%                /* Red */
--info: 221 83% 53%               /* Blue */
```

### Typography Scale
```css
/* Font Sizes */
text-xs: 0.75rem     /* 12px */
text-sm: 0.875rem    /* 14px */
text-base: 1rem      /* 16px */
text-lg: 1.125rem    /* 18px */
text-xl: 1.25rem     /* 20px */
text-2xl: 1.5rem     /* 24px */
text-3xl: 1.875rem   /* 30px */
text-4xl: 2.25rem    /* 36px */
```

### Spacing Scale
```css
/* Spacing Units */
1: 0.25rem    /* 4px */
2: 0.5rem     /* 8px */
3: 0.75rem    /* 12px */
4: 1rem       /* 16px */
6: 1.5rem     /* 24px */
8: 2rem       /* 32px */
12: 3rem      /* 48px */
16: 4rem      /* 64px */
```

## 🔧 Base Components

### Button Component

**Location**: `components/ui/button.tsx`

```typescript
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}
```

**Usage Examples:**
```tsx
// Basic button
<Button>Click me</Button>

// Button variants
<Button variant="destructive">Delete</Button>
<Button variant="outline">Cancel</Button>
<Button variant="ghost">Ghost</Button>

// Button sizes
<Button size="sm">Small</Button>
<Button size="lg">Large</Button>

// Loading state
<Button loading>Saving...</Button>

// With icons
<Button leftIcon={<Plus className="h-4 w-4" />}>
  Add Item
</Button>
```

### Card Component

**Location**: `components/ui/card.tsx`

```typescript
interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined'
  padding?: 'none' | 'sm' | 'md' | 'lg'
}
```

**Usage Examples:**
```tsx
// Basic card
<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card description</CardDescription>
  </CardHeader>
  <CardContent>
    Card content goes here
  </CardContent>
  <CardFooter>
    <Button>Action</Button>
  </CardFooter>
</Card>

// Elevated card
<Card variant="elevated" padding="lg">
  Content with more padding and shadow
</Card>
```

### Input Components

**Location**: `components/ui/form-components.tsx`

```typescript
// Text Input
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

// Select
interface SelectProps {
  options: Array<{ value: string; label: string }>
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  error?: string
}

// Textarea
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  error?: string
  helperText?: string
}
```

**Usage Examples:**
```tsx
// Text input with validation
<Input
  label="Email Address"
  type="email"
  placeholder="Enter your email"
  error={errors.email}
  leftIcon={<Mail className="h-4 w-4" />}
/>

// Select dropdown
<Select
  options={[
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' }
  ]}
  value={selectedValue}
  onValueChange={setSelectedValue}
  placeholder="Choose an option"
/>

// Textarea
<Textarea
  label="Description"
  placeholder="Enter description"
  rows={4}
  helperText="Maximum 500 characters"
/>
```

## 📊 Data Display Components

### Progress Indicator

**Location**: `components/ui/progress-indicator.tsx`

```typescript
interface ProgressIndicatorProps {
  value: number // 0-100
  variant?: 'default' | 'success' | 'warning' | 'error'
  size?: 'sm' | 'md' | 'lg'
  showLabel?: boolean
  label?: string
  animated?: boolean
}
```

**Usage Examples:**
```tsx
// Basic progress bar
<ProgressIndicator value={75} />

// With custom label
<ProgressIndicator 
  value={85} 
  label="Training Progress"
  showLabel={true}
  variant="success"
/>

// Animated progress
<ProgressIndicator 
  value={progressValue} 
  animated={true}
  size="lg"
/>
```

### Status Badge

**Location**: `components/ui/status-badge.tsx`

```typescript
interface StatusBadgeProps {
  status: 'active' | 'inactive' | 'pending' | 'completed' | 'in_progress' | 'failed'
  variant?: 'default' | 'outline' | 'solid'
  size?: 'sm' | 'md' | 'lg'
  customLabel?: string
}
```

**Usage Examples:**
```tsx
// Status badges
<StatusBadge status="active" />
<StatusBadge status="pending" variant="outline" />
<StatusBadge status="completed" size="lg" />

// Custom label
<StatusBadge 
  status="in_progress" 
  customLabel="75% Complete"
/>
```

### Data Table

**Location**: `components/ui/data-table.tsx`

```typescript
interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  loading?: boolean
  pagination?: {
    page: number
    pageSize: number
    total: number
    onPageChange: (page: number) => void
  }
  sorting?: {
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  }
  filtering?: {
    searchValue?: string
    onSearchChange?: (value: string) => void
    filters?: FilterOption[]
  }
}
```

**Usage Examples:**
```tsx
// Define columns
const columns: ColumnDef<User>[] = [
  {
    accessorKey: 'full_name',
    header: 'Name',
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <Avatar src={row.original.avatar_url} />
        <span>{row.getValue('full_name')}</span>
      </div>
    )
  },
  {
    accessorKey: 'email',
    header: 'Email'
  },
  {
    accessorKey: 'role',
    header: 'Role',
    cell: ({ row }) => (
      <StatusBadge status={row.getValue('role')} />
    )
  }
]

// Use data table
<DataTable
  data={users}
  columns={columns}
  loading={isLoading}
  pagination={{
    page: currentPage,
    pageSize: 20,
    total: totalUsers,
    onPageChange: setCurrentPage
  }}
  filtering={{
    searchValue: searchTerm,
    onSearchChange: setSearchTerm
  }}
/>
```

## 🖼️ Media Components

### Optimized Image

**Location**: `components/ui/optimized-image.tsx`

```typescript
interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  variant?: 'avatar' | 'hero' | 'gallery' | 'responsive'
  priority?: boolean
  lazy?: boolean
  fallback?: string
  className?: string
}
```

**Usage Examples:**
```tsx
// Avatar image
<OptimizedImage
  src="/user-avatar.jpg"
  alt="User Avatar"
  variant="avatar"
  width={40}
  height={40}
/>

// Hero image with priority loading
<OptimizedImage
  src="/hero-banner.jpg"
  alt="Hero Banner"
  variant="hero"
  priority={true}
  className="w-full h-64 object-cover"
/>

// Lazy loaded gallery image
<OptimizedImage
  src="/gallery-image.jpg"
  alt="Gallery Image"
  variant="gallery"
  lazy={true}
  fallback="/placeholder.jpg"
/>
```

### File Upload

**Location**: `components/ui/file-upload.tsx`

```typescript
interface FileUploadProps {
  accept?: string
  multiple?: boolean
  maxSize?: number // in bytes
  onFileSelect?: (files: File[]) => void
  onUploadComplete?: (urls: string[]) => void
  variant?: 'dropzone' | 'button' | 'avatar'
  disabled?: boolean
  loading?: boolean
}
```

**Usage Examples:**
```tsx
// Dropzone for multiple files
<FileUpload
  accept="image/*,.pdf,.doc,.docx"
  multiple={true}
  maxSize={10 * 1024 * 1024} // 10MB
  onFileSelect={handleFileSelect}
  variant="dropzone"
/>

// Avatar upload
<FileUpload
  accept="image/*"
  maxSize={2 * 1024 * 1024} // 2MB
  onUploadComplete={handleAvatarUpload}
  variant="avatar"
/>
```

## 🎯 Specialized Components

### Module Card

**Location**: `components/ui/module-card.tsx`

```typescript
interface ModuleCardProps {
  module: {
    id: string
    title: string
    description: string
    cover_image_url?: string
    duration_minutes: number
    lessons_count: number
    completion_percentage?: number
  }
  variant?: 'default' | 'compact' | 'detailed'
  showProgress?: boolean
  onStart?: () => void
  onContinue?: () => void
  onView?: () => void
}
```

**Usage Examples:**
```tsx
// Training module card
<ModuleCard
  module={trainingModule}
  showProgress={true}
  onStart={() => startModule(module.id)}
  onContinue={() => continueModule(module.id)}
/>

// Compact module card
<ModuleCard
  module={trainingModule}
  variant="compact"
  onView={() => viewModule(module.id)}
/>
```

### Job Card

**Location**: `components/ui/job-card.tsx`

```typescript
interface JobCardProps {
  job: {
    id: string
    title: string
    description: string
    employment_type: string
    location_type: string
    salary_range?: {
      min: number
      max: number
      currency: string
    }
    bpo: {
      name: string
      logo_url?: string
    }
    created_at: string
  }
  variant?: 'default' | 'compact' | 'featured'
  showApplyButton?: boolean
  onApply?: () => void
  onView?: () => void
}
```

**Usage Examples:**
```tsx
// Job posting card
<JobCard
  job={jobPosting}
  showApplyButton={true}
  onApply={() => applyToJob(job.id)}
  onView={() => viewJob(job.id)}
/>

// Featured job card
<JobCard
  job={featuredJob}
  variant="featured"
  onView={() => viewJob(job.id)}
/>
```

## 🎨 Layout Components

### Page Layout

**Location**: `components/layout/page-layout.tsx`

```typescript
interface PageLayoutProps {
  title?: string
  description?: string
  breadcrumbs?: Array<{ label: string; href?: string }>
  actions?: React.ReactNode
  sidebar?: React.ReactNode
  children: React.ReactNode
}
```

**Usage Examples:**
```tsx
// Page with breadcrumbs and actions
<PageLayout
  title="Training Modules"
  description="Manage your training content"
  breadcrumbs={[
    { label: 'Dashboard', href: '/admin' },
    { label: 'Training', href: '/admin/training' },
    { label: 'Modules' }
  ]}
  actions={
    <Button onClick={createModule}>
      <Plus className="h-4 w-4 mr-2" />
      Create Module
    </Button>
  }
>
  <ModuleList />
</PageLayout>
```

### Dashboard Grid

**Location**: `components/layout/dashboard-grid.tsx`

```typescript
interface DashboardGridProps {
  children: React.ReactNode
  columns?: 1 | 2 | 3 | 4
  gap?: 'sm' | 'md' | 'lg'
  responsive?: boolean
}
```

**Usage Examples:**
```tsx
// Responsive dashboard grid
<DashboardGrid columns={3} gap="lg" responsive={true}>
  <StatCard title="Total Users" value={1250} />
  <StatCard title="Active Modules" value={25} />
  <StatCard title="Completion Rate" value="78%" />
</DashboardGrid>
```

## 🔄 Interactive Components

### Modal Dialog

**Location**: `components/ui/modal.tsx`

```typescript
interface ModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title?: string
  description?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  children: React.ReactNode
  footer?: React.ReactNode
}
```

**Usage Examples:**
```tsx
// Confirmation modal
<Modal
  open={showDeleteModal}
  onOpenChange={setShowDeleteModal}
  title="Delete Module"
  description="Are you sure you want to delete this training module?"
  size="md"
  footer={
    <div className="flex gap-2">
      <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
        Cancel
      </Button>
      <Button variant="destructive" onClick={handleDelete}>
        Delete
      </Button>
    </div>
  }
>
  <p>This action cannot be undone.</p>
</Modal>
```

### Toast Notifications

**Location**: `components/ui/toast.tsx`

```typescript
interface ToastProps {
  title?: string
  description?: string
  variant?: 'default' | 'success' | 'warning' | 'error'
  duration?: number
  action?: React.ReactNode
}

// Usage with hook
const { toast } = useToast()

toast({
  title: "Success!",
  description: "Module created successfully",
  variant: "success"
})
```

## 🎯 Component Guidelines

### Accessibility
- All components include proper ARIA labels
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- Color contrast compliance

### Performance
- Lazy loading for heavy components
- Memoization for expensive computations
- Optimized re-renders
- Bundle size optimization

### Testing
- Unit tests for all components
- Accessibility testing
- Visual regression testing
- Integration testing

### Documentation
- TypeScript interfaces for all props
- JSDoc comments for complex components
- Usage examples
- Storybook stories (when available)

---

**Next**: Check out the [Authentication Guide](../guides/authentication.md) for security implementation details.
