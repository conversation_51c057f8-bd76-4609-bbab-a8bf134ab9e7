import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Save } from 'lucide-react';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

// Form schema for vacancies (create/edit)
export const vacancyFormSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  requirements: z.string().min(10, "Requirements must be at least 10 characters"),
  // Use enum for job type to ensure only valid values are used
  job_type: z.enum(["full_time", "part_time", "contract", "temporary", "internship"], {
    required_error: "Job type is required",
    invalid_type_error: "Job type must be one of the valid options"
  }),
  location_city: z.string().optional(),
  location_country: z.string().optional(),
  is_remote: z.boolean().default(false),
  salary_min: z.string().optional(),
  salary_max: z.string().optional(),
  salary_currency: z.string().default("USD"),
  application_deadline: z.string().optional(),
  // Use enum for status to ensure valid content_status values
  status: z.enum(["draft", "published", "archived"], {
    required_error: "Status is required",
    invalid_type_error: "Status must be one of the valid options"
  }).default("draft")
});

export type VacancyFormData = z.infer<typeof vacancyFormSchema>;

interface VacancyFormProps {
  initialData?: VacancyFormData;
  bpoId: string;
  onSuccess: () => void;
  onCancel: () => void;
  isEditing?: boolean;
  vacancyId?: string;
}

export default function VacancyForm({
  initialData,
  bpoId,
  onSuccess,
  onCancel,
  isEditing = false,
  vacancyId
}: VacancyFormProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  // Default form values
  const defaultValues: VacancyFormData = {
    title: "",
    description: "",
    requirements: "",
    job_type: "full_time",
    location_city: "",
    location_country: "",
    is_remote: false,
    salary_min: "",
    salary_max: "",
    salary_currency: "USD",
    application_deadline: "",
    status: "draft"
  };

  // Create form with react-hook-form
  const form = useForm<VacancyFormData>({
    resolver: zodResolver(vacancyFormSchema),
    defaultValues: initialData || defaultValues
  });

  // If initialData changes, reset the form
  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }
  }, [initialData, form]);

  const onSubmitVacancy = async (data: VacancyFormData) => {
    try {
      setLoading(true);
      
      if (!bpoId) {
        throw new Error("BPO data not available");
      }
      
      // Define proper TypeScript interface for vacancy data
      interface VacancyData {
        title: string;
        description: string;
        requirements: string;
        job_type: string;
        bpo_id: string;
        status: string;
        created_at?: string;
        updated_at?: string;
        location: any; // Using any to accommodate both null and object
        salary_range: any; // Using any to accommodate both null and object
        application_deadline: string | null;
      }

      // Create vacancy data with all fields, handling nullable fields properly
      const vacancyData: VacancyData = {
        title: data.title,
        description: data.description || "",
        requirements: data.requirements || "",
        job_type: data.job_type,
        bpo_id: bpoId,
        status: data.status || "draft",
        location: null,
        salary_range: null,
        application_deadline: data.application_deadline ? new Date(data.application_deadline).toISOString() : null
      };
      
      // For new vacancies, add created_at
      if (!isEditing) {
        vacancyData.created_at = new Date().toISOString();
      } else {
        // For editing, add updated_at
        vacancyData.updated_at = new Date().toISOString();
      }
      
      // After the basic object is created, add location data if available
      if (data.location_city || data.location_country || data.is_remote) {
        vacancyData.location = {
          city: data.location_city || null,
          country: data.location_country || null,
          remote: data.is_remote
        };
      }
      
      // Add salary data if available
      if (data.salary_min || data.salary_max) {
        vacancyData.salary_range = {
          min: data.salary_min ? parseFloat(data.salary_min) : null,
          max: data.salary_max ? parseFloat(data.salary_max) : null,
          currency: data.salary_currency
        };
      }
      
      console.log(`${isEditing ? "Updating" : "Creating"} vacancy with data:`, vacancyData);
      
      try {
        let result;
        
        if (isEditing && vacancyId) {
          // Update existing vacancy
          result = await supabase
            .from('job_postings')
            .update(vacancyData)
            .eq('id', vacancyId)
            .eq('bpo_id', bpoId) // Security check
            .select()
            .single();
        } else {
          // Create new vacancy
          result = await supabase
            .from('job_postings')
            .insert(vacancyData)
            .select()
            .single();
        }
        
        const { data: createdVacancy, error } = result;
        
        if (error) {
          console.error(`${isEditing ? "Update" : "Creation"} error:`, error);
          throw error;
        }
        
        console.log(`Vacancy ${isEditing ? "updated" : "created"} successfully:`, createdVacancy);
        
        toast({
          title: isEditing ? "Vacancy updated" : "Vacancy created",
          description: isEditing 
            ? `The job vacancy has been updated successfully`
            : `The job vacancy "${data.title}" has been created successfully`,
        });
        
        // Signal success to parent component
        onSuccess();
        
      } catch (error) {
        console.error(`Error ${isEditing ? "updating" : "creating"} vacancy:`, error);
        throw error;
      }
      
    } catch (error) {
      console.error(`Error ${isEditing ? "updating" : "creating"} vacancy:`, error);
      toast({
        title: isEditing ? "Update failed" : "Creation failed",
        description: typeof error === 'object' && error !== null && 'message' in error 
          ? String(error.message) 
          : `There was an error ${isEditing ? "updating" : "creating"} the vacancy`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitVacancy)} className="space-y-6">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Job Title</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Customer Service Representative" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Job Description</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Describe the job role, responsibilities, and other details..." 
                  className="min-h-[120px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="requirements"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Job Requirements</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="List qualifications, skills, experience required..." 
                  className="min-h-[120px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="job_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Job Type</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select job type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="full_time">Full-time</SelectItem>
                    <SelectItem value="part_time">Part-time</SelectItem>
                    <SelectItem value="contract">Contract</SelectItem>
                    <SelectItem value="temporary">Temporary</SelectItem>
                    <SelectItem value="internship">Internship</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Valid job types: full_time, part_time, contract, temporary, internship
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="is_remote"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Remote Position</FormLabel>
                <Select 
                  onValueChange={(value) => field.onChange(value === "true")} 
                  defaultValue={field.value ? "true" : "false"}
                  value={field.value ? "true" : "false"}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Is this a remote position?" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="true">Yes</SelectItem>
                    <SelectItem value="false">No</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Specify if this is a remote job
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="location_city"
            render={({ field }) => (
              <FormItem>
                <FormLabel>City</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., New York" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="location_country"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Country</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., United States" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="salary_min"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Min Salary</FormLabel>
                <FormControl>
                  <Input type="number" placeholder="e.g., 30000" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="salary_max"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Max Salary</FormLabel>
                <FormControl>
                  <Input type="number" placeholder="e.g., 50000" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="salary_currency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Currency</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Currency" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                    <SelectItem value="CAD">CAD</SelectItem>
                    <SelectItem value="AUD">AUD</SelectItem>
                    <SelectItem value="INR">INR</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="application_deadline"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Application Deadline</FormLabel>
              <FormControl>
                <Input type="date" {...field} />
              </FormControl>
              <FormDescription>
                Optional. Leave empty for no deadline.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Job Status</FormLabel>
              <Select 
                onValueChange={field.onChange} 
                defaultValue={field.value}
                value={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>
                Valid statuses: draft, published, archived
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="flex justify-end space-x-4 pt-4">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={loading}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {isEditing ? "Updating..." : "Saving..."}
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {isEditing ? "Update Vacancy" : "Create Vacancy"}
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
} 