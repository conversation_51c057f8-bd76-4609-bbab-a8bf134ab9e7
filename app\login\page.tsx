'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { PasswordInput } from '@/components/ui/password-input';

export default function LoginPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFirstLogin, setIsFirstLogin] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  const [isInvited, setIsInvited] = useState(false);
  const [bpoName, setbpoName] = useState<string | null>(null);

  // Check if this is an invitation flow
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    setIsInvited(params.get('invited') === 'true');
    
    // If user was invited to a specific BPO, try to get the name from query params
    const bpoParam = params.get('bpo');
    if (bpoParam) {
      // The bpo name might be included in the future, for now we'll use a generic message
      setbpoName('a BPO company');
    }
  }, []);

  // Check if user is already logged in
  useEffect(() => {
    const checkSession = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session) {
        // Get user profile to check if password change is required
        const { data: userData, error } = await supabase
          .from('users')
          .select('is_password_change_required')
          .eq('id', data.session.user.id)
          .single();

        if (userData?.is_password_change_required) {
          setIsFirstLogin(true);
          setUserId(data.session.user.id);
        } else {
          // Already logged in and no password change required - redirect
          router.push('/');
        }
      }
    };

    checkSession();
  }, [router]);



  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    // Retry logic for rate limiting
    const attemptLogin = async (retryCount = 0): Promise<any> => {
      try {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) {
          // If rate limited and we haven't retried too many times, wait and retry
          if ((error.message.includes('rate limit') || error.message.includes('429') || error.status === 429) && retryCount < 3) {
            console.log(`Rate limited, retrying in ${(retryCount + 1) * 2} seconds... (attempt ${retryCount + 1}/3)`);
            await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 2000)); // Wait 2, 4, 6 seconds
            return attemptLogin(retryCount + 1);
          }

          // Handle other errors normally
          if (error.message.includes('Invalid login credentials')) {
            setError('Invalid email or password. Please check your credentials and try again.');
          } else if (error.message.includes('Email not confirmed')) {
            setError('Please check your email and click the confirmation link before signing in.');
          } else if (error.message.includes('rate limit') || error.message.includes('429') || error.status === 429) {
            setError('Rate limit reached. Tried multiple times but still blocked. Please wait a few minutes or try incognito mode.');
          } else {
            setError(error.message || 'An error occurred during login');
          }
          return { data: null, error };
        }

        return { data, error: null };
      } catch (err) {
        return { data: null, error: err };
      }
    };

    try {
      const { data, error } = await attemptLogin();

      if (error) {
        return; // Error already handled in attemptLogin
      }

      if (data.user) {
        // Check if user needs to change password on first login
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('is_password_change_required')
          .eq('id', data.user.id)
          .single();

        if (userError) {
          console.error('Error fetching user data:', userError);
          // Don't throw here, just proceed without password change check
        }

        // Update last_login timestamp
        const { error: updateError } = await supabase
          .from('users')
          .update({ last_login: new Date().toISOString() })
          .eq('id', data.user.id);

        if (updateError) {
          console.error('Error updating last_login:', updateError);
        }

        if (userData?.is_password_change_required) {
          // Show password change form
          setIsFirstLogin(true);
          setUserId(data.user.id);
        } else {
          // Redirect to dashboard
          router.push('/');
          router.refresh();
        }
      }
    } catch (error: any) {
      console.error('Login error:', error);
      if (error.message.includes('rate limit') || error.message.includes('429') || error.status === 429) {
        setError('Supabase rate limit reached. This is a server-side limit. Please wait a moment and try again, or try using a different browser/incognito mode.');
      } else {
        setError(error.message || 'An error occurred during login');
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    if (newPassword.length < 8) {
      setError('Password must be at least 8 characters');
      setLoading(false);
      return;
    }

    try {
      // Change password in auth
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (updateError) {
        throw updateError;
      }

      // Update user profile to mark password as changed
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({ 
          is_password_change_required: false,
          last_login: new Date().toISOString()
        })
        .eq('id', userId);

      if (userUpdateError) {
        throw userUpdateError;
      }

      // Redirect to dashboard
      router.push('/');
      router.refresh();
    } catch (error: any) {
      setError(error.message || 'An error occurred while changing password');
    } finally {
      setLoading(false);
    }
  };

  if (isFirstLogin) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8">
          <div>
            <h2 className="mt-6 text-center text-3xl font-bold tracking-tight">
              Change Your Password
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Since this is your first login, please set a new password
            </p>
          </div>
          
          <form className="mt-8 space-y-6" onSubmit={handlePasswordChange}>
            <div className="space-y-4">
              <div>
                <label htmlFor="new-password" className="block text-sm font-medium text-gray-700">
                  New Password
                </label>
                <PasswordInput
                  id="new-password"
                  name="newPassword"
                  autoComplete="new-password"
                  required
                  className="relative block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 mt-1"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                />
              </div>
              
              <div>
                <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700">
                  Confirm Password
                </label>
                <PasswordInput
                  id="confirm-password"
                  name="confirmPassword"
                  autoComplete="new-password"
                  required
                  className="relative block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 mt-1"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
              </div>
            </div>

            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative flex w-full justify-center rounded-md bg-indigo-600 py-2 px-3 text-sm font-semibold text-white hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-70"
              >
                {loading ? 'Updating...' : 'Change Password'}
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-bold tracking-tight">
            BPO Talent Training Platform
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {isInvited 
              ? `You've been invited to join ${bpoName || 'a BPO team'}`
              : 'Sign in to your account'
            }
          </p>
          
          {isInvited && (
            <div className="mt-4 p-4 bg-blue-50 rounded-md text-sm text-blue-700">
              Please sign in if you already have an account, or sign up if you're new. 
              Your account will be automatically linked to your team after login.
            </div>
          )}
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleLogin}>
          <div className="-space-y-px rounded-md shadow-sm">
            <div>
              <label htmlFor="email-address" className="sr-only">
                Email address
              </label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="relative block w-full rounded-t-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                placeholder="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <PasswordInput
                id="password"
                name="password"
                autoComplete="current-password"
                required
                className="relative block w-full rounded-b-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                wrapperClassName="relative"
              />
            </div>
          </div>

          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-700">
                {error}
              </div>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative flex w-full justify-center rounded-md bg-indigo-600 py-2 px-3 text-sm font-semibold text-white hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-70"
            >
              {loading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>
        </form>
        
        {isInvited && (
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-2 text-gray-500">Or</span>
              </div>
            </div>
            
            <div className="mt-6">
              <a
                href={`/signup?invited=true&bpo=${new URLSearchParams(window.location.search).get('bpo') || ''}`}
                className="flex w-full justify-center rounded-md border border-indigo-600 bg-white py-2 px-3 text-sm font-semibold text-indigo-600 hover:bg-indigo-50"
              >
                Create a new account
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 