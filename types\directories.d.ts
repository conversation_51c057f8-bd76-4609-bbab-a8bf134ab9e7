declare module '@/components/vacancy-form' {
  import { ReactNode } from 'react';

  export interface VacancyFormData {
    title: string;
    description: string;
    requirements: string;
    job_type: "full_time" | "part_time" | "contract" | "temporary" | "internship";
    location_city?: string;
    location_country?: string;
    is_remote: boolean;
    salary_min?: string;
    salary_max?: string;
    salary_currency: string;
    application_deadline?: string;
    status: "draft" | "published" | "archived";
  }

  export interface VacancyFormProps {
    initialData?: VacancyFormData;
    bpoId: string;
    onSuccess: () => void;
    onCancel: () => void;
    isEditing?: boolean;
    vacancyId?: string;
  }

  export default function VacancyForm(props: VacancyFormProps): ReactNode;
} 