// Email notification system for interview invitations
// This is a placeholder implementation - in production, you would integrate with
// a service like SendGrid, Mailgun, or AWS SES

interface EmailNotificationData {
  to: string
  subject: string
  html: string
  text?: string
}

interface InterviewInvitationData {
  prospectName: string
  prospectEmail: string
  bpoName: string
  jobTitle: string
  interviewType: string
  proposedTimes?: Array<{
    startTime: string
    endTime: string
    date: string
  }>
  invitationMessage?: string
  interviewId: string
}

interface InterviewAcceptanceData {
  bpoEmail: string
  prospectName: string
  jobTitle: string
  selectedTime: string
  prospectNotes?: string
  interviewId: string
}

export async function sendInterviewInvitationEmail(data: InterviewInvitationData): Promise<boolean> {
  try {
    console.log('📧 Sending interview invitation email to:', data.prospectEmail)
    
    const emailData: EmailNotificationData = {
      to: data.prospectEmail,
      subject: `Interview Invitation - ${data.jobTitle} at ${data.bpoName}`,
      html: generateInvitationEmailHTML(data),
      text: generateInvitationEmailText(data)
    }
    
    // In production, replace this with actual email service
    await mockEmailSend(emailData)
    
    console.log('✅ Interview invitation email sent successfully')
    return true
    
  } catch (error) {
    console.error('❌ Failed to send interview invitation email:', error)
    return false
  }
}

export async function sendInterviewAcceptanceEmail(data: InterviewAcceptanceData): Promise<boolean> {
  try {
    console.log('📧 Sending interview acceptance notification to:', data.bpoEmail)
    
    const emailData: EmailNotificationData = {
      to: data.bpoEmail,
      subject: `Interview Accepted - ${data.jobTitle}`,
      html: generateAcceptanceEmailHTML(data),
      text: generateAcceptanceEmailText(data)
    }
    
    // In production, replace this with actual email service
    await mockEmailSend(emailData)
    
    console.log('✅ Interview acceptance email sent successfully')
    return true
    
  } catch (error) {
    console.error('❌ Failed to send interview acceptance email:', error)
    return false
  }
}

function generateInvitationEmailHTML(data: InterviewInvitationData): string {
  const proposedTimesHTML = data.proposedTimes?.map(time => {
    const start = new Date(time.startTime)
    const end = new Date(time.endTime)
    return `<li>${start.toLocaleDateString()} at ${start.toLocaleTimeString()} - ${end.toLocaleTimeString()}</li>`
  }).join('') || '<li>Times to be arranged</li>'

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #2563eb;">Interview Invitation</h2>
      
      <p>Dear ${data.prospectName},</p>
      
      <p>You have been invited for an interview for the <strong>${data.jobTitle}</strong> position at <strong>${data.bpoName}</strong>.</p>
      
      ${data.invitationMessage ? `<div style="background: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p><strong>Message from the interviewer:</strong></p>
        <p>${data.invitationMessage}</p>
      </div>` : ''}
      
      <h3>Interview Details:</h3>
      <ul>
        <li><strong>Type:</strong> ${data.interviewType}</li>
        <li><strong>Proposed Times:</strong></li>
        <ul>${proposedTimesHTML}</ul>
      </ul>
      
      <p>Please log in to your account to accept this invitation and select your preferred time.</p>
      
      <div style="margin: 30px 0;">
        <a href="${process.env.NEXT_PUBLIC_BASE_URL}/prospect/interviews" 
           style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
          View Interview Invitation
        </a>
      </div>
      
      <p>Best regards,<br>
      ${data.bpoName} Team</p>
    </div>
  `
}

function generateInvitationEmailText(data: InterviewInvitationData): string {
  const proposedTimesText = data.proposedTimes?.map(time => {
    const start = new Date(time.startTime)
    const end = new Date(time.endTime)
    return `- ${start.toLocaleDateString()} at ${start.toLocaleTimeString()} - ${end.toLocaleTimeString()}`
  }).join('\n') || '- Times to be arranged'

  return `
Interview Invitation

Dear ${data.prospectName},

You have been invited for an interview for the ${data.jobTitle} position at ${data.bpoName}.

${data.invitationMessage ? `Message from the interviewer:\n${data.invitationMessage}\n\n` : ''}

Interview Details:
- Type: ${data.interviewType}
- Proposed Times:
${proposedTimesText}

Please log in to your account to accept this invitation and select your preferred time.

Visit: ${process.env.NEXT_PUBLIC_BASE_URL}/prospect/interviews

Best regards,
${data.bpoName} Team
  `.trim()
}

function generateAcceptanceEmailHTML(data: InterviewAcceptanceData): string {
  const selectedTime = new Date(data.selectedTime)
  
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #16a34a;">Interview Accepted</h2>
      
      <p>Great news! <strong>${data.prospectName}</strong> has accepted the interview invitation for the <strong>${data.jobTitle}</strong> position.</p>
      
      <h3>Interview Details:</h3>
      <ul>
        <li><strong>Candidate:</strong> ${data.prospectName}</li>
        <li><strong>Position:</strong> ${data.jobTitle}</li>
        <li><strong>Selected Time:</strong> ${selectedTime.toLocaleDateString()} at ${selectedTime.toLocaleTimeString()}</li>
      </ul>
      
      ${data.prospectNotes ? `<div style="background: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p><strong>Notes from candidate:</strong></p>
        <p>${data.prospectNotes}</p>
      </div>` : ''}
      
      <p>Please prepare for the interview and send the meeting details to the candidate.</p>
      
      <div style="margin: 30px 0;">
        <a href="${process.env.NEXT_PUBLIC_BASE_URL}/bpo/interviews" 
           style="background: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
          View Interview Details
        </a>
      </div>
    </div>
  `
}

function generateAcceptanceEmailText(data: InterviewAcceptanceData): string {
  const selectedTime = new Date(data.selectedTime)
  
  return `
Interview Accepted

Great news! ${data.prospectName} has accepted the interview invitation for the ${data.jobTitle} position.

Interview Details:
- Candidate: ${data.prospectName}
- Position: ${data.jobTitle}
- Selected Time: ${selectedTime.toLocaleDateString()} at ${selectedTime.toLocaleTimeString()}

${data.prospectNotes ? `Notes from candidate:\n${data.prospectNotes}\n\n` : ''}

Please prepare for the interview and send the meeting details to the candidate.

Visit: ${process.env.NEXT_PUBLIC_BASE_URL}/bpo/interviews
  `.trim()
}

// Mock email sending function - replace with actual email service in production
async function mockEmailSend(emailData: EmailNotificationData): Promise<void> {
  // Simulate email sending delay
  await new Promise(resolve => setTimeout(resolve, 100))
  
  console.log('📧 Mock Email Sent:')
  console.log('To:', emailData.to)
  console.log('Subject:', emailData.subject)
  console.log('Content:', emailData.text?.substring(0, 100) + '...')
  
  // In production, implement actual email sending here:
  // - SendGrid: await sgMail.send(emailData)
  // - Mailgun: await mailgun.messages().send(emailData)
  // - AWS SES: await ses.sendEmail(emailData).promise()
}
