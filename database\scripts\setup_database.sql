-- COMPLETE DATABASE SETUP SCRIPT
-- Run this script to set up the entire database from scratch

-- =============================================================================
-- STEP 1: INITIAL SCHEMA
-- =============================================================================

\i database/migrations/01_initial_schema.sql

-- =============================================================================
-- STEP 2: SECURITY FUNCTIONS
-- =============================================================================

\i database/functions/security_functions.sql

-- =============================================================================
-- STEP 3: RLS POLICIES
-- =============================================================================

\i database/policies/rls_policies.sql

-- =============================================================================
-- STEP 4: SAMPLE DATA (Optional)
-- =============================================================================

-- Insert sample admin user
INSERT INTO public.users (id, email, encrypted_password, full_name, role, status)
VALUES (
  '00000000-0000-0000-0000-000000000001',
  '<EMAIL>',
  '$2a$10$example_encrypted_password_hash',
  'Platform Administrator',
  'admin',
  'active'
) ON CONFLICT (email) DO NOTHING;

-- Insert sample training module
INSERT INTO public.training_modules (id, title, description, duration_minutes, status, created_by)
VALUES (
  '11111111-1111-1111-1111-111111111111',
  'Customer Service Fundamentals',
  'Learn the basics of excellent customer service in a BPO environment',
  120,
  'published',
  '00000000-0000-0000-0000-000000000001'
) ON CONFLICT (id) DO NOTHING;

-- =============================================================================
-- VERIFICATION
-- =============================================================================

-- Verify tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- Verify functions exist
SELECT routine_name, routine_type
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_name LIKE '%admin%' OR routine_name LIKE '%membership%'
ORDER BY routine_name;

-- Verify policies exist
SELECT tablename, policyname
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;
