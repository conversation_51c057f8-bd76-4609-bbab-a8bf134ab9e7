import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    // Get table information for job_postings
    const { data: tableInfo, error: tableError } = await supabase
      .from('job_postings')
      .select('*')
      .limit(1);

    if (tableError) {
      return NextResponse.json({ error: tableError }, { status: 500 });
    }

    // Get column information
    const { data: columns, error: columnsError } = await supabase.rpc('get_table_columns', {
      table_name: 'job_postings'
    });

    // If the RPC doesn't exist, try a different approach
    if (columnsError) {
      // Fallback: Try to infer schema from the first record
      const schema = tableInfo && tableInfo.length > 0 
        ? Object.keys(tableInfo[0]).map(key => {
            return {
              column_name: key,
              data_type: typeof tableInfo[0][key],
              is_nullable: tableInfo[0][key] === null ? 'YES' : 'UNKNOWN'
            };
          })
        : [];

      return NextResponse.json({
        table: 'job_postings',
        sample: tableInfo,
        inferred_schema: schema,
        error: columnsError
      });
    }

    return NextResponse.json({
      table: 'job_postings',
      sample: tableInfo,
      schema: columns
    });
  } catch (error) {
    return NextResponse.json({ error: String(error) }, { status: 500 });
  }
} 