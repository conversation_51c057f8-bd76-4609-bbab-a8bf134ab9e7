import { ProfilePage } from "@/components/profile-page"
import { cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from '@supabase/supabase-js'
import { redirect } from "next/navigation"
import { Database } from "@/types/supabase"

// Create admin client for operations that need to bypass RLS
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ""
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ""
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey)

interface Module {
  id: string
  title: string
  progress: number
  status: string
}

// Must match the interface in profile-page.tsx
interface Certificate {
  id: string
  module_title: string
  issue_date: string
  certificate_url: string
}

interface Skill {
  name: string
  level: string
  proficiency: number
}

interface Badge {
  id: string
  title: string
  description: string
  badge_type: string
}

// Define custom interfaces for tables not yet in database schema
interface BadgeData {
  id: string;
  title: string;
  description: string;
  badge_type: string;
}

interface CallPracticeSession {
  id: string;
  duration_minutes: number;
  score: number;
}

// Extend ProspectProfile interface to match component requirements
interface ProspectProfile {
  id: string;
  contact_info: any;
  education: any[];
  experience: any[];
  skills: any[];
  intro_video_url: string | null;
  resume_url: string | null;
  profile_visibility: boolean;
  training_status: string;
}

export default async function Profile() {
  const supabase = createServerComponentClient<Database>({ cookies })
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect("/login")
  }
  
  // Get the user data
  const { data: userData, error: userError } = await supabase
    .from("users")
    .select("id, role, full_name, email, avatar_url")
    .eq("id", session.user.id)
    .single()
  
  if (userError || !userData || userData.role !== "prospect") {
    redirect("/unauthorized")
  }
  
  // Also fetch the user data from auth to get avatar if available
  const { data: { user: authUser } } = await supabase.auth.getUser()
  
  console.log("User data:", {
    id: userData.id,
    role: userData.role,
    avatar_url: userData.avatar_url,
    auth_avatar: authUser?.user_metadata?.avatar_url
  })
  
  // Get the prospect's profile
  const { data: prospectData, error: prospectError } = await adminClient
    .from("prospects")
    .select(`
      id, 
      contact_info,
      education,
      experience,
      skills,
      intro_video_url,
      resume_url,
      profile_visibility,
      training_status
    `)
    .eq("user_id", userData.id)
    .single()
  
  if (prospectError) {
    console.error("Error fetching prospect data:", prospectError.message)
  }
  
  // Get training progress statistics
  const prospectId = prospectData?.id
  
  // Prepare profile data
  const profileData = {
    user: {
      id: userData.id,
      full_name: userData.full_name || "",
      email: userData.email || "",
      role: userData.role,
      // Use user table avatar or fall back to auth metadata avatar
      avatar_url: userData.avatar_url || authUser?.user_metadata?.avatar_url || null
    },
    prospect: prospectData ? {
      id: prospectData.id,
      contact_info: prospectData.contact_info || {},
      education: Array.isArray(prospectData.education) ? prospectData.education : [],
      experience: Array.isArray(prospectData.experience) ? prospectData.experience : [],
      skills: Array.isArray(prospectData.skills) ? prospectData.skills : [],
      intro_video_url: prospectData.intro_video_url,
      resume_url: prospectData.resume_url,
      profile_visibility: prospectData.profile_visibility || false,
      training_status: prospectData.training_status || "not_started"
    } : null,
    training: {
      progress: 0,
      aiCallHours: 0,
      avgScore: 0,
      totalCalls: 0,
      modules: [] as Module[],
      certificates: [] as Certificate[],
      badges: [] as Badge[],
      skills: [] as Skill[]
    }
  };
  
  if (prospectId) {
    try {
      // Get modules and progress
      const { data: modules } = await supabase
        .from("training_modules")
        .select(`
          id,
          title,
          description,
          duration_minutes,
          status
        `)
        .eq("status", "published")
      
      const moduleIds = modules?.map(m => m.id) || []
      
      // Get progress for this prospect
      const { data: progress } = await adminClient
        .from("progress_records")
        .select(`
          id,
          status,
          score,
          activity_id,
          activities(
            id,
            lesson_id,
            lessons(
              id,
              module_id
            )
          )
        `)
        .eq("prospect_id", prospectId)
      
      // Calculate module completion stats
      if (modules && modules.length > 0 && progress && progress.length > 0) {
        // Track modules with activity progress
        const moduleProgress = new Map()
        const moduleCompletionCounts = new Map()
        const moduleActivityCounts = new Map()
        
        // Count activities per module
        for (const module of modules) {
          // Get all activities for this module
          const { data: activities } = await adminClient
            .from("activities")
            .select(`
              id,
              lessons(module_id)
            `)
            .eq("lessons.module_id", module.id)
          
          if (activities) {
            moduleActivityCounts.set(module.id, activities.length)
          }
        }
        
        // Process progress records
        for (const record of progress) {
          // Access nested properties safely using type assertions
          const activity = record.activities as any
          
          if (record.status === 'completed' && 
              activity && 
              activity.lessons && 
              activity.lessons.module_id) {
            
            const moduleId = activity.lessons.module_id
            
            // Increment completion count for this module
            moduleCompletionCounts.set(
              moduleId, 
              (moduleCompletionCounts.get(moduleId) || 0) + 1
            )
            
            // Mark module as having progress
            moduleProgress.set(moduleId, true)
          }
        }
        
        // Determine module completion status
        const moduleData: Module[] = modules.map(module => {
          const totalActivities = moduleActivityCounts.get(module.id) || 0
          const completedActivities = moduleCompletionCounts.get(module.id) || 0
          
          let progress = 0
          let status = 'not_started'
          
          if (totalActivities > 0) {
            progress = Math.round((completedActivities / totalActivities) * 100)
            
            if (progress === 100) {
              status = 'completed'
            } else if (progress > 0) {
              status = 'in_progress'
            }
          }
          
          return {
            id: module.id,
            title: module.title,
            progress,
            status
          }
        })
        
        profileData.training.modules = moduleData
        
        // Calculate overall progress
        const totalActivities = Array.from(moduleActivityCounts.values()).reduce((sum, count) => sum + count, 0)
        const completedActivities = progress.filter(p => p.status === 'completed').length
        
        if (totalActivities > 0) {
          profileData.training.progress = Math.round((completedActivities / totalActivities) * 100)
        }
      }
      
      // Get files (certificates and documents)
      const { data: files } = await adminClient
        .from("files")
        .select(`
          id,
          title,
          file_type,
          file_category,
          issued_at,
          file_url,
          module_id,
          original_filename,
          verification_code
        `)
        .eq("prospect_id", prospectId)
        .order("issued_at", { ascending: false })

      if (files && files.length > 0) {
        // Get module titles for files that have module_id
        const moduleIds = files.filter(file => file.module_id).map(file => file.module_id)

        let moduleTitleMap = new Map<string, string>()
        if (moduleIds.length > 0) {
          const { data: moduleTitles } = await adminClient
            .from("training_modules")
            .select("id, title")
            .in("id", moduleIds)

          if (moduleTitles) {
            moduleTitles.forEach(module => {
              moduleTitleMap.set(module.id, module.title)
            })
          }
        }

        // Filter and map certificates
        const certificateFiles = files.filter(file =>
          file.file_type === 'certificate' || file.file_type === 'training_certificate'
        )

        const certificateData: Certificate[] = certificateFiles.map(file => ({
          id: file.id,
          module_title: file.module_id ?
            (moduleTitleMap.get(file.module_id) || "Unknown Module") :
            file.title,
          issue_date: new Date(file.issued_at).toLocaleDateString(),
          certificate_url: file.file_url || ""
        }))

        profileData.training.certificates = certificateData

        // Store all files for potential Files section
        profileData.training.allFiles = files.map(file => ({
          id: file.id,
          title: file.title,
          file_type: file.file_type,
          file_category: file.file_category,
          issued_at: file.issued_at,
          file_url: file.file_url,
          original_filename: file.original_filename,
          module_title: file.module_id ? moduleTitleMap.get(file.module_id) : undefined
        }))
      }

      // Get assessment scores and performance data
      const { data: assessmentCompletions } = await adminClient
        .from("assessment_completions")
        .select(`
          id,
          score,
          status,
          completed_at,
          assessments(
            id,
            title,
            module_id,
            training_modules(title)
          )
        `)
        .eq("user_id", userData.id)
        .eq("status", "completed")
        .order("completed_at", { ascending: false })

      if (assessmentCompletions && assessmentCompletions.length > 0) {
        // Group assessments by module to get best scores per module
        const moduleAssessments = new Map<string, Array<any>>()

        assessmentCompletions.forEach(ac => {
          const moduleId = ac.assessments?.module_id
          if (moduleId) {
            if (!moduleAssessments.has(moduleId)) {
              moduleAssessments.set(moduleId, [])
            }
            moduleAssessments.get(moduleId)!.push(ac)
          }
        })

        // Calculate best scores per module and overall statistics
        const moduleScores: Array<{
          moduleId: string
          moduleTitle: string
          bestScore: number
          totalAttempts: number
          assessmentTitle: string
        }> = []

        let totalUniqueAssessments = 0
        let totalScoreSum = 0

        moduleAssessments.forEach((assessments, moduleId) => {
          // Get the best score for this module
          const bestAssessment = assessments.reduce((best, current) =>
            (current.score || 0) > (best.score || 0) ? current : best
          )

          moduleScores.push({
            moduleId,
            moduleTitle: bestAssessment.assessments?.training_modules?.title || "Unknown Module",
            bestScore: bestAssessment.score || 0,
            totalAttempts: assessments.length,
            assessmentTitle: bestAssessment.assessments?.title || "Unknown Assessment"
          })

          totalUniqueAssessments++
          totalScoreSum += (bestAssessment.score || 0)
        })

        const avgScore = totalUniqueAssessments > 0 ? Math.round(totalScoreSum / totalUniqueAssessments) : 0

        // Store assessment performance data
        profileData.training.assessmentStats = {
          totalCompleted: assessmentCompletions.length, // Total attempts across all assessments
          totalUniqueAssessments: totalUniqueAssessments, // Unique assessments completed
          averageScore: avgScore, // Average of best scores per module
          moduleScores: moduleScores, // Best scores per module
          recentAssessments: assessmentCompletions.slice(0, 5).map(ac => ({
            id: ac.id,
            title: ac.assessments?.title || "Unknown Assessment",
            module_title: ac.assessments?.training_modules?.title || "Unknown Module",
            score: ac.score || 0,
            completed_at: ac.completed_at,
            status: ac.status
          }))
        }
      }

      // Badges functionality hasn't been implemented in the database yet
      // Set empty badges for now
      profileData.training.badges = [];
      
      // Call practice functionality hasn't been implemented in the database yet
      // Using default values for now
      profileData.training.aiCallHours = 0;
      profileData.training.avgScore = 0;
      profileData.training.totalCalls = 0;
      
      // We no longer need to copy skills to training.skills since we'll use prospect.skills directly
      profileData.training.skills = [];
      
    } catch (error: any) {
      console.error("Error fetching profile data:", error.message || error)
    }
  }
  
  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <ProfilePage data={profileData} />
    </div>
  )
} 