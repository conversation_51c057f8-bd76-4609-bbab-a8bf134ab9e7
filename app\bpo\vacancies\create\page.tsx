'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function CreateVacancyPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the main vacancies page with a query parameter to open the create dialog
    router.push('/bpo/vacancies?create=true');
  }, [router]);

  return (
    <div className="flex h-[60vh] items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-500 dark:text-gray-400">Redirecting to vacancy creation form...</p>
      </div>
    </div>
  );
} 