const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyFilesMigration() {
  console.log('🚀 Starting Files Migration...\n');

  try {
    // Step 1: Create the files table
    console.log('📝 Creating files table...');

    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS public.files (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        prospect_id UUID NOT NULL,
        module_id UUID,
        assessment_id UUID,
        file_type VARCHAR(50) NOT NULL DEFAULT 'document', -- 'certificate', 'document', 'training_certificate'
        file_category VARCHAR(50) NOT NULL DEFAULT 'uploaded', -- 'uploaded', 'system_generated'
        title VARCHAR(255) NOT NULL,
        description TEXT,
        file_url TEXT NOT NULL, -- URL to the file
        original_filename VARCHAR(255), -- Original filename when uploaded
        file_size INTEGER DEFAULT 0, -- File size in bytes
        mime_type VARCHAR(100), -- MIME type of the file
        issued_at TIMESTAMPTZ DEFAULT NOW(),
        expires_at TIMESTAMPTZ,
        verification_code VARCHAR(100) UNIQUE,
        is_verified BOOLEAN DEFAULT TRUE,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );
    `;

    const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    if (createError) {
      console.error('Error creating files table:', createError);
      return;
    }
    console.log('✅ Files table created successfully');

    // Step 2: Since certificates table is empty, skip data copying
    console.log('📋 Certificates table is empty, skipping data copy...');
    console.log('✅ No data to copy');

    // Step 3: Add constraints and indexes
    console.log('🔗 Adding constraints and indexes...');
    
    const constraintsSQL = `
      -- Add foreign key constraints for files table
      ALTER TABLE public.files 
      ADD CONSTRAINT IF NOT EXISTS fk_files_prospect_id 
      FOREIGN KEY (prospect_id) REFERENCES public.prospects(id) 
      ON DELETE CASCADE ON UPDATE CASCADE;

      ALTER TABLE public.files 
      ADD CONSTRAINT IF NOT EXISTS fk_files_module_id 
      FOREIGN KEY (module_id) REFERENCES public.training_modules(id) 
      ON DELETE SET NULL ON UPDATE CASCADE;

      -- Add check constraints
      ALTER TABLE public.files 
      ADD CONSTRAINT IF NOT EXISTS chk_files_type_valid 
      CHECK (file_type IN ('certificate', 'document', 'training_certificate'));

      ALTER TABLE public.files 
      ADD CONSTRAINT IF NOT EXISTS chk_files_category_valid 
      CHECK (file_category IN ('uploaded', 'system_generated'));

      -- Add indexes for files table
      CREATE INDEX IF NOT EXISTS idx_files_prospect_id ON public.files(prospect_id);
      CREATE INDEX IF NOT EXISTS idx_files_module_id ON public.files(module_id);
      CREATE INDEX IF NOT EXISTS idx_files_type ON public.files(file_type);
      CREATE INDEX IF NOT EXISTS idx_files_category ON public.files(file_category);
      CREATE INDEX IF NOT EXISTS idx_files_verification_code ON public.files(verification_code);
      CREATE INDEX IF NOT EXISTS idx_files_issued_at ON public.files(issued_at);
    `;

    const { error: constraintsError } = await supabase.rpc('exec_sql', { sql: constraintsSQL });
    if (constraintsError) {
      console.error('Error adding constraints:', constraintsError);
      return;
    }
    console.log('✅ Constraints and indexes added successfully');

    // Step 4: Verify migration
    console.log('🔍 Verifying migration...');
    
    const { data: certificatesCount } = await supabase
      .from('certificates')
      .select('*', { count: 'exact', head: true });
    
    const { data: filesCount } = await supabase
      .from('files')
      .select('*', { count: 'exact', head: true });

    console.log(`📊 Certificates table: ${certificatesCount?.length || 0} records`);
    console.log(`📊 Files table: ${filesCount?.length || 0} records`);

    console.log('\n🎉 Files migration completed successfully!');
    console.log('📝 Next steps:');
    console.log('   1. Update application code to use files table');
    console.log('   2. Test the new Files page functionality');
    console.log('   3. Once verified, drop the certificates table');

  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

// Run the migration
applyFilesMigration();
