'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Toaster } from '@/components/ui/toaster';
import { Loader2, Plus, Search, BookOpen, Filter } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ModuleCard } from '@/components/training/ModuleCard';
import { ModuleForm } from '@/components/training/ModuleForm';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

type Module = {
  id: string;
  title: string;
  description: string;
  cover_image_url: string | null;
  duration_minutes: number | null;
  status: 'draft' | 'published' | 'archived';
  required_order: number | null;
  created_at: string;
  updated_at: string;
  created_by: string;
  lessons_count?: number;
};

const sortOptions = [
  { value: 'newest', label: 'Newest' },
  { value: 'oldest', label: 'Oldest' },
  { value: 'title_asc', label: 'Title (A-Z)' },
  { value: 'title_desc', label: 'Title (Z-A)' },
  { value: 'order_asc', label: 'Order (Low-High)' },
];

const statusOptions = [
  { value: 'all', label: 'All' },
  { value: 'draft', label: 'Draft' },
  { value: 'published', label: 'Published' },
  { value: 'archived', label: 'Archived' },
];

export default function TrainingModulesPage() {
  const { toast } = useToast();
  const [modules, setModules] = useState<Module[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('newest');
  const [statusFilter, setStatusFilter] = useState('all');
  
  const [showModuleForm, setShowModuleForm] = useState(false);
  const [currentModuleId, setCurrentModuleId] = useState<string | undefined>();
  
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [moduleToDelete, setModuleToDelete] = useState<string | null>(null);
  
  // Fetch modules on component mount and after changes
  useEffect(() => {
    fetchModules();
  }, [sortBy, statusFilter]);
  
  async function fetchModules() {
    setLoading(true);
    
    try {
      // Build the query
      let query = supabase
        .from('training_modules')
        .select('*, lessons:lessons(id)');
      
      // Apply status filter if not 'all'
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }
      
      // Apply sorting
      switch (sortBy) {
        case 'newest':
          query = query.order('created_at', { ascending: false });
          break;
        case 'oldest':
          query = query.order('created_at', { ascending: true });
          break;
        case 'title_asc':
          query = query.order('title', { ascending: true });
          break;
        case 'title_desc':
          query = query.order('title', { ascending: false });
          break;
        case 'order_asc':
          query = query.order('required_order', { ascending: true, nullsFirst: false });
          break;
        default:
          query = query.order('created_at', { ascending: false });
      }
      
      // Execute the query
      const { data, error } = await query;
      
      if (error) throw error;
      
      // Process the data to get lesson counts
      const processedData = data?.map(module => ({
        ...module,
        lessons_count: module.lessons?.length || 0,
      })) || [];
      
      setModules(processedData);
    } catch (error: any) {
      console.error('Error fetching modules:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to fetch training modules',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }

  const handleAddClick = () => {
    setCurrentModuleId(undefined);
    setShowModuleForm(true);
  };

  const handleEditModule = (id: string) => {
    setCurrentModuleId(id);
    setShowModuleForm(true);
  };

  const handleDeleteModule = (id: string) => {
    setModuleToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!moduleToDelete) return;
    
    setLoading(true);
    
    try {
      // First, delete all associated lessons
      const { error: lessonsError } = await supabase
        .from('lessons')
        .delete()
        .eq('module_id', moduleToDelete);
      
      if (lessonsError) throw lessonsError;
      
      // Then delete the module
      const { error } = await supabase
        .from('training_modules')
        .delete()
        .eq('id', moduleToDelete);
      
      if (error) throw error;
      
      // Success
      toast({
        title: 'Module Deleted',
        description: 'The training module has been deleted successfully.',
      });
      
      // Refresh the modules list
      fetchModules();
    } catch (error: any) {
      console.error('Error deleting module:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete the module',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
      setDeleteDialogOpen(false);
      setModuleToDelete(null);
    }
  };

  const handleManageLessons = (id: string) => {
    // This would navigate to a lessons management page for this module
    window.location.href = `/admin/training-modules/${id}/lessons`;
  };

  // Filter modules based on search query
  const filteredModules = modules.filter(module => 
    module.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    module.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Training Modules</h1>
          <p className="text-muted-foreground">
            Create and manage training modules for prospect onboarding
          </p>
        </div>
        <Button onClick={handleAddClick} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          <span>Add New Module</span>
        </Button>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
        <div className="flex-1 relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search modules..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="flex gap-3 flex-wrap">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center gap-2">
            <BookOpen className="h-4 w-4 text-muted-foreground" />
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                {sortOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      
      <div>
        <Badge variant="outline" className="mb-4">
          {filteredModules.length} {filteredModules.length === 1 ? 'module' : 'modules'} found
        </Badge>
      </div>
      
      {loading ? (
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="mt-4 text-muted-foreground">Loading training modules...</p>
        </div>
      ) : filteredModules.length === 0 ? (
        <div className="flex flex-col items-center justify-center border rounded-lg py-12">
          <BookOpen className="h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">No training modules found</h3>
          <p className="mt-1 text-muted-foreground">
            {searchQuery 
              ? 'Try adjusting your search or filters'
              : 'Get started by creating your first training module'
            }
          </p>
          <Button onClick={handleAddClick} className="mt-4">
            <Plus className="mr-2 h-4 w-4" />
            Add New Module
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredModules.map((module) => (
            <ModuleCard
              key={module.id}
              id={module.id}
              title={module.title}
              description={module.description}
              coverImageUrl={module.cover_image_url || undefined}
              durationMinutes={module.duration_minutes || undefined}
              status={module.status}
              requiredOrder={module.required_order || undefined}
              lessonCount={module.lessons_count || 0}
              onEdit={handleEditModule}
              onDelete={handleDeleteModule}
              onManageLessons={handleManageLessons}
            />
          ))}
        </div>
      )}
      
      {/* Module Form */}
      <ModuleForm
        isOpen={showModuleForm}
        onClose={() => setShowModuleForm(false)}
        onSuccess={fetchModules}
        moduleId={currentModuleId}
      />
      
      {/* Delete Confirmation */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the training module and all its lessons.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      <Toaster />
    </div>
  );
} 